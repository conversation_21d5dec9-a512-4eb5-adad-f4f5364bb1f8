# Schema Cache Warmup Initializer
# Đ<PERSON>m bảo schema cache được load đúng cách trong production environment

Rails.application.config.after_initialize do
  # Chỉ chạy trong production và khi không phải là rake task
  if Rails.env.production? && !defined?(Rake)
    Rails.logger.info "Warming up schema cache for critical tables..."
    
    begin
      # Danh sách các table quan trọng cần preload schema
      critical_tables = %w[
        t_aggregate_ranking_points
        t_aggregate_offers
        t_popular_rankings
        master_makes
        master_models
      ]
      
      # Preload schema cho các table quan trọng
      critical_tables.each do |table|
        begin
          ActiveRecord::Base.connection.columns(table)
          Rails.logger.debug "Schema preloaded for #{table}"
        rescue => e
          Rails.logger.warn "Failed to preload schema for #{table}: #{e.message}"
        end
      end
      
      # Đảm bảo column information được load cho các model quan trọng
      critical_models = [
        TAggregateRankingPoint,
        TAggregateOffer,
        TPopularRanking,
        MasterMake,
        MasterModel
      ]
      
      critical_models.each do |model|
        begin
          # Force load column information
          model.columns_hash
          Rails.logger.debug "Column information loaded for #{model.name}"
        rescue => e
          Rails.logger.warn "Failed to load column information for #{model.name}: #{e.message}"
        end
      end
      
      Rails.logger.info "Schema cache warmup completed successfully"
      
    rescue => e
      Rails.logger.error "Schema cache warmup failed: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
    end
  end
end

# Thêm một method để manual refresh schema cache nếu cần
module SchemaCacheHelper
  def self.refresh_cache!
    Rails.logger.info "Manually refreshing schema cache..."
    
    begin
      # Clear existing cache
      ActiveRecord::Base.connection.schema_cache.clear!
      
      # Reset column information cho các model quan trọng
      [
        TAggregateRankingPoint,
        TAggregateOffer, 
        TPopularRanking,
        MasterMake,
        MasterModel
      ].each do |model|
        model.reset_column_information
      end
      
      # Preload lại schema
      %w[
        t_aggregate_ranking_points
        t_aggregate_offers
        t_popular_rankings
        master_makes
        master_models
      ].each do |table|
        ActiveRecord::Base.connection.columns(table)
      end
      
      Rails.logger.info "Manual schema cache refresh completed"
      true
    rescue => e
      Rails.logger.error "Manual schema cache refresh failed: #{e.message}"
      false
    end
  end
end

# Thêm vào Rails console để dễ debug
if defined?(Rails::Console)
  Rails.logger.info "Schema cache helper available in console. Use SchemaCacheHelper.refresh_cache! to manually refresh."
end
