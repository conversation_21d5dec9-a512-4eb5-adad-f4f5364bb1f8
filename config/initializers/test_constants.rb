MIN_INT = -32768
MAX_INT = 32767
TEST_SP_USER_AGENT = 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148'.freeze
RSPEC_MAKER_DATA = [{ name: 'Make 1', svg_name: 'maker-1', stock: 92, url: 'used_car/make 1/all/', sp_url: 'used_car/make 1/all/',
                      top_models: [{ name: 'All Models', stock: 92, url: 'used_car/make 1/all/' }] },
                    { name: 'Make 2', svg_name: 'maker-2', stock: 4, url: 'used_car/make 2/all/', sp_url: 'used_car/make 2/all/',
                      top_models: [{ name: 'All Models', stock: 4, url: 'used_car/make 2/all/' }] },
                    { name: 'Make 3', svg_name: 'maker-3', stock: 17, url: 'used_car/make 3/all/', sp_url: 'used_car/make 3/all/',
                      top_models: [{ name: 'All Models', stock: 17, url: 'used_car/make 3/all/' }] }].freeze
RSPEC_BODY_STYLE_DATA = [{ svg_name: 'icon-name', photo: nil, name: 'Name 1', stock: 188, url: 'used_car/all/all/?bsty=1',
                           url_with_float_id: 'used_car/all/all/?bsty=1.0', sp_url: 'used_car/all/all/?bsty=1' }].freeze
RSPEC_CATEGORY_DATA = [{ name: 'Left Hand Drive', svg_name: 'icon-handle', key_get_stock_count: 'SteeringID13', stock: 41, svg_width: 32,
                         svg_height: 32, pc_path: 'used_car/all/all/?st=13', id: 1 },
                       { name: 'Manual', svg_name: 'icon-manual', key_get_stock_count: 'TransmissionID6', stock: 94, svg_width: 32, svg_height: 32,
                         pc_path: 'used_car/all/all/?tmns=6', id: 2 },
                       { name: 'Diesel', svg_name: 'icon-oilpot', key_get_stock_count: 'FuelTypeID17', stock: 125, svg_width: 32, svg_height: 32,
                         pc_path: 'used_car/all/all/?fues=17', id: 3 },
                       { name: '4WD', svg_name: 'icon-tire', key_get_stock_count: 'DriveTypeID10', stock: 69, svg_width: 28, svg_height: 28,
                         pc_path: 'used_car/all/all/?dr=10', id: 4 },
                       { name: 'No Accidents', svg_name: 'icon-no-accidentscar', key_get_stock_count: 'IsAccident:0 AND IsNoAccidentsHistory:True',
                         stock: 0, svg_width: 28, svg_height: 28, pc_path: 'used_car/all/all/?ac=2', id: 5 },
                       { name: 'Not Repaired', svg_name: 'icon-not-repaired', key_get_stock_count: 'IsAccident:1', stock: 0, svg_width: 28,
                         svg_height: 28, pc_path: 'used_car/all/all/?ac=1', id: 6 }].freeze
