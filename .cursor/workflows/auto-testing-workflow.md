# Testing Workflow Guide
### Quy trình test và báo cáo kết quả

## Tổng quan quy trình
1. Đọc issue từ GitHub để hiểu requirement và test case
2. Thực hiện test và ghi lại kết quả
3. <PERSON><PERSON> <PERSON> comments và cập nhật lên <PERSON> (nếu được phép)

## Cách sử dụng
- "Thực hiện bước 1 của auto-testing-workflow cho issue #XXX"
- "Tiếp tục với bước X của auto-testing-workflow cho issue #XXX"
- "Thực hiện bước X của auto-testing-workflow cho issue #XXX"

## Chi tiết các bước

### Bước 1: Đọc issue từ GitHub
Khi được yêu cầu, tôi sẽ:
- Truy cập issue #XXX trên GitHub thông qua MCP github
- Phân tích requirement
- Tì<PERSON> comment có chứa test case (bỏ qua comment đã bị hidden)
- Tó<PERSON> tắt các test case cần thực hiện
- **PHẢI ĐẢM BẢO** đ<PERSON><PERSON> chính xác các test case từ GitHub comment
- Xác định test case nào có thể thực hiện với MCP Playwright
- Bước 1 sẽ **TẠO RA DANH SÁCH CHÍNH THỨC** các test case sẽ được thực hiện ở bước 2
- **CHỈ CÁC TEST CASE TRONG DANH SÁCH NÀY** mới được thực hiện ở bước 2
- **BẮT BUỘC** lưu danh sách test case đã xác định vào file `.cursor/issue_{issue_number}_memo.md` với định dạng sau:
  ```markdown
  # Test Cases for Issue #{issue_number}
  
  ## Danh sách test case
  
  | ID | Mô tả | Bước thực hiện | Kết quả mong đợi | Có thể test với Playwright |
  |---|-------|----------------|------------------|---------------------------|
  | TC-XXX | Mô tả test case | Các bước thực hiện | Kết quả mong đợi | Có/Không |
  | TC-XXX | ... | ... | ... | ... |
  ```
- Sử dụng `edit_file` để tạo file memo này

### Bước 2: Thực hiện test
Khi được yêu cầu, tôi sẽ:
- **ĐẦU TIÊN** phải đọc danh sách test case đã lưu trữ ở bước 1 bằng cách sử dụng `read_file` để đọc file `.cursor/issue_{issue_number}_memo.md`
- **TUYỆT ĐỐI CHỈ** thực hiện test theo các test case đã ghi trong file memo
- **KHÔNG ĐƯỢC PHÉP** tạo thêm bất kỳ test case mới nào hoặc thay đổi test case
- **BẮT BUỘC** bắt đầu bằng việc liệt kê lại **CHÍNH XÁC** danh sách test case từ file memo:
  ```
  Danh sách test case sẽ thực hiện (đã xác định từ bước 1 và lưu trong file memo):
  1. [ID] - [Mô tả test case]
  2. [ID] - [Mô tả test case]
  ...
  ```
- **THỰC HIỆN TUẦN TỰ** từng test case trong danh sách trên, tiếp tục thực hiện các test case khác kể cả khi có test case fail
- **BỎ QUA** các test case yêu cầu sử dụng Firefox hoặc Safari
- **TUÂN THỦ CHÍNH XÁC** các bước thực hiện trong test case
- **CHỈ** phản hồi ngắn gọn khi thực hiện từng test case:
  ```
  Đang test: [ID] - [Mô tả test case]
  Kết quả: [Pass/Fail]
  ```
- **TUYỆT ĐỐI KHÔNG** tự động tạo comment trên GitHub nếu chưa được cho phép
- Ghi lại kết quả test chi tiết **NHƯNG CHƯA CẬP NHẬT** lên GitHub comment
- Chuẩn bị dữ liệu cho bảng test case (kết quả và comments)
- **BẮT BUỘC** cập nhật file memo `.cursor/issue_{issue_number}_memo.md` để thêm kết quả test:
  ```markdown
  # Test Cases for Issue #{issue_number}
  
  ## Danh sách test case
  
  | ID | Mô tả | Bước thực hiện | Kết quả mong đợi | Có thể test với Playwright | Kết quả test | Ghi chú |
  |---|-------|----------------|------------------|---------------------------|-------------|---------|
  | TC-XXX | Mô tả test case | Các bước thực hiện | Kết quả mong đợi | Có/Không | Pass/Fail | Ghi chú về kết quả test (nếu có) |
  | TC-XXX | ... | ... | ... | ... | ... | ... |
  ```
- Nếu cần lưu evidence hoặc kết quả test chi tiết, lưu vào file `.cursor/issue_{issue_number}_test_results.md`

### Bước 3: Bổ sung comments và cập nhật GitHub
**YÊU CẦU:** Phải được user cho phép mới được thực hiện bước này

Khi được yêu cầu, tôi sẽ:
- **ĐẦU TIÊN** đọc thông tin test case và kết quả đã lưu trữ trong file `.cursor/issue_{issue_number}_memo.md`
- Bổ sung comments, ghi chú vấn đề phát hiện và đề xuất cải tiến
- Tạo báo cáo kết quả test theo format:

| ID | Mô tả | Bước thực hiện | Kết quả mong đợi | Kết quả test | Ghi chú |
|---|-------|----------------|------------------|--------------|----------|
| TC-XXX | [Mô tả] | [Các bước] | [Kết quả mong đợi] | Pass/Fail | - Với test case không thể dùng Playwright: Ghi rõ lý do<br>- Với test case thực hiện được: Ghi thêm phát hiện quan trọng nếu có |

- **PHẢI ĐẢM BẢO** báo cáo chỉ bao gồm các test case đã được lưu trữ trong file memo
- **HỎI USER** có muốn cập nhật kết quả lên GitHub comment không
- **CHỈ TẠO COMMENT MỚI** để báo cáo kết quả test **KHI ĐƯỢC PHÉP RÕ RÀNG**
- **SAU KHI HOÀN THÀNH** việc comment trên GitHub hoặc khi được yêu cầu, thực hiện xóa **TẤT CẢ** các file tạm đã tạo trong quá trình test:
  - Xóa file `.cursor/issue_{issue_number}_memo.md` 
  - Xóa file `.cursor/issue_{issue_number}_test_results.md` (nếu có)
  - Xóa bất kỳ file tạm nào khác có pattern `.cursor/issue_{issue_number}_*.md`
  - Sử dụng lệnh `delete_file` để xóa từng file
- **BÁO CÁO** với user sau khi đã xóa toàn bộ các file tạm

## Lưu ý quan trọng
- Test case đã được tạo từ workflow [create-test-case-workflow](:.cursor/workflows/create-test-case-workflow.md) và đăng tải lên GitHub comment
- **BẮT BUỘC** phải đọc test case từ GitHub comment, không tự tạo test case mới
- **CANH CHỈNH TUYỆT ĐỐI**: Test case được thực hiện ở bước 2 **PHẢI GIỐNG HỆT** danh sách test case đã xác định ở bước 1 và lưu trong file memo
- **MCP Playwright chỉ hỗ trợ Chrome**, các test case yêu cầu Firefox hoặc Safari sẽ được **BỎ QUA**
- Đối với test case không thể thực hiện với Playwright, **PHẢI GHI RÕ** trong cột Ghi chú
- Khi không chắc chắn về bất kỳ thông tin nào, **PHẢI HỎI USER** trước khi tiếp tục
- **TUYỆT ĐỐI KHÔNG** tự động tạo comment trên GitHub nếu chưa được user cho phép
- **BỊ CẤM NGHIÊM NGẶT** việc thực hiện test case không nằm trong danh sách đã xác định ở bước 1 và lưu trong file memo
- **LUÔN SỬ DỤNG FILE MEMO** để đảm bảo tính nhất quán giữa các bước trong workflow
- **QUẢN LÝ FILE TẠM**: Tất cả file tạm phải được xóa sau khi hoàn thành quy trình hoặc khi user yêu cầu
