# Debug Workflow Guide
### Quy trình debug và tìm lỗi

## Tổng quan quy trình

Workflow này bao gồm 5 bước chính:
1. <PERSON><PERSON><PERSON> định và tái tạo lỗi
2. <PERSON><PERSON> tích codebase để thu hẹp phạm vi
3. <PERSON><PERSON> dụng kỹ thuật debug để tìm nguyên nhân
4. Triển khai và kiểm tra giải pháp
5. Tài liệu hóa vấn đề và giải pháp

## Lưu knowledge

Trong mỗi bước:
1. Thu thập thông tin (knowledge) từ bước đó
2. Lưu knowledge vào file `.cursor/knowledges/debugs/{target_code}_{current_date}.md`, hãy lưu đúng vào folder .cursor của dự án
3. Xác nhận với user về tính chính xác của knowledge
4. Cho phép user chỉnh sửa knowledge nếu cần
5. Sử dụng knowledge đã xác nhận cho các bư<PERSON>c tiếp theo
6. <PERSON><PERSON><PERSON> cáo với user để user biết được là kiểm tra đã hoàn tất

## Cách sử dụng

1. Nhắc đến workflow này khi muốn bắt đầu quy trình debug: "Thực hiện bước 1 của debug-workflow cho [mô tả lỗi]"
2. Để chuyển sang bước tiếp theo: "Tiếp tục với bước X của debug-workflow cho [mô tả lỗi]"
3. Để thực hiện một bước cụ thể: "Thực hiện bước X của debug-workflow cho [mô tả lỗi]"

## Chi tiết các bước

### Bước 1: Xác định và tái tạo lỗi
**Lệnh mẫu:** "Thực hiện bước 1 của debug-workflow cho [mô tả lỗi]"

Khi được yêu cầu, tôi sẽ:
- Yêu cầu mô tả chi tiết về lỗi (nếu chưa có)
- Xác định các bước để tái tạo lỗi
- Tạo môi trường để lỗi xuất hiện một cách nhất quán
- Ghi lại các triệu chứng chính xác của lỗi
- Kiểm tra logs và thông báo lỗi liên quan
- Xác nhận lại với user về hiểu biết về lỗi trước khi chuyển sang bước tiếp theo

### Bước 2: Phân tích codebase để thu hẹp phạm vi
**Lệnh mẫu:** "Tiếp tục với bước 2 của debug-workflow cho [mô tả lỗi]"

Khi được yêu cầu, tôi sẽ:
- Xác định các file và module có thể liên quan đến lỗi
- Phân tích luồng thực thi để tìm các điểm có thể gây lỗi
- Kiểm tra các thay đổi gần đây (nếu có) trong code
- Sử dụng kỹ thuật chia nhỏ vấn đề để thu hẹp phạm vi
- Tạo một danh sách các nghi vấn và giả thuyết
- Xác nhận lại với user về phạm vi lỗi trước khi chuyển sang bước tiếp theo

### Bước 3: Áp dụng kỹ thuật debug để tìm nguyên nhân
**Lệnh mẫu:** "Tiếp tục với bước 3 của debug-workflow cho [mô tả lỗi]"

Khi được yêu cầu, tôi sẽ:
- Áp dụng các kỹ thuật debug từ `.cursor/rules/debug-rules/debug.mdc`
- Sử dụng console logging/debugging để theo dõi luồng dữ liệu
- Đề xuất đặt breakpoints tại các vị trí quan trọng
- Kiểm tra các giả thuyết đã đưa ra
- Phân tích dữ liệu đầu vào và đầu ra tại các điểm quan trọng
- Xác định nguyên nhân gốc rễ của lỗi
- Xác nhận lại với user về nguyên nhân lỗi trước khi chuyển sang bước tiếp theo

### Bước 4: Triển khai và kiểm tra giải pháp
**Lệnh mẫu:** "Tiếp tục với bước 4 của debug-workflow cho [mô tả lỗi]"

Khi được yêu cầu, tôi sẽ:
- Đề xuất các giải pháp khả thi dựa trên nguyên nhân đã xác định
- Triển khai giải pháp được chọn
- Kiểm tra giải pháp trên môi trường có thể tái tạo lỗi
- Đánh giá giải pháp về tính hiệu quả và tác động phụ
- Tinh chỉnh giải pháp nếu cần
- Cung cấp các bài test để đảm bảo lỗi không tái xuất hiện
- Xác nhận lại với user về hiệu quả của giải pháp trước khi chuyển sang bước tiếp theo

### Bước 5: Tài liệu hóa vấn đề và giải pháp
**Lệnh mẫu:** "Tiếp tục với bước 5 của debug-workflow cho [mô tả lỗi]"

Khi được yêu cầu, tôi sẽ:
- Tạo báo cáo lỗi đầy đủ theo mẫu từ `.cursor/rules/debug-rules/debug.mdc`
- Mô tả lỗi, nguyên nhân và giải pháp một cách chi tiết
- Liệt kê các bài học kinh nghiệm từ việc debug
- Đề xuất các biện pháp phòng ngừa tương tự trong tương lai
- Cung cấp tài liệu tham khảo liên quan đến lỗi và giải pháp

## Lưu ý quan trọng
- Mỗi bước trong workflow sẽ chỉ được thực hiện khi user yêu cầu rõ ràng
- Tại mỗi bước, luôn xác nhận với user trước khi chuyển sang bước tiếp theo
- Không cần phải liên kết với issue từ GitHub nếu không có
- Nếu không có issue, không cần sử dụng MCP GitHub
- Tuân thủ quy tắc debug từ `.cursor/rules/debug-rules/debug.mdc`
- Tập trung vào hiểu nguyên nhân gốc rễ trước khi triển khai giải pháp
- Ưu tiên các giải pháp đơn giản và ít rủi ro nhất
- Nếu có vấn đề chưa rõ ràng hoặc cần thêm thông tin, luôn hỏi user trước khi tiếp tục thực hiện
