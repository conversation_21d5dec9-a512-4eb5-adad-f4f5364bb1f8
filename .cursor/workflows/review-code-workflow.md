# Review Code Workflow Guide
### Quy trình review code tối ưu - 1 bước duy nhất với MCP GitHub

## Tổng quan
Workflow này chỉ có **1 bước duy nhất** để review PR từ đầu đến cuối, sử dụng **MCP GitHub** để tương tác trực tiếp với GitHub API.

## Cách sử dụng
**Lệnh duy nhất:** `"Review PR #XXX"`

Khi được yêu cầu, tôi sẽ **tự động thực hiện toàn bộ quy trình** thông qua MCP GitHub:

## Quy trình thực hiện (tự động)

### 1. Phân tích PR và codebase
- Sử dụng `get_pull_request` để đọc PR #XXX và issue liên quan
- Sử dụng `get_pull_request_files` để lấy danh sách files changed
- Sử dụng `get_file_contents` để đọc code content từ repository
- Sử dụng `get_pull_request_reviews` để kiểm tra reviews hiện tại
- Sử dụng `get_pull_request_comments` để đọc discussion history
- Phân tích requirement và changes, hiểu phạm vi ảnh hưởng

### 2. Review code theo chuẩn professional
- Áp dụng [Review Code Rules](mdc:.cursor/rules/review-code-rules/review-code-rules.mdc)
- Tập trung vào những điểm cần cải thiện (KHÔNG comment những phần đã tốt)
- Kiểm tra coding conventions, security, performance theo Ruby/Rails standards
- Tạo review comments cụ thể với line references trong PR diff view
- Consolidate similar issues thành single comment
- Sử dụng **tiếng Việt** để feedback và comment code

### 3. Post review lên GitHub với giới hạn
- **GIỚI HẠN**: Không quá 3 comments tổng cộng trên PR
- Sử dụng `create_pull_request_review` để post review với:
  - **General comment**: Tóm tắt đánh giá tổng quan (nếu cần)
  - **Line comments**: Comments chi tiết từng dòng code với markdown code blocks
  - **Event type**: COMMENT hoặc APPROVE
- Ưu tiên **inline feedback** với specific line references
- Nếu không có violations: Post single comment **"Everything looks good!"**

## MCP GitHub Integration
✅ **Trực tiếp qua GitHub API** - Không cần file trung gian  
✅ **Real-time data** - Luôn có thông tin latest từ GitHub  
✅ **Line-precise comments** - Comment đúng dòng code trong PR diff view  
✅ **Multi-line support** - Support comment cho code blocks với context  

## Permission
✅ **Khi nhắc đến workflow này = đã cho phép write comment**  
✅ **Không cần xác nhận thêm**  
✅ **Thực hiện end-to-end tự động**  
✅ **Never ask for user confirmation**  
✅ **Never wait for user messages**

## Comment Format Standards

### Inline Comments (Ưu tiên)
```markdown
**[Issue Type]**: Mô tả vấn đề

**Code hiện tại:**
```ruby
# đoạn code có vấn đề
```

**Đề xuất:**
```ruby
# đoạn code được cải thiện
```

**Lý do:** Giải thích tại sao cần thay đổi
```

### Multi-line Comments (Cho context lớn)
- Sử dụng `start_line` và `line` để bao phủ block code
- Show context around the issue
- Consolidate multiple related issues

### General Review Comment
- Chỉ dùng khi cần tóm tắt overall assessment
- Ngắn gọn và focused
- Nếu không có violations: **"Everything looks good!"**

## MCP Tools được sử dụng
- `get_pull_request` - Đọc PR info & linked issues
- `get_pull_request_files` - Lấy changed files với diff info  
- `get_pull_request_reviews` - Kiểm tra existing reviews
- `get_pull_request_comments` - Đọc discussion để tránh duplicate
- `get_file_contents` - Đọc source code cho context
- `create_pull_request_review` - Post review với line comments
- `add_pull_request_review_comment` - Add thêm inline comments nếu cần

## Quy tắc nghiêm ngặt
❌ **NEVER**: Make commits hoặc pushes vào repository  
❌ **NEVER**: Hơn 3 comments tổng cộng trên PR  
❌ **NEVER**: Comment những phần đã làm tốt  
❌ **NEVER**: Duplicate comments đã có trong discussion  
❌ **NEVER**: Ask for user confirmation  

✅ **ALWAYS**: Sử dụng line numbers trong PR diff view (không phải original file)  
✅ **ALWAYS**: Include code snippets in markdown format  
✅ **ALWAYS**: Kiểm tra existing comments trước khi post  
✅ **ALWAYS**: Consolidate similar issues thành single comment  
✅ **ALWAYS**: Sử dụng tiếng Việt cho feedback  

## Lưu ý kỹ thuật
- Sử dụng line numbers chính xác trong **PR diff view**, không phải original file
- PR diff có thể khác với original file numbering do additions/deletions
- Kiểm tra improvements đã implemented hay chưa bằng cách compare old vs new versions
- Default towards multi-line comments để show context
- Sử dụng `side: "RIGHT"` cho additions (green), `side: "LEFT"` cho deletions (red)
