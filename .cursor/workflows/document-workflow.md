# Document Workflow Guide
### Quy trình lập tài liệu kỹ thuật và hướng dẫn sử dụng

## Tổng quan quy trình

Workflow này bao gồm 5 bướ<PERSON> chính:
1. Đ<PERSON>c issue/PR từ GitHub để hiểu chức năng cần tài liệu
2. Đọc codebase để hiểu cách hoạt động của chức năng
3. Tạo sequence diagram mô tả luồng hoạt động
4. Viết tài liệu kỹ thuật chi tiết
5. Review và cập nhật tài liệu

## Kiểm tra MCP và lưu knowledge

Trong mỗi bước:
1. Tự động kiểm tra MCP cần thiết (Github)
2. Thu thập thông tin (knowledge) từ bước đó
3. Lưu knowledge vào file `.cursor/knowledges/documents/{feature_id}_{current_date}.md`, hãy lưu đúng vào folder .cursor của dự án
4. <PERSON><PERSON><PERSON> nhận với user về tính chính xác của knowledge
5. <PERSON> phép user chỉnh sửa knowledge nếu cần
6. Sử dụng knowledge đã xác nhận cho các bước tiếp theo
7. Báo cáo với user để user biết được là kiểm tra đã hoàn tất

## Cách sử dụng

1. Nhắc đến workflow này khi muốn bắt đầu quy trình viết tài liệu: "Thực hiện bước 1 của document-workflow cho chức năng XXX"
2. Để chuyển sang bước tiếp theo: "Tiếp tục với bước X của document-workflow cho chức năng XXX"
3. Để thực hiện một bước cụ thể: "Thực hiện bước X của document-workflow cho chức năng XXX"

## Chi tiết các bước

### Bước 1: Đọc issue/PR từ GitHub
**Lệnh mẫu:** "Thực hiện bước 1 của document-workflow cho chức năng XXX"

Khi được yêu cầu, tôi sẽ:
- Truy cập issue/PR liên quan trên GitHub thông qua MCP github
- Phân tích requirement và mục đích của chức năng
- Xác định đối tượng sử dụng và phạm vi
- Tóm tắt các điểm chính về chức năng cần làm tài liệu
- Xác nhận lại với user về tính đầy đủ của requirement trước khi chuyển sang bước tiếp theo

### Bước 2: Đọc codebase 
**Lệnh mẫu:** "Tiếp tục với bước 2 của document-workflow cho chức năng XXX"

Khi được yêu cầu, tôi sẽ:
- Xác định các file/module liên quan đến chức năng
- Phân tích code để hiểu cơ chế hoạt động
- Xác định các thành phần tham gia vào chức năng
- Hiểu rõ logic và luồng xử lý
- Xác nhận lại với user về phạm vi và cơ chế hoạt động trước khi chuyển sang bước tiếp theo

### Bước 3: Tạo sequence diagram
**Lệnh mẫu:** "Tiếp tục với bước 3 của document-workflow cho chức năng XXX"

Khi được yêu cầu, tôi sẽ:
- Xác định các actor và component tham gia
- Vẽ luồng tương tác giữa các thành phần theo cú pháp Mermaid
- Mô tả chi tiết các bước xử lý chính
- Thể hiện rõ các điều kiện và vòng lặp
- Cập nhật diagram dựa trên feedback từ user
- Xác nhận lại tính chính xác của diagram trước khi chuyển sang bước tiếp theo

### Bước 4: Viết tài liệu kỹ thuật
**Lệnh mẫu:** "Tiếp tục với bước 4 của document-workflow cho chức năng XXX"

Khi được yêu cầu, tôi sẽ:
- Sử dụng template từ `.cursor/templates/document-template.md`
- Điền thông tin chức năng dựa trên knowledge đã thu thập
- Mô tả chi tiết từng bước trong sequence diagram
- Thêm thông tin về API/interface liên quan
- Mô tả các trường hợp đặc biệt và ràng buộc
- Tuân thủ quy tắc từ `.cursor/rules/document-rules/document-rule.mdc`
- Xác nhận lại nội dung tài liệu với user trước khi chuyển sang bước tiếp theo

### Bước 5: Review và cập nhật
**Lệnh mẫu:** "Tiếp tục với bước 5 của document-workflow cho chức năng XXX"

Khi được yêu cầu, tôi sẽ:
- Kiểm tra tính chính xác và đầy đủ của tài liệu
- Kiểm tra việc tuân thủ quy tắc về cấu trúc và định dạng
- Đảm bảo sequence diagram chính xác và rõ ràng
- Cập nhật tài liệu theo feedback từ user
- Lưu phiên bản cuối cùng vào thư mục tài liệu của dự án

## Lưu ý quan trọng
- Mỗi bước trong workflow sẽ chỉ được thực hiện khi user yêu cầu rõ ràng
- Tại mỗi bước, luôn xác nhận với user trước khi chuyển sang bước tiếp theo
- Đảm bảo tài liệu tuân thủ các quy tắc đã định nghĩa trong `document-rules/document-rule.mdc`
- Tài liệu phải cung cấp đủ thông tin để người đọc hiểu được chức năng mà không cần đọc code
- Sequence diagram phải phản ánh chính xác luồng xử lý của chức năng
- Nếu có vấn đề chưa rõ ràng hoặc cần thêm thông tin, luôn hỏi user trước khi tiếp tục thực hiện
