# Phân Tích Yêu Cầu

## M<PERSON><PERSON> đích
Chuyển đổi các GitHub issue, yê<PERSON> cầ<PERSON>, hoặc request của stakeholder thành phân tích yêu cầu có cấu trúc với hai kết quả đầu ra cần thiết:
1. **Requirement Summary** - Tổng quan rõ ràng về yêu cầu
2. **Acceptance Criteria** - User story theo cú pháp <PERSON>

**Nguyên tắc chính:**
- <PERSON><PERSON><PERSON> bảo tính rõ ràng và đầy đủ cho team phát triển
- <PERSON><PERSON><PERSON> vụ cho quá trình coding và testing, đảm bảo cả team có cùng góc nh<PERSON>n, tr<PERSON>h bất cứ hiểu nhầm nào.

## Workflow chính

### 1. Phân tích đầu vào (Thực hiện các hành động theo đúng thứ tự, không bỏ qua hành động nào)
- Với url issue mà user gửi, h<PERSON><PERSON> sử dụng`get_issue` của **github-mcp-server** để đọc GitHub issue và comment liên quan
- Dựa vào nội dung issue, hãy phân tich yêu cầu và tạo một file `issue_{issue_id}_requirement.md` để lưu phần phân tích đó
- Hiểu rõ yêu cầu bằng cách hỏi qua **Devin MCP Server**:
  - owner/repo: ZIGExN/tcv-web-v2
  - Sử dụng `ask_question`: Sử dụng nội dung từ file `issue_{issue_id}_requirement.md` để đặt câu hỏi để đưa ra hướng thực hiện, các file code liên quan cần chỉnh sửa.
    - *Mục đích: Đảm bảo hiểu rõ về tính năng/yêu cầu sắp thực hiện, kiểm tra có các tính năng tương tự đã được thực hiện trong hệ thống chưa. Cuối cùng đưa ra kết quả phân tích yêu cầu chính xác.*
- Review các issue được tham chiếu hoặc tài liệu liên quan
- Sau khi có câu trả lời từ Devin MCP Server, tiến hành đọc codebase dựa theo output nhận được, tìm thêm các file code liên quan nếu còn để hiểu thêm về yêu cầu và tìm những thông tin liên quan hỗ trợ cho việc design coding, tạo testcase

### 2. Tạo kết quả đầu ra cuối
- Dựa vào kết quả của bước 1, tổng hợp toàn bộ thông tin thành 1 tài liệu hoàn chỉnh
- Tạo file markdown lưu kết quả các mục:
  - **Requirement Summary**
  - **Acceptance Criteria**
- Tuân thủ `Các quy tắc cần tuân theo`

### 3. Kiểm tra tính rõ ràng của yêu cầu
- Dựa vào tất cả thông tin yêu cầu, code, tìm điểm conflict, mơ hồ, cần làm rõ
- Xác định các rủi ro có thể gặp
- Đặt câu hỏi, nếu không có câu hỏi nào thì tự động tiếp tục

### 4. Comment lên GitHub
- Comment kết quả phân tích lên GitHub issue/PR tương ứng
- Đảm bảo format rõ ràng và dễ đọc cho team


## Các quy tắc cần tuân theo

### Requirement Summary
- Cần liệt kê những phần yêu cầu cần thực hiện
- Không tập trung vào bối cảnh hoặc các thông tin bổ sung khác
- Ngắn gọn, đủ ý dễ hiểu cho cả dev, tester
- Tóm tắt ngắn gọn quá trình implement code
- Xác định các url liên quan

### Tiêu chuẩn Gherkin
- Cần đủ ý, ngắn gọn, không được dài dòng nhiều step rườm rà
- Theo đúng template
- Có 1-2 ví dụ nếu có thể
- Không cần phân biệt trình duyệt (browser)
- Mỗi scenario đứng riêng biệt
- Sử dụng thuật ngữ thống nhất trong toàn bộ
- Loại bỏ những step không cần thiết
- Kết quả đầu ra phải bằng tiếng Anh

### Template cấu trúc
```gherkin
Feature: [Mô tả business feature]

  As a [vai trò user]
  I want to [khả năng]
  So that [giá trị business]

  Scenario [số thứ tự]: [Tên scenario cụ thể]
    Given [trạng thái/ngữ cảnh ban đầu]
    When [hành động/trigger]
    Then [kết quả mong đợi]
    And [xác minh bổ sung nếu cần]
```

## Ví dụ

### Issue gốc:
"As a user, I want to register and login to the system so that I can access my personal account.
- Users should be able to register with email and password
- Email must be unique and valid format
- Password must be at least 8 characters
- Users can login with registered credentials
- Show appropriate error messages for invalid attempts"

### Định dạng kết quả đầu ra:

#### Requirement Summary
Tạo mới chức năng user authentication. Các phần cần implement:
1. Chức năng registration và login.
2. Validate tính duy nhất/định dạng email, thực thi độ mạnh password, và cung cấp feedback cho user về các attempt authentication.

#### Acceptance Criteria
```gherkin
Feature: User registration and login

  As a user
  I want to register and login to the system
  So that I can access my personal account

  Scenario 1: Successful user registration
    Given I am on the registration page
    When I enter a valid email and password
    Then I should see registration successful message
    And I should be able to login with my credentials

  Scenario 2: Registration with invalid email
    Given I am on the registration page
    When I enter an invalid email format
    Then I should see an error message about invalid email

  Scenario 3: Successful user login
    Given I have a registered account
    When I login with correct credentials
    Then I should be logged in successfully

  Scenario 4: Login with incorrect credentials
    Given I have a registered account
    When I login with incorrect credentials
    Then I should see an error message about invalid credentials
```

## Lưu ý quan trọng
- Cần tuân thủ chặt chẽ các bước trong workflow, đọc toàn bộ nội dung, hiểu rõ rồi mới bắt đầu
- Lưu ý về các quy tắc cần tuân theo
