# Performance Optimization Workflow Guide
### Quy trình tối ưu hiệu suất và nâng cao chất lượng code

## Tổng quan quy trình

Workflow này bao gồm 5 bước chính:
1. <PERSON><PERSON><PERSON> định vấn đề hiệu suất và thiết lập mục tiêu
2. <PERSON><PERSON> lường hiệu suất hiện tại và xác định điểm nghẽn
3. <PERSON><PERSON> tích nguyên nhân và đề xuất giải pháp
4. Triển khai tối ưu và kiểm tra kết quả
5. Tài liệu hóa cải tiến và bài học kinh nghiệm

## Lưu knowledge

Trong mỗi bước:
1. Thu thập thông tin (knowledge) từ bước đó
2. Lưu knowledge vào file `.cursor/knowledges/performance/{target_code}_{current_date}.md`, hãy lưu đúng vào folder .cursor của dự án
3. <PERSON><PERSON><PERSON> nhận với user về tính chính xác của knowledge
4. Cho phép user chỉnh sửa knowledge nếu cần
5. <PERSON><PERSON> dụng knowledge đã xác nhận cho các bước tiếp theo
6. Báo cáo với user để user biết được là kiểm tra đã hoàn tất

## Cách sử dụng

1. Nhắc đến workflow này khi muốn bắt đầu quy trình tối ưu: "Thực hiện bước 1 của performance-optimization cho [tên component/module]"
2. Để chuyển sang bước tiếp theo: "Tiếp tục với bước X của performance-optimization cho [tên component/module]"
3. Để thực hiện một bước cụ thể: "Thực hiện bước X của performance-optimization cho [tên component/module]"

## Chi tiết các bước

### Bước 1: Xác định vấn đề hiệu suất và thiết lập mục tiêu
**Lệnh mẫu:** "Thực hiện bước 1 của performance-optimization cho [tên component/module]"

Khi được yêu cầu, tôi sẽ:
- Xác định các vấn đề hiệu suất hiện tại (nếu đã biết) hoặc mục tiêu cải thiện
- Xác định các chỉ số hiệu suất quan trọng (KPI) cần cải thiện
- Thiết lập mục tiêu cụ thể và đo lường được
- Xác định phạm vi tối ưu (code, database, mạng, UI, etc.)
- Ưu tiên các vấn đề hiệu suất dựa trên mức độ ảnh hưởng
- Xác nhận lại với user về mục tiêu và phạm vi trước khi chuyển sang bước tiếp theo

### Bước 2: Đo lường hiệu suất hiện tại và xác định điểm nghẽn
**Lệnh mẫu:** "Tiếp tục với bước 2 của performance-optimization cho [tên component/module]"

Khi được yêu cầu, tôi sẽ:
- Đề xuất các công cụ và phương pháp đo lường phù hợp
- Hướng dẫn cách thu thập các số liệu hiệu suất
- Phân tích codebase để tìm các điểm nghẽn tiềm năng
- Sử dụng profiling để xác định hàm/phương thức chạy chậm
- Phân tích sử dụng bộ nhớ, CPU, mạng, cơ sở dữ liệu
- Tạo baseline hiệu suất để so sánh sau khi tối ưu
- Xác nhận lại với user về các điểm nghẽn đã xác định trước khi chuyển sang bước tiếp theo

### Bước 3: Phân tích nguyên nhân và đề xuất giải pháp
**Lệnh mẫu:** "Tiếp tục với bước 3 của performance-optimization cho [tên component/module]"

Khi được yêu cầu, tôi sẽ:
- Áp dụng các kỹ thuật tối ưu từ `.cursor/rules/performance-rules/performance-optimization.mdc`
- Phân tích nguyên nhân gốc rễ của các vấn đề hiệu suất
- Đề xuất các giải pháp tối ưu cụ thể cho từng điểm nghẽn
- Ưu tiên các giải pháp tối ưu dựa trên tác động và độ phức tạp
- Đánh giá rủi ro và tác động phụ của mỗi giải pháp
- Tạo kế hoạch triển khai chi tiết
- Xác nhận lại với user về các giải pháp đề xuất trước khi chuyển sang bước tiếp theo

### Bước 4: Triển khai tối ưu và kiểm tra kết quả
**Lệnh mẫu:** "Tiếp tục với bước 4 của performance-optimization cho [tên component/module]"

Khi được yêu cầu, tôi sẽ:
- Triển khai các giải pháp tối ưu đã được phê duyệt
- Áp dụng từng tối ưu một cách có kiểm soát
- Đo lường hiệu suất sau mỗi thay đổi
- So sánh kết quả với baseline đã thiết lập
- Đánh giá hiệu quả của các tối ưu
- Tinh chỉnh hoặc điều chỉnh các tối ưu nếu cần
- Cung cấp các test để xác nhận hiệu suất cải thiện
- Xác nhận lại với user về kết quả tối ưu trước khi chuyển sang bước tiếp theo

### Bước 5: Tài liệu hóa cải tiến và bài học kinh nghiệm
**Lệnh mẫu:** "Tiếp tục với bước 5 của performance-optimization cho [tên component/module]"

Khi được yêu cầu, tôi sẽ:
- Tạo báo cáo tối ưu đầy đủ theo mẫu từ `.cursor/rules/performance-rules/performance-optimization.mdc`
- Ghi lại các số liệu trước và sau khi tối ưu
- Mô tả chi tiết các tối ưu đã thực hiện và lý do
- Liệt kê các bài học kinh nghiệm và best practices
- Đề xuất các biện pháp duy trì hiệu suất trong tương lai
- Xác định các vấn đề hiệu suất còn lại và hướng tối ưu tiếp theo

## Lưu ý quan trọng
- Mỗi bước trong workflow sẽ chỉ được thực hiện khi user yêu cầu rõ ràng
- Tại mỗi bước, luôn xác nhận với user trước khi chuyển sang bước tiếp theo
- Không cần phải liên kết với issue từ GitHub nếu không có
- Nếu không có issue, không cần sử dụng MCP GitHub
- Tuân thủ quy tắc tối ưu từ `.cursor/rules/performance-rules/performance-optimization.mdc`
- "Tối ưu sớm là gốc rễ của mọi tội lỗi" - chỉ tối ưu khi thực sự cần thiết
- Đo lường trước, tối ưu sau - luôn có dữ liệu trước khi thực hiện thay đổi
- Tối ưu điểm nghẽn lớn nhất trước - tập trung vào vấn đề gây tác động lớn nhất
- Nếu có vấn đề chưa rõ ràng hoặc cần thêm thông tin, luôn hỏi user trước khi tiếp tục thực hiện
