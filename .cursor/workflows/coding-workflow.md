# Coding Workflow Guide
### Quy trình viết code và triển khai chức năng

## Tổng quan quy trình

Workflow này bao gồm 4 bước chính:
1. <PERSON><PERSON><PERSON> yêu cầu và hiểu ngữ cảnh
2. <PERSON><PERSON><PERSON> kế hoạch viết code
3. Triển khai code
4. Xử lý phản hồi và cải tiến

## Kiểm tra MCP và lưu knowledge

Trong mỗi bước:
1. Tự động kiểm tra MCP cần thiết (Github)
2. Thu thập thông tin (knowledge) từ bước đó
3. Lưu knowledge vào file `.cursor/{feature_id}_coding.md`, hãy lưu đúng vào folder .cursor của dự án và KHÔNG tạo folder mới
4. Xác nhận với user về tính chính xác của knowledge
5. Cho phép user chỉnh sửa knowledge nếu cần
6. Sử dụng knowledge đã xác nhận cho các b<PERSON><PERSON><PERSON> tiếp theo
7. <PERSON><PERSON><PERSON> cáo với user để user biết được là bước đã hoàn tất

## Áp dụng Coding Rules

Trong toàn bộ quy trình, đặc biệt là khi triển khai code, luôn áp dụng các quy tắc từ thư mục `.cursor/rules/coding-rules/` theo hướng dẫn chi tiết trong [Applied Coding Rules](mdc:.cursor/rules/coding-rules/applied-coding-rules.mdc):

1. [Coding Conventions](mdc:.cursor/rules/coding-rules/coding-conventions.mdc) - Quy ước chung về coding
2. [Coding Style](mdc:.cursor/rules/coding-rules/coding-style.mdc) - Phong cách viết code
3. [Model Rules](mdc:.cursor/rules/coding-rules/model.mdc) - Quy tắc cho Models
4. [Controller Rules](mdc:.cursor/rules/coding-rules/controller.mdc) - Quy tắc cho Controllers
5. [View Rules](mdc:.cursor/rules/coding-rules/view.mdc) - Quy tắc cho Views

Kiểm tra các rules này để đảm bảo code tuân thủ tiêu chuẩn dự án.

## Cách sử dụng

1. Nhắc đến workflow này khi muốn bắt đầu quy trình viết code: "Thực hiện bước 1 của coding-workflow cho issue #XXX"
2. Để chuyển sang bước tiếp theo: "Tiếp tục với bước X của coding-workflow cho issue #XXX"
3. Để thực hiện một bước cụ thể: "Thực hiện bước X của coding-workflow cho issue #XXX"

## Chi tiết các bước

### Bước 1: Đọc yêu cầu và hiểu ngữ cảnh
**Lệnh mẫu:** "Thực hiện bước 1 của coding-workflow cho issue #XXX"

Khi được yêu cầu, tôi sẽ:
- Đọc tất cả comment của GitHub issue để thu thập ngữ cảnh và yêu cầu đầy đủ
- Xác định và phân tích comment `Requirement Analysis` có chứa:
  - Phần Acceptance Criteria ở định dạng Gherkin (các scenario Given-When-Then)
  - Yêu cầu chức năng và đặc tả kỹ thuật
  - Quy tắc business và ràng buộc
- Phân tích các scenario Gherkin để hiểu:
  - Hành vi và tương tác mong đợi của user
  - Phản hồi và kết quả của hệ thống
  - Các trường hợp biên và điều kiện lỗi
  - Yêu cầu tích hợp
- Phân tích kỹ lưỡng yêu cầu từ user và thông tin GitHub issue
- KHÔNG thực hiện COMMENT trên issue
- Review codebase liên quan để hiểu:
  - Các pattern và convention hiện có
  - Dependencies và quan hệ
  - Cấu trúc và tổ chức code
  - Convention đặt tên đang được sử dụng
  - Pattern testing đang được áp dụng
- Tham khảo các coding rules trong `.cursor/rules/coding-rules/` để hiểu quy tắc cần tuân thủ
- Tóm tắt yêu cầu, các scenario Gherkin, và các giả định
- Xác nhận với user về tính đầy đủ và chính xác của yêu cầu trước khi chuyển sang bước tiếp theo

### Bước 2: Tạo kế hoạch viết code
**Lệnh mẫu:** "Tiếp tục với bước 2 của coding-workflow cho issue #XXX"

Khi được yêu cầu, tôi sẽ:
- Soạn thảo kế hoạch rõ ràng, có cấu trúc dựa trên các scenario Gherkin từ Acceptance Criteria bao gồm:
  - File cần tạo mới hoặc chỉnh sửa
  - Thay đổi database nếu cần (migration, cập nhật schema)
  - Test case cần implement (ánh xạ trực tiếp từ các scenario Gherkin)
  - Chức năng mong đợi và các trường hợp biên (rút ra từ các scenario Given-When-Then)
  - Điểm tích hợp với code hiện có
  - Bất kỳ thay đổi cấu hình cần thiết nào
  - Phương pháp triển khai cho từng scenario Gherkin
- Ánh xạ từng scenario Gherkin tới các component code cụ thể và test case
- Đảm bảo kế hoạch tuân thủ các coding rules của dự án trong `.cursor/rules/coding-rules/`
- Trình bày kế hoạch cho user để xác nhận
- Điều chỉnh dựa trên feedback để đảm bảo phù hợp với kỳ vọng của user
- Xác nhận kế hoạch cuối cùng với user trước khi chuyển sang bước tiếp theo

### Bước 3: Triển khai code
**Lệnh mẫu:** "Tiếp tục với bước 3 của coding-workflow cho issue #XXX"

Khi được yêu cầu, tôi sẽ:
- Viết code tuân theo các rules trong `.cursor/rules/coding-rules/`:
  - Áp dụng `coding-style.mdc` và `coding-conventions.mdc` cho tất cả loại file
  - Sử dụng `model.mdc` cho thiết kế và triển khai model
  - Tuân theo `controller.mdc` khi phát triển controller
  - Tuân thủ `view.mdc` khi xây dựng view và frontend component
- Triển khai từng scenario Gherkin một cách có hệ thống:
  - Chuyển đổi điều kiện Given thành setup và precondition phù hợp
  - Triển khai các action When là chức năng core
  - Đảm bảo các kết quả Then được validate và test đúng cách
- Tổ chức việc triển khai một cách logic:
  - Bắt đầu với model và thay đổi database
  - Triển khai service và business logic
  - Tạo controller và route
  - Phát triển view và frontend component
  - Viết test case toàn diện phản ánh các scenario Gherkin
- Tạo test case tương ứng trực tiếp với các scenario Acceptance Criteria
- Ghi chép các quyết định quan trọng và các triển khai không rõ ràng
- Cung cấp code rõ ràng, ngắn gọn với comment phù hợp
- Xác minh code đảm bảo tuân thủ đầy đủ các tiêu chuẩn dự án
- Xác minh tất cả các scenario Gherkin được triển khai và test đúng cách
- Thông báo user khi việc triển khai hoàn tất
- Bao gồm hướng dẫn để test việc triển khai theo Acceptance Criteria
- Xác nhận kết quả triển khai với user trước khi chuyển sang bước tiếp theo

### Bước 4: Xử lý phản hồi và cải tiến
**Lệnh mẫu:** "Tiếp tục với bước 4 của coding-workflow cho issue #XXX"

Khi được yêu cầu, tôi sẽ:
- Xem xét kỹ phản hồi của user
- Thực hiện các sửa đổi theo yêu cầu một cách nhanh chóng
- Giải quyết bất kỳ lỗi hoặc trường hợp ngoại lệ nào
- Refactor nếu cần để làm rõ, cải thiện hiệu suất hoặc khả năng bảo trì
- Đảm bảo các chỉnh sửa vẫn tuân thủ các quy tắc trong `.cursor/rules/coding-rules/`
- Cập nhật các bài kiểm tra để phản ánh các thay đổi
- Ghi lại các thay đổi được thực hiện để đáp ứng phản hồi
- Tiếp tục vòng lặp phản hồi cho đến khi user hài lòng
- Khi hoàn thành, tổng hợp các thay đổi đã thực hiện và bài học kinh nghiệm

## Lưu ý quan trọng
- Mỗi bước trong workflow chỉ được thực hiện khi user yêu cầu rõ ràng
- Tại mỗi bước, luôn xác nhận với user trước khi chuyển sang bước tiếp theo
- Tuân thủ các quy ước và tiêu chuẩn đã thiết lập trong dự án
- Luôn tham khảo và áp dụng các quy tắc từ thư mục `.cursor/rules/coding-rules/`
- Nếu có vấn đề chưa rõ ràng hoặc cần thêm thông tin, luôn hỏi user trước khi tiếp tục
- Sau khi thực hiện xong bước 4, hãy xóa file knowledge `.cursor/{feature_id}_coding.md`
- KHÔNG thực hiện COMMENT trên issue hay tạo BRANCH PR nếu chưa được user cho phép
