# [Tên chức năng] - [Mã chức năng]

## M<PERSON><PERSON> đích
Mô tả ngắn gọn mục đích của chức năng này

## Các thành phần tham gia
- Component 1: Vai trò/trách nhiệm
- Component 2: <PERSON>ai trò/trách nhiệm
- ...

## Sequence Diagram
```mermaid
sequenceDiagram
    Actor A->>System B: Request action
    System B->>Database C: Query data
    Database C-->>System B: Return result
    System B-->>Actor A: Response
```

## Mô tả chi tiết luồng xử lý
1. **Bước 1**: Mô tả chi tiết
   - Input: 
   - Xử lý:
   - Output:

2. **Bước 2**: Mô tả chi tiết
   - Input:
   - Xử lý:
   - Output:
   
...

## API/Interfaces
### API 1
- Endpoint: `[URL]`
- Method: `[GET/POST/PUT/DELETE]`
- Request parameters:
  - param1 (type): mô tả
  - param2 (type): mô tả
- Response:
```json
{
  "status": "success",
  "data": {}
}
```

## Các trường hợp đặc biệt
- **Trường hợp 1**: Mô tả và cách xử lý
- **Trường hợp 2**: Mô tả và cách xử lý

## Ràng buộc và giới hạn
- Ràng buộc 1
- Ràng buộc 2
- ...

## Tài liệu liên quan
- [Tên tài liệu 1](link1)
- [Tên tài liệu 2](link2) 