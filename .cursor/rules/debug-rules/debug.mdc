---
description: 
globs: 
alwaysApply: false
---
# Quy tắc Debug và Tìm Lỗi

## Quy trình debug cơ bản
1. **T<PERSON>i tạo lỗi**: Khởi động môi trường dev và tạo điều kiện để lỗi xuất hiện một cách nhất quán
2. **Thu hẹp phạm vi**: <PERSON><PERSON><PERSON> định controller/model/view cụ thể nơi lỗi xảy ra
3. **Kiểm tra logs**: Xem Rails logs trong `log/development.log` để tìm thông báo lỗi
4. **Theo dõi luồng request**: Kiểm tra luồng dữ liệu từ request đến response
5. **Thiết lập breakpoints**: Sử dụng debugger/byebug để dừng chương trình tại các điểm quan trọng
6. **Kiểm tra SQL queries**: <PERSON><PERSON><PERSON> định các vấn đề về N+1 và query không hiệu quả
7. **Sửa lỗi**: Triển khai và kiểm tra giải pháp

## <PERSON><PERSON> thuật debug hiệu quả cho Rails

### Kỹ thuật sử dụng logger
- Sử dụng `Rails.logger.debug` cho thông tin chi tiết
- Sử dụng `Rails.logger.info` cho thông tin quan trọng
- Sử dụng `Rails.logger.error` cho lỗi
- Format log với context: `Rails.logger.debug { "User #{user.id}: #{message}" }`

### Kỹ thuật sử dụng debugger
- Thêm `binding.pry` (cần gem 'pry-byebug') vào điểm cần debug
- Sử dụng `byebug` để dừng chương trình và kiểm tra biến
- Các lệnh hữu ích trong debugger:
  - `next` (n): Chạy dòng tiếp theo
  - `step` (s): Đi vào method
  - `continue` (c): Tiếp tục chạy
  - `up`/`down`: Di chuyển trong call stack

### Kỹ thuật kiểm tra tham số và session
- Kiểm tra params: `Rails.logger.debug { "Params: #{params.inspect}" }`
- Kiểm tra session: `Rails.logger.debug { "Session: #{session.to_h}" }`
- Kiểm tra cookies: `Rails.logger.debug { "Cookies: #{cookies.to_h}" }`

### Kỹ thuật debug view và template
- Sử dụng `debug` helper: `<%= debug @variable %>`
- In thông tin: `<%= @variable.inspect %>`
- Kiểm tra instance variables: `<%= controller.instance_variables %>`

## Các lỗi phổ biến trong Rails

### Lỗi routing
- Kiểm tra bằng `rails routes | grep resource_name`
- Sai định nghĩa routes trong `config/routes.rb`
- URL helper không chính xác

### Lỗi database/ActiveRecord
- N+1 query: Sử dụng `includes`, `preload`, `eager_load`
- Sai quan hệ trong associations: `belongs_to`, `has_many`
- Validations không đúng
- Callback gây lỗi

### Lỗi controller
- Strong parameters thiếu trường
- Filter/callback chặn xử lý
- Redirect/render được gọi nhiều lần

### Lỗi view
- Nil object trong partial
- Helper method không trả về HTML an toàn
- Layout/content_for sử dụng không đúng

### Lỗi bất đồng bộ
- Background job (Sidekiq/Resque) gặp lỗi
- Race condition trong cập nhật đồng thời

## Công cụ debug cho Rails

### Gem hỗ trợ debug
- `pry-byebug`: REPL mạnh mẽ với khả năng stepping
- `better_errors`: Hiển thị lỗi chi tiết
- `web-console`: Console trong browser
- `bullet`: Phát hiện N+1 queries
- `rspec`: Test để xác nhận lỗi

### Rails built-in
- `rails console`: Thử nghiệm code trực tiếp
- `rails dbconsole`: Truy cập trực tiếp database
- `rails routes`: Liệt kê tất cả routes
- `rails log:clear`: Xóa log files

## Quy tắc sửa lỗi
1. **Hiểu nguyên nhân gốc rễ**: Đừng chỉ sửa triệu chứng
2. **Viết test**: Tạo test để xác nhận lỗi và đảm bảo sửa đúng
3. **Giải pháp nhỏ nhất**: Thực hiện thay đổi nhỏ nhất có thể để sửa lỗi
4. **Đảm bảo không phá vỡ tính năng khác**: Chạy test suite
5. **Tài liệu hóa**: Ghi chú về lỗi và cách sửa

## Mẫu báo cáo lỗi

### Mẫu báo cáo lỗi cơ bản
```
## Mô tả lỗi
[Mô tả ngắn gọn về lỗi]

## Bước tái hiện
1. Truy cập [URL cụ thể]
2. [Thực hiện hành động]
3. [...]

## Kết quả mong đợi
[Mô tả hành vi mong đợi]

## Kết quả thực tế
[Mô tả hành vi thực tế]

## Logs và lỗi
```
[Dán log lỗi]
```

## Phân tích nguyên nhân
[Phân tích nguyên nhân gốc rễ]

## Giải pháp
[Mô tả giải pháp]
```

### Mẫu báo cáo sửa lỗi
```
## Lỗi đã sửa
[Mô tả ngắn gọn về lỗi]

## Thay đổi đã thực hiện
- [File đã thay đổi]: [Mô tả thay đổi]
- [File đã thay đổi]: [Mô tả thay đổi]

## Kiểm tra đã thực hiện
- [Test đã chạy]
- [Các kiểm tra thủ công]

## Các vấn đề tiềm ẩn
[Liệt kê các vấn đề có thể phát sinh]
```
