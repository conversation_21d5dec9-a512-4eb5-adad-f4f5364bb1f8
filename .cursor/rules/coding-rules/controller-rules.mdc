---
description: 
globs: app/controllers/**/*.rb
alwaysApply: false
---
# Core Principles
- Follow RESTful architecture with standard actions (index, show, new, create, edit, update, destroy)
- Optimize controller structure:
  + Move business logic to service objects
  + Use concerns for shared functionality 
  + Implement before_action for common setup and authorization
  + Create private methods for small, reusable tasks
- Security and validation:
  + Use strong_parameters to prevent mass assignment vulnerabilities
  + Return appropriate HTTP status codes
  + Protect Ajax endpoints with validate_xhr_request?
- Response handling:
  + Prefer redirect_to over render for successful POST/PUT/DELETE requests
  + Set instance variables only when needed for views

# Organization & Namespaces
- Utilize namespaces (smart_phone, kcar, feature, topics, line) to organize controllers by functionality
- Structure concerns by purpose:
  + Breadcrumbs: manage breadcrumb trails for each controller
  + Metas: handle metadata and SEO for each controller
  + SmartPhones: process responsive handling for mobile devices
  + Utils: provide common utility methods
  + Shared: implement shared methods across controllers

# Mobile & Responsive
- Implement call_smart_phone_action for mobile view rendering
- Run init_kuruma_cookie_session to track user sessions
- Check device type with request.smart_phone?, request.mobile
- Handle iOS and Android apps with ios_app? and android_app?
- Use native_app? for common handling of all native applications
- Configure mobile-specific views in app/views/smart_phone directory

# Performance & Caching
- Implement efficient caching strategies:
  + Use page_caching? and private_page_caching? with appropriate parameters
  + Implement Caching.fetch with proper expiration times
  + Apply fragment caching for frequently used sections
  + Use Russian Doll caching for complex view hierarchies
- Query optimization:
  + Leverage service objects for data retrieval
  + Implement eager loading for required associations
  + Avoid N+1 query problems

# Service Objects Pattern
- Extend BaseService as foundation for all service objects
- Pass request and params from controllers to service objects
- Use UsedcarParams to normalize and optimize parameters
- Create specialized services for each functionality (CarService, TopicService, SearchService, etc.)
- Return consistent response objects with standard interface (error?, data, body, etc.)

# API & Ajax Handling
- Use Api class to interact with external APIs
- Process Ajax requests with request.xhr?
- Return JSON for Ajax requests
- Avoid side effects in Ajax actions
- Add skip_before_action :verify_authenticity_token for AJAX/API endpoints when necessary

# Special Handling
- A/B Testing:
  + Implement ab_test method for A/B test experiments
  + Create specialized helpers for specific tests
  + Configure A/B testing for both PC and mobile with separate configurations
- Error handling:
  + Use rescue_from for consistent error processing
  + Log errors with ExceptionNotifier
  + Implement specialized error handling for Ajax requests
  + Create UsedcarError::* custom exceptions when needed
- Breadcrumbs and Metadata:
  + Include Breadcrumbs::* and Metas::* in controllers
  + Call generate_breadcrumbs and generate_metas in render method


