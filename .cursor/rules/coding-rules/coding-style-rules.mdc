---
description:
globs: **/*.rb
alwaysApply: false
---
# Ruby Syntax & Style
## Formatting
- Use 2 spaces for indentation (no tabs)
- Limit lines to 120 characters
- Use Unix-style line endings (LF)
- End each file with a newline
- Remove trailing whitespace from each line
- Use spaces around operators and after commas
- No spaces after `(`, `[` or before `]`, `)`
- Use empty lines to break up logical segments of code
- Always add a blank line before the final return (including implicit return) in any Ruby method
- Indent `when` as deep as `case`
- Use empty lines between method definitions
- Use `_` or prefix with `_` for unused block parameters
- Avoid explicit return statements unless necessary for early returns
- Use `usedcar` (not `used_car`) when naming variables related to used cars
- Remove parentheses when calling methods without parameters
- Use Ruby 2.3's safe navigation operator (`&.`) over `try!`

## Naming Conventions
- Use `snake_case` for methods, variables, files, and directories
- Use `CamelCase` for classes and modules
- Use `SCREAMING_SNAKE_CASE` for constants
- Postfix boolean methods with question mark: `authorized?`
- Postfix potentially destructive methods with exclamation mark: `save!`
- Name service classes after their purpose: `CarSearchService`
- Name concern modules based on functionality: `Breadcrumbable`

## Code Organization
- Group related methods together
- Place class methods above instance methods
- Order methods: public > protected > private
- Place macros (has_many, validates) at the top of the class
- Use consistent ordering of macros:
  ```ruby
  # Order of macros
  include ModuleName
  extend OtherModule
  attr_* declarations
  belongs_to/has_many/has_one
  validates/validate
  callbacks
  scopes
  delegations
  ```

## String Literals
- Prefer string interpolation over concatenation
- Use single quotes for strings without interpolation
- Use double quotes for strings with interpolation or special characters
- Use heredocs for multi-line strings
  ```ruby
  message = <<~HEREDOC
    This is a multi-line
    string with interpolation: #{value}
  HEREDOC
  ```

## Conditional Expressions
- Use `&&` and `||` for boolean expressions, `and` and `or` for control flow
- Avoid nested conditionals when possible
- Use modifier clauses when conditions are simple, and add a empty line after it:
  ```ruby
  return if value.blank?
  ```
- Use `case` when comparing a value with multiple conditions
- Favor `present?`/`blank?` over explicit nil checks
- Avoid complex ternary operators (`?:`) - use `if/else` for clarity

## Collections
- Use `%w[]` for arrays of words
- Use `%i[]` for arrays of symbols
- Use `%r{}` for regexp
- Use direct hash literal syntax instead of `Hash.new`
- Use Ruby 2.0+ hash syntax when possible: `{ key: value }`
- Use consistent ordering of hash elements (alphabetically when practical)

## Error Handling
- Use `rescue` specific exceptions, not `Exception`
- Use `begin/rescue/ensure/end` for exception handling
- Avoid rescuing in method definitions unless it's explicit
- Use custom error classes for domain-specific errors

# Rails Conventions

## Controllers
- Follow the "thin controller, fat model" principle
- Extract complex business logic to service objects
- Use controller concerns for shared functionality
- Keep controllers RESTful with standard actions
- Use strong parameters to filter attributes
- Reference example: [application_controller.rb](mdc:app/controllers/application_controller.rb)
- Reference example: [cars_controller.rb](mdc:app/controllers/cars_controller.rb)

## Models
- Validate data integrity in the model
- Extract common validations to custom validators or concerns
- Use scopes for commonly-used queries
- Define meaningful associations and callbacks
- Keep models focused on a single responsibility
- Reference example: [car.rb](mdc:app/models/car.rb)
- Reference example: [shop.rb](mdc:app/models/shop.rb)

## Views
- Use Slim templates for new views
- Keep views free of business logic
- Extract partials for reused view components
- Use helpers for formatting and presentation logic

## Testing
- Test both expected and edge cases
- Write descriptive test names
- Use factories instead of fixtures
- Isolate tests from external dependencies
- Keep tests DRY but readable

# Project-Specific Conventions

## Special Conventions
- This project has no API, we call API from an external endpoint through `lib/api.rb` and `lib/api/*`
- Treat this project as FE part
- Use `services` to interact with API, handle parameters, call API, process response
- Refer existed similar code, features before created a new one, reuse if there is neccessary methods, avoid define a new one
- Minimize code, file changes
- Avoid using js if not neccessary
- There is additional environment called `staging` beside `development`, `production` and `test` (not really use `test`), which is configured nearly the same as `production` excluding the keys, authentication,...

## Namespaces
- Use namespaces to organize related controllers:
  - `smart_phone/`: Mobile-specific controllers like [smart_phone/application_controller.rb](mdc:app/controllers/smart_phone/application_controller.rb)
  - `kcar/`: K-Car specific functionality
  - `feature/`: Feature-specific controllers like [feature/topics_controller.rb](mdc:app/controllers/feature/topics_controller.rb)
  - `topics/`: Topic-related controllers
  - `line/`: LINE integration controllers like [line/cashback_controller.rb](mdc:app/controllers/line/cashback_controller.rb)
  - ...

## Services
- Inherit from `BaseService` for all service objects
- Return consistent response objects with standard interface
- Use service objects for complex business logic
- Name services after their functionality

## Concerns
- Organize concerns into subdirectories by domain:
  - `app/controllers/concerns/breadcrumbs/`
  - `app/controllers/concerns/metas/`
  - `app/controllers/concerns/smart_phones/`
  - `app/models/concerns/car/`
  - `app/models/concerns/shops/`
- Keep concerns focused on a single responsibility
- Document the purpose of each concern

## Mobile Support
- Use `request.smart_phone?` to detect mobile devices
- Implement `call_smart_phone_action` for mobile views
- Place mobile-specific views in `app/views/smart_phone/`
- Test mobile interfaces on both iOS and Android

# Dependency Management

- Clearly document gem dependencies
- Group gems logically in the Gemfile
- Specify gem versions appropriately
- Use bundler for gem management
- Be cautious about adding new dependencies
- Consider extracting common code into internal gems
- Keep dependencies updated but take effects into account (some updates need to change codes)
- Address security vulnerabilities promptly
- Vet new gems for quality and maintenance status
- Consider the impact of dependencies on application performance

# Design Patterns

- Apply the Single Responsibility Principle
- Use Dependency Injection where appropriate
- Implement the Repository Pattern for data access abstraction
- Apply the Decorator Pattern for extending functionality
- Use the Observer Pattern for event handling
- Implement the Command Pattern for encapsulating operations
- Apply the Strategy Pattern for interchangeable algorithms
- Use the Adapter Pattern for interfacing with external systems
- Follow the DRY (Don't Repeat Yourself) principle
- Apply SOLID principles where they make sense
- Helpers used for views, services for controllers,... and things like this

# Ruby Idioms
- Use `each` instead of `for` loops
- Use implicit returns when possible
- Use guard clauses to avoid nested conditionals
- Use the conditional form of methods when available (`even?` instead of `num % 2 == 0`)
- Use `map`, `select`, `reject`, `reduce` over explicit iteration when appropriate
- Use `Symbol#to_proc` syntax with enumerable methods: `array.map(&:method)`
- Use the `||=` operator for memoization
- Favor `Hash.new(0)` over initializing a hash and defaulting values
- Use `fetch` with a default value or block instead of `||`
- Avoid modifying method arguments unless that's the intent
- Use keyword arguments for options
- Use heredocs with squish for multiline strings

# Ruby Documentation
- Document public API methods using YARD or RDoc syntax
- Document complex algorithms with inline comments
- Use clear, concise method and variable names
- Add example usage in documentation when helpful
- Document exceptions that methods might raise
- Include type information for parameters and return values
- Don't over-document obvious code
- Document class and module purpose
