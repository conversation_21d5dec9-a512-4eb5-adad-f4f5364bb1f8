---
description: 
globs: 
alwaysApply: true
---
# Naming Conventions
- Model names are singular and CamelCase (e.g., User)
- Controller names are plural and CamelCase (e.g., UsersController)
- Database table names are plural and snake_case (e.g., users)
- View templates are named after controller actions (e.g., index.html.slim)
- Helper modules are named after controllers with Helper suffix (e.g., UsersHelper)
- Mailer names end with <PERSON><PERSON> (e.g., UserMailer)
- Job class names end with Job (e.g., ProcessReportJob)
- Concern modules use -able suffix when appropriate (e.g., Searchable)
- Service objects use verb-noun naming (e.g., CreateUser)
- Migration file names start with a timestamp and describe the change
- Partial file names start with underscore (e.g., _form.html.slim)
- Use meaningful names that reflect domain concepts
- Variables and method names use snake_case
- Constants use SCREAMING_SNAKE_CASE
- CSS classes use kebab-case (e.g., form-container)

# File Organization
- Place models in app/models/
- Place controllers in app/controllers/
- Place views in app/views/controller_name/
- Place helpers in app/helpers/
- Place mailers in app/mailers/
- Place jobs in app/jobs/
- Place services in app/services/
- Place concerns in app/models/concerns/ or app/controllers/concerns/
- Place shared code in lib/ if it doesn't fit in app/
- Group related files in subdirectories when appropriate
- Use namespacing for organization (e.g., Admin::UsersController)
- Place JavaScript modules in app/javascript/
- Place custom stylesheets in app/assets/stylesheets/
- Place component files in app/components/
- For smart phone features, place them in subdirectories `smart_phone` (e.g. app/controlers/smart_phone)
- For native app, place in `native_app` subdirectories 

# Project Structure
- Keep configuration in appropriate config/ files
- Use initializers for application setup
- Keep domain-specific logic in models or service objects
- Organize assets logically
- Group related functionality in modules
- Use proper autoloading and namespacing
- Keep routes organized by resource or feature area
- Organize Rake tasks in lib/tasks with meaningful file names
- Follow RESTful conventions for controller actions
- Use concerns to extract shared behavior
- Utilize service objects for complex business logic

# Ruby Coding Style
- Use 2 spaces for indentation (not tabs)
- Keep lines under 100 characters when possible
- Use snake_case for methods and variables
- Use CamelCase for classes and modules
- Use SCREAMING_SNAKE_CASE for constants
- Use spaces around operators, after commas, colons, semicolons, and around { and before }
- No spaces after (, [ and before ], )
- Avoid trailing whitespace
- End each file with a newline
- Use Ruby 3.x syntax when available
- Prefer string interpolation over concatenation
- Use guard clauses for early returns
- Prefer unless over if !condition
- Prefer each over for loops

# Rails Best Practices
- Follow "Fat models, skinny controllers" principle
- Use scopes for commonly used queries
- Utilize ActiveRecord callbacks sparingly and with caution
- Validate data at the model level
- Use concerns to extract shared behavior
- Use service objects for complex operations
- Prefer delegations over method forwarding
- Follow RESTful routes and controller actions
- Extract complex queries into scopes or query objects
- Keep controllers focused on CRUD operations
- Use partials to DRY up views
- Follow the principle of least privilege for models
- Use form objects for complex forms
- Use presenters/decorators for view-specific logic

# JavaScript Conventions
- Use camelCase for variables and functions
- Use PascalCase for classes and components
- Use 2 spaces for indentation
- Prefer ES6+ syntax
- Use arrow functions when appropriate
- Avoid global variables
- Use const and let instead of var
- Document complex functions and components

# Testing Conventions
- Use RSpec for testing
- Follow "arrange, act, assert" pattern
- One expectation per test when possible
- Use factories instead of fixtures
- Use meaningful test descriptions
- Test happy paths and edge cases
- Mock external services
- Keep tests independent and idempotent
- Use proper test doubles (stubs, mocks, spies)
- Organize tests to mirror the codebase structure

