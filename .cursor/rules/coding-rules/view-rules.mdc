---
description: 
globs: app/views/**/*.html.slim
alwaysApply: false
---
# Core View Principles
- Use Slim for new templates (maintain ERB for existing ones)
- Focus on presentation logic, avoid business logic in views
- Extract complex logic to helpers or view components
- Sanitize user input with appropriate methods
- Organize views:
  + Use partials for shared code, pass locals instead of instance variables
  + Use content_for to organize layouts
  + Avoid deep nesting in templates
  + Follow BEM naming convention for CSS classes

# View Structure
- Maintain consistent directory structure:
  + app/views/layouts/: Application layouts
  + app/views/shared/: Partials used across multiple controllers
  + app/views/components/: View components
  + app/views/smart_phone/: Mobile-specific views
- Utilize standardized partials:
  + _car_card.html.slim: For car listings (grid/list views)
  + _breadcrumbs.html.slim: For consistent breadcrumb trails
  + _meta_tags.html.slim: For SEO and metadata
  + _header.html.slim and _footer.html.slim: For layout elements

# Mobile & Responsive
- Create separate views for mobile in smart_phone/ directory
- Implement responsive design using:
  + Mobile-first approach with media queries
  + Viewport meta tags (initial-scale, width)
  + Touchscreen-friendly elements (larger buttons, swipe support)
- Use adaptive images:
  + Implement multiple image sizes for responsive displays
  + Use srcset attribute for optimal loading
  + Lazy load images with data-src pattern
- Test on both iOS and Android devices

# Components & Helpers
- Create modular view components:
  + Structure with BEM methodology
  + Maintain single responsibility
  + Include documentation
- Implement helpers for:
  + Formatting (price_fmt, date_fmt, mileage_fmt)
  + Icon handling (icon_tag)
  + Car attribute display (specs_for_car)
  + Complex UI elements (filter_tags, pagination_links)
- Utilize asset helpers:
  + image_tag with srcset support
  + stylesheet_link_tag with media queries
  + javascript_include_tag with async/defer attributes

# Car Listing Views
- Create consistent car card components:
  + Include essential data (price, year, mileage, location)
  + Standardize image ratios (16:9 for thumbnails)
  + Display highlight badges for special cars
  + Implement consistent hover effects
- Implement search results:
  + Support both grid and list views
  + Include proper sort options
  + Add filter UI consistent with search form
  + Support lazy loading for pagination

# Forms & Interactions
- Create consistent form styles:
  + Use form objects for complex forms
  + Add client-side validation with data attributes
  + Provide clear feedback for errors
  + Implement auto-suggest functionality for search inputs
- Improve search forms:
  + Implement range sliders for numeric fields
  + Use predictive search for maker/model fields
  + Create collapsible advanced search sections
  + Support saved search functionality

# Performance Optimization
- Implement caching strategies:
  + Use fragment caching with proper cache keys
  + Implement Russian Doll caching for nested components
  + Bust caches appropriately on update
- Optimize assets:
  + Compress and optimize images
  + Use SVG for icons and simple graphics
  + Implement proper asset packaging
  + Use CDN for static assets
- Lazy load below-fold content:
  + Use Intersection Observer for content loading
  + Implement skeleton screens for loading states
  + Defer non-critical JavaScript execution
- Obey Core Web Vitals standards

# Accessibility & Internationalization
- Implement proper accessibility:
  + Add appropriate ARIA attributes
  + Ensure proper contrast ratios
  + Support keyboard navigation
  + Test with screen readers
- Handle internationalization:
  + Use I18n.t for all text
  + Organize translations logically
  + Support Japanese language specifics
  + Implement proper date/time/currency formatting
