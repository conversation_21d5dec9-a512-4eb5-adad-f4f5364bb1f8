---
description: 
globs: app/models/**/*.rb
alwaysApply: false
---
# Structure & Organization
- Follow Rails naming conventions: singular, CamelCase
- Structure model content consistently:
  + Constants and class attributes
  + Includes and extends
  + Associations (belongs_to, has_many, etc.)
  + Validations and callbacks
  + Scopes
  + Class methods
  + Instance methods
- Organize models by functionality:
  + app/models/master/: ActiveHash models for master data
  + app/models/topic/: Topic-related models
  + app/models/concerns/: Shared behaviors
- Keep models focused on single responsibility
- Document complex database relationships
- Use concerns for code reuse

# Car & Shop Models
- For Car model:
  + Implement delegations for attributes from related models
  + Create helper methods for formatting (price_disp, total_price_disp)
  + Add state checking methods (newest?, dealer_car?, archive?)
  + Optimize relationships with related models (Maker, CarModel, Prefecture)
  + Implement display methods (car_name_full, mileage_disp)
- For Shop model:
  + Structure shop management logic and Car relationships
  + Define scopes for search functionality
  + Use consistent display methods (address_disp, business_hours_disp)

# ActiveHash Models
- Inherit from Master::Base for consistency
- Use fields declaration at class beginning
- Follow standard structure:
  ```ruby
  class Master::Example < Master::Base
    fields :id, :name, :code, :other_fields
    # other methods
  end
  ```
- Use YAML for master data
- Build associations between ActiveHash models
- Handle errors for API data sources
- Clearly define relationships between models (Maker, CarModel, Grade)

# Topic Models
- Store topic-related models in `app/models/topic/` directory
- Inherit from Topic::Base when appropriate
- Define search_params to support topic-based searches
- Create associations with other models as needed
- Implement methods for metadata and breadcrumb building

# Validations & Callbacks
- Implement strict validations for user inputs
- Use CustomValidations module for common validation patterns
- Create consistent error messages
- Use callbacks judiciously
- Place validations at model level for business rules
- Extract complex validations into custom validators
- Avoid callbacks that affect other models

# Concerns
- Organize concerns by functionality:
  + car/: Behaviors for Car model
  + car_inquiry/: Behaviors for Inquiry
  + custom_validations/: Shared validations
  + shops/: Behaviors for Shop model
- Focus each concern on a single functionality
- Document purpose of each concern
- Avoid interdependencies between concerns

# Cashback Models
- Implement cashback processing logic and entries
- Create strict validations for data fields
- Define clear relationships with User and Car
- Handle cashback status states (registered, confirmed, etc.)
- Secure user information in entries

# Performance Optimization
- Implement caching for frequently accessed data
- Avoid N+1 queries with eager loading
- Define indexes for frequently queried fields
- Use counter caches instead of count
- Avoid complex calculations in frequently called methods
- Use scopes for reusable queries
- Optimize complex queries
- Use CarCount and analysis services for car searches

# Security
- Never store sensitive data as plaintext
- Validate user input thoroughly
- Implement proper access control
- Use secure random tokens for sensitive operations
- Protect personal user information

# General Model Best Practices

- Keep models focused on a single responsibility
- Extract shared behavior using concerns
- Document non-obvious database relations or business rules

# Inquiry Models
- Implement strict validations for user inputs
- Use the `CustomValidations` module for common validation patterns
- Keep validation messages consistent with the rest of the application
- Use callbacks to handle data transformations:
  ```ruby
  before_validation do
    # data transformations
  end
  ```
- Implement proper file validation for uploaded content
- Document form fields and their validation requirements
- Handle different types of inquiry separately (CarInquiry, CSCarInquiry)

# Performance Considerations
- Implement caching for frequently accessed data
- Optimize queries to avoid N+1 issues
- Use efficient data loading strategies
- Avoid complex calculations in frequently called methods
- Use counter caches for frequently counted relationships
- Document performance-critical sections of code
- Use fragment caching in views to speed up page

# Security Best Practices
- Never store sensitive data in plaintext
- Validate user input thoroughly
- Implement proper access control
- Document security considerations
- Use secure random tokens for sensitive operations
- Secure user information

# ActiveRecord Best Practices
- Add appropriate database indexes for associations and frequently queried fields
- Use soft-delete (with paranoia or discard gem) instead of destroy when appropriate
- Use proper enum definitions for status fields
- Validate data at the model level for business rules
- Use custom validators for complex validation logic
- Avoid callbacks that affect other models or external services
- Use transactions for operations that need to be atomic

# Query Optimization
- Avoid complex queries directly in models
- Use scopes to create reusable queries
- Move complex queries to Query Objects
- Implement caching for frequent and expensive queries
- Optimize queries using appropriate indexes
- Use explain to analyze complex queries
- Consider raw SQL only when ActiveRecord doesn't meet requirements
- Use CarCount and related services for car search

# Relationships and Associations
- Define clear relationships between models
- Use options like dependent in associations when needed
- Avoid creating unnecessary relationships
- Use inverse_of when appropriate to avoid memory issues
- Implement validations for associations when necessary
- Use counter cache for frequently counted relationships
- Consider using gems like scenic for complex database views
- Document complex relationships between models

