---
description:
globs:
alwaysApply: false
---
# Applying Coding Rules

This document describes how to apply coding standards from the `.cursor/rules/coding-rules` directory in the development process.

## Reference Rules

- [coding-conventions-rules.mdc](mdc:.cursor/rules/coding-rules/coding-conventions-rules.mdc) - General coding conventions
- [coding-style-rules.mdc](mdc:.cursor/rules/coding-rules/coding-style-rules.mdc) - Code style guidelines
- [model-rules.mdc](mdc:.cursor/rules/coding-rules/model-rules.mdc) - Rules for Models
- [controller-rules.mdc](mdc:.cursor/rules/coding-rules/controller-rules.mdc) - Rules for Controllers
- [view-rules.mdc](mdc:.cursor/rules/coding-rules/view-rules.mdc) - Rules for Views

## Implementation in Development Process

### Step 1: Requirements Analysis and Context Understanding
- Review coding rules to understand compliance requirements during development
- Consider naming conventions, code structure, and file organization according to [coding-conventions-rules.mdc](mdc:.cursor/rules/coding-rules/coding-conventions-rules.mdc)
- Identify which specific rules apply to your current task

### Step 2: Code Planning
- Ensure your plan follows coding rules in `.cursor/rules/coding-rules/`
- Prioritize principles from [coding-style-rules.mdc](mdc:.cursor/rules/coding-rules/coding-style-rules.mdc)
- Create a checklist of applicable rules for your task

### Step 3: Code Implementation
- **All Files**: Apply [Coding Conventions](mdc:.cursor/rules/coding-rules/coding-conventions.mdc) and [Coding Style](mdc:.cursor/rules/coding-rules/coding-style.mdc)
- **Models**: Follow [Model Rules](mdc:.cursor/rules/coding-rules/model.mdc) for design and implementation
- **Controllers**: Implement [Controller Rules](mdc:.cursor/rules/coding-rules/controller.mdc)
- **Views**: Adhere to [View Rules](mdc:.cursor/rules/coding-rules/view.mdc) for frontend components
- **Testing**: Ensure test coverage follows testing standards
- **Documentation**:
  - Only update documentation when necessary and when it already exists
  - Don't create new documentation unless specifically required
  - Focus on updating technical specifications that directly affect code functionality
  - For existing docs, ensure changes reflect current code behavior

### Step 4: Code Review and Quality Assurance
- Use automated tools to check rule compliance
- Review code against the applicable rule checklist
- Ensure all changes maintain rule consistency
- Update tests to reflect applied rules

### Step 5: Feedback and Improvement
- Document any challenges in applying specific rules
- Propose improvements to rules when needed
- Share learning experiences with the team
- Keep up with rule updates and changes

## Rule Application Guidelines

### Priority Order
When rules conflict, follow this priority:
1. Specific rules (model, controller, view)
2. Coding style guidelines
3. General conventions

### Best Practices
- Keep rules documentation readily accessible
- Use IDE tools and linters to enforce rules
- Regular team discussions about rule application
- Maintain consistency across the codebase

### Common Pitfalls to Avoid
- Inconsistent rule application
- Overlooking specific rules for your context
- Not documenting rule exceptions
- Ignoring automated tool warnings

### When in Doubt
1. Consult the specific rule documentation
2. Discuss with team members
3. Seek clarification from project leads
4. Document decisions and rationale

## Continuous Improvement

### Regular Review Process
- Periodic review of rule implementation
- Team feedback on rule effectiveness
- Updates based on new technologies
- Documentation of common issues and solutions

### Training and Onboarding
- Include rules overview in onboarding
- Regular team training sessions
- Share best practices and examples
- Maintain updated rule documentation

## Rule Compliance Guidelines

### Before Committing Code
- Follow naming conventions
- Apply appropriate design patterns
- Implement required tests
- Update existing documentation (only if necessary)
- Run linting tools
- Perform self-review against rules

### During Code Review
- Check for rule compliance
- Verify test coverage
- Review documentation updates (only for existing docs)
- Ensure consistent style
- Validate architectural decisions

Remember: Rules exist to improve code quality and maintainability. They should be followed consistently but not blindly - always consider the context and discuss exceptions when necessary.
