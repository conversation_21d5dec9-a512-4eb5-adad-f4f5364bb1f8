---
description: 
globs: 
alwaysApply: false
---
# Quy tắc Tối ưu Hiệu suất

## Quy trình tối ưu hiệu suất
1. **Đo lường hiệu suất hiện tại**: Thiết lập baseline để so sánh
2. **X<PERSON><PERSON> định điểm nghẽn**: S<PERSON> dụng công cụ profiling để tìm các phần code gây chậm
3. **Phân tích nguyên nhân**: <PERSON><PERSON><PERSON> định lý do gây ra vấn đề hiệu suất
4. **Thực hiện tối ưu**: <PERSON><PERSON> dụng các kỹ thuật tối ưu phù hợp với Rails
5. **Kiểm tra kết quả**: So sánh hiệu suất trước và sau tối ưu
6. **Tài liệu hóa**: <PERSON><PERSON> lạ<PERSON> các thay đổi và kết quả

## <PERSON><PERSON> thuật đo lường hiệu suất cho Rails

### Đo thời gian thực thi
- Sử dụng `rack-mini-profiler` để đo thời gian render và DB queries
- Thêm benchmark vào code: `Benchmark.measure { ... }`
- Sử dụng Rails built-in instrumentation: `ActiveSupport::Notifications.instrument('name') { ... }`
- Theo dõi thời gian xử lý trong Rails logs

### Đo lường bộ nhớ
- Sử dụng `memory_profiler` gem: `MemoryProfiler.report { ... }`
- Kiểm tra memory bloat với `derailed_benchmarks`
- Theo dõi ObjectSpace để đếm objects: `ObjectSpace.count_objects`

### Đo lường database
- Phân tích SQL với `EXPLAIN` trong Rails console
- Sử dụng `bullet` để phát hiện N+1 queries
- Theo dõi số lượng queries với `rails log:clear` và đếm queries trong development log
- Đo lường connection pool với `ActiveRecord::Base.connection_pool.stat`

### Đo lường frontend performance
- Sử dụng browser DevTools (Network, Performance tabs)
- Đo thời gian load các assets (JS, CSS, images)
- Kiểm tra render-blocking resources
- Phân tích Core Web Vitals (LCP, FID, CLS)

## Các kỹ thuật tối ưu cho Rails

### Tối ưu ActiveRecord và Database
- **Giảm N+1 queries**: Sử dụng `includes`, `preload`, `eager_load`
- **Index hợp lý**: Tạo indexes cho các cột thường xuyên truy vấn
- **Tối ưu queries**:
  - Sử dụng `select` để lấy chỉ các cột cần thiết
  - Sử dụng `where.not`, `find_each` thay vì lặp qua tất cả records
  - Sử dụng `pluck` thay vì `map` khi chỉ cần một số cột
- **Counter caching**: Sử dụng `counter_cache: true` trong associations
- **Batch processing**: Xử lý dữ liệu lớn theo batches với `find_in_batches`

### Tối ưu caching trong Rails
- **Fragment caching**: `<% cache product do %>...<% end %>`
- **Russian Doll caching**: Nested fragment caches
- **Low-level caching**: `Rails.cache.fetch('key', expires_in: 12.hours) { ... }`
- **SQL query caching**: Kích hoạt trong `config/environments/production.rb`
- **HTTP caching**: Sử dụng ETag, Last-Modified, và Cache-Control headers
- **CDN**: Sử dụng cho assets tĩnh

### Tối ưu Rails Views
- **Tránh N+1 trong views**: Preload associations trước khi render
- **Partial caching**: Cache các partial được dùng lặp lại
- **Collection rendering**: Sử dụng `render partial: 'item', collection: @items`
- **Content_for buffering**: Giảm duplicated content
- **Tránh helpers phức tạp**: Di chuyển logic phức tạp vào Presenter/Decorator objects

### Tối ưu assets và frontend
- **Asset pipeline**: Sử dụng `config.assets.compile = false` trong production
- **Minification**: JS và CSS minification
- **Image optimization**: Sử dụng định dạng và kích thước phù hợp (WebP, AVIF)
- **Lazy loading**: Thêm `loading="lazy"` cho images
- **Turbolinks/Turbo**: Cải thiện navigation giữa các trang

### Tối ưu background processing
- **Sidekiq/Delayed Job**: Di chuyển các tác vụ nặng vào background jobs
- **Batch processing**: Chia nhỏ các tác vụ lớn
- **Scheduling**: Lên lịch jobs vào thời điểm server ít tải

## Công cụ phân tích hiệu suất cho Rails

### Công cụ monitoring
- New Relic
- Scout APM
- AppSignal
- Skylight

### Gems hỗ trợ phân tích
- `rack-mini-profiler`: Profiling request performance
- `bullet`: Phát hiện N+1 queries
- `derailed_benchmarks`: Benchmark ứng dụng Rails
- `flamegraph`: Visualize code performance
- `memory_profiler`: Phân tích sử dụng bộ nhớ
- `stackprof`: Ruby stackprofiler

### Rails built-in tools
- Rails logs với thời gian xử lý query
- ActiveSupport::Notifications
- Rails console và truy vấn SQL EXPLAIN

## Các vấn đề hiệu suất phổ biến trong Rails

### Database Performance
- N+1 queries (phổ biến nhất)
- Thiếu index cho foreign keys
- Over-fetching dữ liệu (select * thay vì các cột cần thiết)
- Joins phức tạp và không hiệu quả
- Connection pool quá nhỏ hoặc quá lớn

### Application Performance
- Callback chains quá dài trong ActiveRecord models
- Business logic phức tạp trong controllers
- View templates phức tạp và thiếu caching
- Service objects không hiệu quả
- Memory bloat từ Ruby objects

### Frontend Performance
- Assets quá lớn hoặc không được minify
- Quá nhiều HTTP requests
- JavaScript blocking rendering
- Images không được tối ưu

## Mẫu báo cáo tối ưu hiệu suất

### Mẫu báo cáo phân tích hiệu suất
```
## Vấn đề hiệu suất
[Mô tả ngắn gọn về vấn đề]

## Đo lường hiện tại
- Thời gian request: [số liệu] ms
- Số lượng SQL queries: [số liệu] queries/request
- Memory usage: [số liệu] MB
- Thông số khác: [số liệu]

## Phân tích nguyên nhân
[Giải thích chi tiết về nguyên nhân]

## Giải pháp đề xuất
[Mô tả các giải pháp tối ưu]

## Lợi ích dự kiến
[Ước tính cải thiện hiệu suất]

## Rủi ro tiềm ẩn
[Liệt kê các rủi ro có thể]
```

### Mẫu báo cáo kết quả tối ưu
```
## Tối ưu đã thực hiện
[Mô tả ngắn gọn về các tối ưu]

## Kết quả đo lường
- Thời gian request trước: [số liệu] ms -> sau: [số liệu] ms
- Số lượng SQL queries trước: [số liệu] -> sau: [số liệu]
- Memory usage trước: [số liệu] MB -> sau: [số liệu] MB
- Thông số khác: [số liệu]

## Files đã thay đổi
- [File path]: [Mô tả thay đổi]
- [File path]: [Mô tả thay đổi]

## Hạn chế còn lại
[Liệt kê các vấn đề chưa giải quyết]

## Đề xuất tiếp theo
[Đề xuất các bước tối ưu tiếp theo]
```

## Quy tắc tối ưu theo nguyên tắc

### Nguyên tắc cho Rails
1. **Database trước, application sau**: Tối ưu database là ưu tiên số một
2. **Caching là chìa khóa**: Áp dụng caching ở mọi cấp độ có thể
3. **Đo lường trước và sau**: Luôn có dữ liệu cụ thể trước khi tối ưu
4. **Tối ưu theo mức độ ảnh hưởng**: Tập trung vào các trang có traffic cao
5. **Phân tích Production**: Tối ưu dựa trên dữ liệu thực tế, không chỉ development
