---
description:
globs:
alwaysApply: false
---
# Quy tắc viết tài liệu kỹ thuật

## C<PERSON>u trúc tài liệu kỹ thuật
Tài liệu kỹ thuật phải bao gồm các phần sau:
1. **Tiêu đề và mã chức năng**: Tiêu đề ngắn gọn và mã số tham chiếu
2. **<PERSON><PERSON><PERSON> đích chức năng**: <PERSON><PERSON> tả mục đích, vấn đề giải quyết
3. **<PERSON><PERSON><PERSON> thành phần tham gia**: Liệt kê các component và vai trò
4. **Sequence diagram**: Biểu diễn luồng tương tác giữa các thành phần
5. **<PERSON><PERSON> tả chi tiết luồng xử lý**: Chi tiết từng bước trong quy trình
6. **API/Interfaces**: <PERSON><PERSON> tả các API và interface liên quan
7. **<PERSON><PERSON><PERSON> trường hợp đặc biệt**: Liệt kê các edge case và xử lý
8. **Ràng buộc và giới hạn**: Các điều kiện và giới hạn của chức năng
9. **Tài liệu liên quan**: Tham chiếu đến tài liệu liên quan

## Quy tắc tạo sequence diagram
- Sử dụng cú pháp Mermaid để tạo sequence diagram
- Đặt tên actor/component rõ ràng, phản ánh đúng vai trò
- Sử dụng mũi tên khác nhau để thể hiện loại message:
  - `->>`: Gọi đồng bộ
  - `-->>`: Phản hồi
  - `-x`: Gọi thất bại
  - `-)`: Gọi không đồng bộ
- Sử dụng `alt/else` để thể hiện điều kiện
- Sử dụng `loop` để thể hiện vòng lặp
- Sử dụng `Note` để thêm chú thích

## Định dạng mô tả chức năng
- **Mô tả input/output**:
  - Liệt kê rõ ràng các tham số đầu vào, kiểu dữ liệu và ràng buộc
  - Mô tả chi tiết kết quả trả về và các trạng thái có thể
- **Mô tả các bước xử lý**:
  - Sử dụng danh sách có thứ tự
  - Mỗi bước phải rõ ràng, cụ thể
  - Đánh số các bước và thêm tiêu đề ngắn gọn
- **Mô tả ràng buộc và điều kiện**:
  - Sử dụng ngôn ngữ chính xác
  - Nêu rõ ràng buộc kỹ thuật và nghiệp vụ
  - Chỉ rõ các giới hạn của hệ thống

## Ngôn ngữ và giọng điệu
- Sử dụng ngôn ngữ kỹ thuật chính xác
- Viết ngắn gọn, rõ ràng, tránh dài dòng
- Sử dụng thuật ngữ nhất quán trong toàn bộ tài liệu
- Sử dụng câu chủ động thay vì bị động
- Tránh các từ ngữ mơ hồ như "có thể", "nên", "thỉnh thoảng"

## Quy tắc sử dụng code
- Sử dụng code blocks (``` code ```) cho các đoạn code dài
- Sử dụng inline code (` code `) cho biến, hàm, tên lớp trong câu
- Đảm bảo code được định dạng và thụt lề đúng
- Thêm comment vào code khi cần thiết để giải thích

## Cập nhật tài liệu
- Tài liệu phải được cập nhật khi chức năng thay đổi
- Ghi rõ phiên bản và ngày cập nhật
- Ghi lại lịch sử thay đổi quan trọng
