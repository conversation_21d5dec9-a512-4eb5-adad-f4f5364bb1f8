---
description: 
globs: 
alwaysApply: false
---
# Nguyên tắc Review Code

- Review tập trung vào tính đúng đắ<PERSON>, dễ đọc và khả năng maintain
- Đưa ra feedback mang tính xây dựng, cụ thể và có thể thực hiện được
- Xem xét cả implementation kỹ thuật và business requirements
- Tuân theo checklist review nhất quán
- Ưu tiên các vấn đề major hơn là các vấn đề style nhỏ
- Chỉ approve code khi tất cả vấn đề major đã được giải quyết
- **Sử dụng tiếng Việt** để feedback và comment code
- **Giới hạn tối đa 3 comments** trên mỗi PR
- **Consolidate similar issues** thành single comment

# Checklist Review Tổng quát

## Chất lượng Code (Ưu tiên cao)
- Code có tuân thủ coding convention của project?
- Code có bị duplicate (DRY - Don't Repeat Yourself)?
- Có xử lý error/exception đầy đủ?
- Có xem xét các edge case?
- Code có được tổ chức tốt và có cấu trúc logic?
- Có tuân thủ quy tắc đặt tên?
- Method/class có quá dài hoặc quá phức tạp?
- Có áp dụng design patterns phù hợp?

## Ruby/Rails Specific Standards
- Sử dụng đúng Ruby idioms và best practices?
- ActiveRecord associations và validations được định nghĩa đúng?
- Controller actions tuân theo RESTful conventions?
- Có sử dụng đúng scopes và callbacks?
- Strong parameters được implement chính xác?
- Database migrations được viết an toàn?
- Routes được organize hợp lý?
- Gems dependencies cần thiết và up-to-date?

## Bảo mật (Critical)
- Input từ user có được validate và sanitize?
- Có kiểm tra authorization ở những nơi cần thiết?
- Data nhạy cảm (API key) có được bảo vệ?
- Có lỗ hổng SQL injection?
- Có ngăn chặn mass assignment?
- CSRF protection có được implement đúng?
- HTTP status code trả về có phù hợp?
- Params filtering để tránh log sensitive data?
- Authentication/authorization logic secure?

## Performance (Important)
- Query database có được optimize?
- Có tránh vấn đề N+1 query?
- Cache có được implement hợp lý?
- Các operation tốn nhiều resource có được xử lý hiệu quả?
- Có implement pagination cho large dataset?
- Background job có được sử dụng cho heavy tasks?
- Có quan tâm đến vấn đề memory usage?
- Database indexes được thiết kế phù hợp?
- Eager loading được sử dụng đúng chỗ?

## Code Organization & Architecture
- Concerns được sử dụng phù hợp?
- Service objects cho complex business logic?
- Presenter/decorator pattern cho view logic?
- Proper separation of concerns?
- Module/namespace organization logical?
- Helper methods defined ở đúng nơi?

# Comment Format Guidelines

## Inline Comment Structure
```markdown
**[Category - Priority]**: Brief description

**Vấn đề:**
```ruby
# code snippet showing the issue
```

**Đề xuất:**
```ruby
# improved code snippet
```

**Lý do:** Explanation in Vietnamese why this matters
```

## Categories & Priorities
- **Security - Critical**: Các vấn đề bảo mật nghiêm trọng
- **Performance - High**: Vấn đề ảnh hưởng performance
- **Logic - High**: Logic sai hoặc edge case missing
- **Convention - Medium**: Vi phạm coding conventions
- **Style - Low**: Code style improvements

## Multi-line Comment Format
Dùng cho multiple related issues trong cùng một block:

```markdown
**Multiple Issues Found:**

1. **Security**: [specific issue]
2. **Performance**: [specific issue]  
3. **Convention**: [specific issue]

**Code block:**
```ruby
# affected code with line numbers
```

**Suggestions:** Consolidated recommendations
```

# Bỏ qua khi review code

- **BỎ QUA những review** yêu cầu thêm comment để giải thích cho code
- **BỎ QUA những review** cho rằng là cần thêm document, thiếu document cho chức năng phức tạp
- **BỎ QUA những review** thiếu test case hoặc cần bổ sung test case cho chức năng phức tạp
- **BỎ QUA những phần code** đã implement tốt và không có vấn đề
- **BỎ QUA minor style issues** nếu đã đạt limit 3 comments

# Action Rules

## When to COMMENT vs APPROVE
- **COMMENT**: Có issues cần address (security, performance, logic errors)
- **APPROVE**: Code quality tốt, minor issues không ảnh hưởng functionality
- **Single "Everything looks good!"**: Không có violations nào được tìm thấy

## Comment Consolidation Rules
- Group similar issues across multiple files into one comment
- Reference specific lines/files trong consolidated comment
- Prioritize theo severity: Critical > High > Medium > Low
- Nếu có >3 issues, chọn 3 most important ones
