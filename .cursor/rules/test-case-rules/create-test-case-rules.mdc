---
description:
globs:
alwaysApply: false
---
# QUY TẮC TẠO TEST CASE

## 1. PHẠM VI VÀ LOẠI BỎ

### ✅ TẬP TRUNG VÀO:
- **Chức năng nghiệp vụ**: Test các tính năng theo yêu cầu business
- **API Testing**: Kiểm tra endpoints, request/response
- **User Experience**: Luồng người dùng trên một môi trường chuẩn
- **Data Validation**: <PERSON><PERSON><PERSON> thực dữ liệu đầu vào/đầu ra

### ❌ KHÔNG TẠO TEST CASE CHO:
- **Cross-browser Testing**: Không test trên nhiều trình duyệt (Chrome, Firefox, Safari, Edge)
- **Browser Compatibility**: Không test tương thích trình duyệt
- **Responsive Design**: Không test responsive trên các trình duyệt khác nhau
- **Code Quality**: Không test code syntax, performance, coverage
- **Unit/Integration Testing**: Không test ở mức code level

## 2. CÁC LOẠI TEST CASE (THEO ĐỘ ƯU TIÊN)

### 🔴 BẮT BUỘC - <PERSON><PERSON> ưu tiên cao:

#### **POSITIVE TEST CASE**
- Luồng chính (Happy Path): Người dùng thực hiện đúng theo thiết kế
- Dữ liệu hợp lệ: Tất cả điều kiện đầu vào đều đúng
- Kết quả mong đợi: Hệ thống hoạt động như thiết kế

#### **NEGATIVE TEST CASE**
- Dữ liệu không hợp lệ: Input sai format, thiếu required fields
- Xử lý lỗi: Kiểm tra error handling và error messages
- Edge cases: Các trường hợp ngoại lệ

### 🟡 TÙY CHỌN - Độ ưu tiên trung bình:

#### **BOUNDARY TEST CASE**
- Giá trị biên: min, max, min-1, max+1
- Giới hạn input: Length, range, format constraints
- Threshold testing: Các ngưỡng giới hạn

#### **INTEGRATION TEST CASE**
- Tương tác module: Kiểm tra liên kết giữa các chức năng
- Data flow: Dữ liệu truyền qua các module
- Side effects: Ảnh hưởng đến các tính năng khác

### 🟢 CHỈ KHI REQUIREMENT ĐỀ CẬP:

#### **PERFORMANCE TEST CASE** (Chỉ khi requirement yêu cầu)
- Response time: Thời gian phản hồi API/UI
- Load testing: Nhiều request đồng thời
- Resource usage: CPU, memory, database

#### **SECURITY TEST CASE** (Chỉ khi requirement yêu cầu)
- Authentication: Xác thực người dùng
- Authorization: Phân quyền truy cập
- Data protection: Bảo vệ thông tin nhạy cảm

## 3. QUY TẮC NHÓM VÀ TỔ CHỨC

### **NHÓM THEO CHỨC NĂNG** (Bắt buộc)
```
# [FUNCTION_NAME] - [BRIEF_DESCRIPTION]
## TC-[PREFIX]-001: [Test case title]
## TC-[PREFIX]-002: [Test case title]

# SEARCH FUNCTION - Tìm kiếm xe cũ
## TC-SEARCH-001: Tìm kiếm với từ khóa hợp lệ
## TC-SEARCH-002: Tìm kiếm với từ khóa không tồn tại
## TC-SEARCH-003: Tìm kiếm với ký tự đặc biệt

# FILTER FUNCTION - Lọc kết quả tìm kiếm
## TC-FILTER-001: Lọc theo khoảng giá
## TC-FILTER-002: Lọc theo nhiều tiêu chí đồng thời
```

### **THỨ TỰ ƯU TIÊN**
1. **Positive cases** trước → **Negative cases** sau
2. **Cơ bản** trước → **Phức tạp** sau
3. **Độc lập** trước → **Phụ thuộc** sau

### **AUTOMATION-FRIENDLY**
- Mỗi test case độc lập, không phụ thuộc lẫn nhau
- Bước thực hiện rõ ràng, có thể tự động hóa
- Data setup/cleanup được định nghĩa cụ thể

## 4. QUY TẮC URL VÀ API

### **WEB URL**
- **Domain**: Sử dụng `{host}` từ workflow step 1
- **Đầy đủ**: `{host}/usedcar/detail/12345` ❌ "Truy cập trang chi tiết"
- **Tham số cụ thể**: Cung cấp ID thực hoặc điều kiện rõ ràng

### **API ENDPOINT**
- **Format chuẩn**:
  ```
  1. Gửi request [METHOD] đến [endpoint]
     ```json
     {
       "key": "value"
     }
     ```
  ```

- **Ví dụ thực tế**:
  ```
  1. Gửi request POST đến {host}/api/v1/cars/search
     ```json
     {
       "keyword": "honda",
       "price_min": 200000000,
       "price_max": 500000000
     }
     ```
  ```

### **VÍ DỤ ĐÚNG/SAI**
| ❌ Sai | ✅ Đúng |
|---------|---------|
| "Truy cập trang tìm kiếm" | "Truy cập {host}/usedcar/search" |
| "Gọi API tìm kiếm" | "Gửi GET đến {host}/api/v1/cars/search?q=honda" |
| "Nhập từ khóa" | "Nhập 'honda civic' vào field search_keyword" |

## 5. QUY TẮC KIỂM TRA (VERIFICATION)

### **CỤ THỂ VÀ ĐO LƯỜNG ĐƯỢC**
- ❌ "Kiểm tra kết quả hiển thị đúng"
- ✅ "Kiểm tra response trả về status 200 và chứa ít nhất 5 xe Honda"

### **EXPECTED RESULT PHẢI TRÌNH BÀY**
- **Status code**: 200, 400, 404, 500...
- **Response structure**: JSON fields, data types
- **UI elements**: Button states, text content, visibility
- **Data accuracy**: Specific values, counts, calculations

### **VÍ DỤ VERIFICATION TỐT**
```
Expected Result:
- API response status: 200
- Response body chứa:
  + "total_count": >= 1
  + "data" array với mỗi item có fields: id, name, price, image_url
  + Tất cả xe trong kết quả có brand = "Honda"
- UI hiển thị:
  + Số lượng kết quả khớp với total_count
  + Mỗi xe hiển thị đầy đủ: tên, giá, hình ảnh
```

## 6. TEMPLATE VÀ FORMAT

Mỗi test case tuân theo: [test-case-template.md](mdc:.cursor/templates/test-case-template.md)

### **CHECKLIST CHẤT LƯỢNG TEST CASE**
- [ ] ID test case có prefix nhóm chức năng
- [ ] Title mô tả rõ ràng scenario
- [ ] Precondition được định nghĩa đầy đủ
- [ ] Steps có URL/API endpoint cụ thể
- [ ] Expected result có thể đo lường được
- [ ] Test case độc lập, không phụ thuộc
- [ ] Có thể automation được
