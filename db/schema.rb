# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.1].define(version: 2025_05_01_061527) do
  create_table "a_service_contacts", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "service_contact_id", null: false
    t.string "referrer_type", limit: 100
    t.string "src", limit: 100
    t.string "src_2", limit: 100
    t.string "src_3", limit: 100
    t.datetime "registered_date", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "contents_make_brand_valids", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "contents_id"
    t.integer "make_brand_id"
    t.index ["contents_id", "make_brand_id"], name: "index_contents_make_brand_valids", unique: true
  end

  create_table "contents_model_valids", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "contents_id", null: false
    t.integer "model_master_id", null: false
    t.index ["contents_id", "model_master_id"], name: "index_contents_model_valids", unique: true
  end

  create_table "m_any_configs", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "category", null: false
    t.integer "sub_category", limit: 2, null: false
    t.integer "sequence", limit: 2, null: false
    t.text "any_data", null: false
    t.string "data_type", limit: 16, null: false
    t.boolean "is_valid", null: false
    t.string "key_name", limit: 128, null: false
    t.string "remark", limit: 300, null: false
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
  end

  create_table "m_area_groups", primary_key: "area_group_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "area_id", null: false
    t.integer "country_id", null: false
    t.boolean "is_delete", null: false
  end

  create_table "m_areas", primary_key: "area_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "entry_date", null: false
    t.datetime "edit_date", null: false
    t.string "area", limit: 50, null: false
    t.string "en", limit: 50, null: false
    t.integer "sort", null: false
    t.integer "sort_localize"
  end

  create_table "m_articles", primary_key: "article_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "entry_date", null: false
    t.datetime "edit_date", null: false
    t.boolean "is_delete", null: false
    t.string "group_name", limit: 50, null: false
    t.integer "sort", null: false
    t.string "en", limit: 50, null: false
  end

  create_table "m_countries", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "country", limit: 100, null: false
    t.string "a2", limit: 2, null: false
    t.string "a3", limit: 3, null: false
    t.integer "number", null: false
    t.string "country_jp", limit: 100
    t.string "country_e", limit: 100
    t.string "ar", limit: 100
    t.string "de", limit: 100
    t.string "en", limit: 100
    t.string "es", limit: 100
    t.string "fr", limit: 100
    t.string "it", limit: 100
    t.string "ja", limit: 100
    t.string "ko", limit: 100
    t.string "pt", limit: 100
    t.string "ro", limit: 100
    t.string "ru", limit: 100
    t.string "zh_cn", limit: 100
    t.string "country_code", limit: 100
    t.string "short_country", limit: 100
    t.index ["number"], name: "m_countries_index_number", unique: true
  end

  create_table "m_cs_phone_numbers", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "phone_numer", limit: 32, null: false
    t.integer "country_number", null: false
    t.boolean "is_whats_up", null: false
    t.integer "priority", null: false
    t.boolean "is_sp_disp", null: false
    t.boolean "is_valid", null: false
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
  end

  create_table "m_exchange_rate_histories", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "target_date", null: false
    t.integer "exchange_id", null: false
    t.string "exchange_name", limit: 30, null: false
    t.string "exchange_name_ascii", limit: 10, null: false
    t.string "exchange_symbol", limit: 30, null: false
    t.float "rate", null: false
    t.datetime "created_date", null: false
    t.datetime "updated_date", null: false
  end

  create_table "m_exchange_rates", primary_key: "exchange_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "exchange_name", limit: 30, null: false
    t.string "exchange_name_ascii", limit: 10, null: false
    t.string "exchange_symbol", limit: 30, null: false
    t.decimal "rate", precision: 24, scale: 18, null: false
    t.datetime "created_date", null: false
    t.datetime "updated_date", null: false
  end

  create_table "m_import_regulations", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "country_number", null: false
    t.integer "regulations_id", null: false
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
    t.boolean "is_valid", null: false
    t.integer "model_year_from"
    t.boolean "is_model_year_from_update"
    t.integer "model_year_to"
    t.boolean "is_model_year_to_update"
    t.integer "steering_id", limit: 1
    t.boolean "is_import_regulations", null: false
    t.index ["country_number", "regulations_id"], name: "index_m_import_regulations_on_country_number_and_regulations_id", unique: true
  end

  create_table "m_inspection_groups", primary_key: "inspection_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "country_id", null: false
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
    t.boolean "is_valid", null: false
  end

  create_table "m_inspections", primary_key: "inspection_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
    t.boolean "is_valid", null: false
    t.string "name", limit: 50, null: false
  end

  create_table "m_localizes", primary_key: "country_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
    t.boolean "is_valid", null: false
    t.boolean "is_logistics", null: false
    t.integer "discharge_port_id"
  end

  create_table "m_names", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "group_key", limit: 50, null: false
    t.integer "id_of_master", null: false
    t.integer "lang_id", null: false
    t.string "name", limit: 50, null: false
    t.boolean "is_valid", null: false
  end

  create_table "m_option_groups", primary_key: "option_group_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "entry_date", null: false
    t.datetime "edit_date", null: false
    t.boolean "is_delete", null: false
    t.string "name", limit: 100, null: false
    t.integer "sort", null: false
  end

  create_table "m_options", primary_key: "option_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "entry_date", null: false
    t.datetime "edit_date", null: false
    t.boolean "is_delete", null: false
    t.string "name", limit: 100, null: false
    t.integer "group_id", null: false
    t.integer "sort", null: false
  end

  create_table "m_ports", primary_key: "port_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "country_number", null: false
    t.string "port_cd", limit: 3, null: false
    t.string "port_name", limit: 100, null: false
    t.string "port_initial", limit: 1, null: false
    t.boolean "is_valid", null: false
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
  end

  create_table "m_primary_body_styles", primary_key: "primary_body_style_id", id: { type: :integer, limit: 1 }, charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "entry_date", null: false
    t.datetime "edit_date", null: false
    t.boolean "is_delete", null: false
    t.string "name", limit: 100, null: false
    t.integer "sort", limit: 1, null: false
    t.integer "category_id", limit: 1, null: false
  end

  create_table "m_secondary_body_styles", primary_key: "secondary_body_style_id", id: { type: :integer, limit: 1 }, charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "entry_date", null: false
    t.datetime "edit_date", null: false
    t.boolean "is_delete", null: false
    t.integer "primary_body_style_id", limit: 1, null: false
    t.string "name", limit: 100, null: false
    t.integer "sort", limit: 1, null: false
    t.integer "category_id", limit: 1, null: false
  end

  create_table "m_seller_awards", primary_key: "award_id", id: :integer, charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id"
    t.integer "year"
    t.integer "award_type"
    t.boolean "is_valid"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "m_service_plans", primary_key: "id_service_plan", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "id_service", null: false
    t.integer "count", null: false
    t.string "vc_memo"
    t.integer "span"
    t.decimal "price", precision: 10, scale: 2, null: false
    t.boolean "is_display", null: false
    t.datetime "dt_created"
    t.datetime "dt_updated"
    t.decimal "price_jpy", precision: 10, scale: 2
  end

  create_table "m_services", primary_key: "id_service", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "service_name", limit: 100, null: false
    t.boolean "is_valid", null: false
    t.integer "id_site"
    t.integer "sort"
    t.datetime "dt_created", null: false
    t.datetime "dt_updated", null: false
  end

  create_table "make_brands", primary_key: "make_brand_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "make_id", null: false
    t.integer "brand_id", null: false
    t.integer "country_id", null: false
    t.boolean "is_active", null: false
    t.boolean "is_delete", null: false
    t.datetime "entry_date"
    t.datetime "update_date"
    t.integer "vehicle_type_id"
  end

  create_table "make_brands_to_world_makes", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "make_brand_id", null: false
    t.integer "world_make_id", null: false
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
    t.boolean "is_valid", null: false
    t.index ["make_brand_id", "world_make_id"], name: "index_make_brands_to_world_makes", unique: true
  end

  create_table "makes", primary_key: "make_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "make_name", limit: 50
    t.string "make_ascii_name", limit: 50
    t.string "description", limit: 2000
    t.boolean "is_valid", null: false
    t.datetime "entry_date"
    t.datetime "update_date"
    t.integer "vehicle_type_id"
  end

  create_table "makes_translates", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "lang_id", null: false
    t.integer "make_id", null: false
    t.string "make_name", limit: 50, null: false
    t.boolean "is_valid", null: false
    t.datetime "entry_date", null: false
    t.datetime "update_date", null: false
    t.index ["lang_id", "make_id"], name: "index_makes_translates_on_lang_id_and_make_id", unique: true
  end

  create_table "master_makes", primary_key: "id_make", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "vc_name", limit: 100
    t.string "vc_name_e", limit: 100
    t.integer "id_make_group"
    t.boolean "is_valid"
    t.integer "id_make_old"
    t.datetime "dt_created"
    t.datetime "dt_updated"
    t.integer "temp_make_id"
    t.index "(lower(`vc_name_e`))", name: "master_makes_index_vc_name_e"
  end

  create_table "master_models", primary_key: "id_model", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "id_make", null: false
    t.string "vc_name", limit: 100, null: false
    t.string "vc_name_e", limit: 100
    t.integer "id_category", null: false
    t.boolean "is_valid", null: false
    t.integer "id_model_old", null: false
    t.datetime "dt_created", null: false
    t.datetime "dt_updated", null: false
    t.integer "temp_model_id", null: false
    t.integer "temp_make_id", null: false
    t.index "(lower(`vc_name_e`))", name: "master_model_index_vc_name_e"
  end

  create_table "model_images", primary_key: "image_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "sales_id"
    t.integer "image_type_id"
    t.string "extension", limit: 10
    t.string "caption", limit: 300
    t.string "source_file_name", limit: 50
    t.integer "sort_order"
    t.index ["sales_id"], name: "tbl_model_image_sales_id_index"
  end

  create_table "model_master_to_world_models", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "model_master_id", null: false
    t.integer "world_model_id", null: false
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
    t.boolean "is_valid", null: false
    t.index ["model_master_id", "world_model_id"], name: "index_model_master_to_world_models", unique: true
    t.index ["world_model_id"], name: "model_master_to_world_models_index_world_model_id"
  end

  create_table "model_master_translates", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "lang_id", null: false
    t.integer "model_master_id", null: false
    t.boolean "is_valid", null: false
    t.datetime "entry_date", null: false
    t.datetime "update_date", null: false
    t.string "custom_model_name", limit: 50, null: false
    t.index ["lang_id", "model_master_id"], name: "index_model_master_translates_on_lang_id_and_model_master_id", unique: true
    t.index ["model_master_id"], name: "model_master_translates_index_model_master_id"
  end

  create_table "model_masters", primary_key: "model_master_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "make_brand_id", null: false
    t.string "custom_model_name", limit: 50, null: false
    t.string "model_ascii_name", limit: 300, null: false
    t.integer "car_type_id"
    t.boolean "is_delete", null: false
    t.datetime "entry_date"
    t.datetime "update_date"
    t.integer "vehicle_type_id"
  end

  create_table "model_sales_masters", primary_key: "sales_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "model_master_id", null: false
    t.integer "country_id", null: false
    t.integer "year"
    t.integer "month"
    t.integer "model_change_type_id"
    t.boolean "is_active", null: false
    t.boolean "is_delete", null: false
    t.datetime "entry_date"
    t.datetime "update_date"
    t.index ["model_master_id"], name: "model_sales_masters_index_model_master_id"
  end

  create_table "mt_countries", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "lang_id", null: false
    t.integer "country_id", null: false
    t.string "name", limit: 50
    t.boolean "is_valid", null: false
    t.datetime "entry_date", null: false
    t.datetime "edit_date", null: false
    t.index ["lang_id", "country_id"], name: "index_mt_countries_on_lang_id_and_country_id", unique: true
  end

  create_table "t_aggregate_offers", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "maker_id", null: false
    t.integer "model_id", null: false
    t.string "maker_nm", limit: 50, null: false
    t.string "model_nm", limit: 50, null: false
    t.integer "body_type_id", null: false
    t.integer "price_range", null: false
    t.integer "offer_count", null: false
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
  end

  create_table "t_aggregate_ranking_points", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "maker_id", null: false
    t.integer "model_id", null: false
    t.string "maker_nm", limit: 50, null: false
    t.string "model_nm", limit: 50, null: false
    t.integer "ranking_point", null: false
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
  end

  create_table "t_condition_export_performances", primary_key: "t_export_performance_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
    t.datetime "collection_month", null: false
    t.string "group_name", null: false
    t.string "param_name", null: false
    t.integer "param_value", null: false
    t.integer "all_count", default: 0, null: false
    t.integer "individual_count", default: 0, null: false
    t.index ["t_export_performance_id"], name: "PK_T_ExportPerformance", unique: true
  end

  create_table "t_condition_listing_numbers", primary_key: "t_listing_number_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
    t.datetime "collection_day", null: false
    t.string "group_name", null: false
    t.string "param_name", null: false
    t.integer "param_value", null: false
    t.integer "stock_count", default: 0, null: false
    t.index ["t_listing_number_id"], name: "PK_T_ListingNumber", unique: true
  end

  create_table "t_country_ip_addresses", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "ip_number_start", null: false
    t.bigint "ip_number_end", null: false
    t.datetime "created_date", null: false
    t.datetime "updated_date", null: false
    t.string "ip_address_start", limit: 50, null: false
    t.string "ip_address_end", limit: 50, null: false
    t.integer "country_id", null: false
    t.string "country_name", limit: 50
    t.boolean "is_valid", null: false
    t.index ["ip_number_start", "ip_number_end", "country_id"], name: "three_index_t_country_ip_addresses", unique: true
  end

  create_table "t_export_performance_queries", primary_key: "t_export_performance_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
    t.datetime "collection_month", null: false
    t.string "group_name", null: false
    t.string "param_name", null: false
    t.integer "param_value", null: false
    t.integer "all_count", default: 0, null: false
    t.integer "individual_count", default: 0, null: false
    t.index ["t_export_performance_id"], name: "PK_T_ExportPerformance", unique: true
  end

  create_table "t_export_performances", primary_key: "t_export_performance_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
    t.datetime "stock_date", null: false
    t.integer "make_id", null: false
    t.integer "model_id", null: false
    t.integer "all_money_receive_count", default: 0, null: false
    t.integer "make_money_receive_count", default: 0, null: false
    t.integer "model_money_receive_count", default: 0, null: false
    t.index ["t_export_performance_id"], name: "PK_T_ExportPerformance", unique: true
  end

  create_table "t_feedback_summaries", primary_key: "id_user_info", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
    t.integer "feedback_count1_year"
    t.integer "yes_count1_year"
    t.integer "no_count1_year"
    t.decimal "rating_avg1_year", precision: 3, scale: 1
    t.decimal "accurate_avg1_year", precision: 3, scale: 1
    t.decimal "communication_avg1_year", precision: 3, scale: 1
    t.decimal "quickly_avg1_year", precision: 3, scale: 1
    t.integer "feedback_count_half_year"
    t.integer "yes_count_half_year"
    t.integer "no_count_half_year"
    t.decimal "rating_avg_half_year", precision: 3, scale: 1
    t.decimal "accurate_avg_half_year", precision: 3, scale: 1
    t.decimal "communication_avg_half_year", precision: 3, scale: 1
    t.decimal "quickly_avg_half_year", precision: 3, scale: 1
    t.integer "feedback_count_total"
    t.integer "yes_count_total"
    t.integer "no_count_total"
    t.decimal "rating_avg_total", precision: 3, scale: 1
    t.decimal "accurate_avg_total", precision: 3, scale: 1
    t.decimal "communication_avg_total", precision: 3, scale: 1
    t.decimal "quickly_avg_total", precision: 3, scale: 1
  end

  create_table "t_invoice_issue_ranking_countries", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "invoice_issue_ranking_county_id"
    t.bigint "make_id"
    t.bigint "model_id"
    t.integer "issue_count"
    t.integer "country_number"
    t.integer "image_id"
    t.integer "ranking"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["country_number"], name: "index_t_invoice_issue_ranking_countries_on_country_number"
    t.index ["invoice_issue_ranking_county_id"], name: "index_t_invoice_issue_ranking_countries_on_ranking_id", unique: true
    t.index ["make_id"], name: "index_t_invoice_issue_ranking_countries_on_make_id"
    t.index ["model_id"], name: "index_t_invoice_issue_ranking_countries_on_model_id"
  end

  create_table "t_listing_counts", primary_key: "t_listing_count_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "service_id", null: false
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
    t.boolean "is_listing", null: false
    t.integer "total_count", null: false
    t.integer "country_number", null: false
    t.integer "make_id", null: false
    t.integer "model_id", null: false
    t.integer "parts_category_id", null: false
    t.integer "make_brand_id", null: false
    t.integer "model_master_id", null: false
    t.integer "buy_now_only_count", null: false
  end

  create_table "t_listing_number_queries", primary_key: "t_listing_number_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
    t.datetime "collection_day", null: false
    t.string "group_name", null: false
    t.string "param_name", null: false
    t.integer "param_value", null: false
    t.integer "stock_count", default: 0, null: false
    t.index ["t_listing_number_id"], name: "PK_T_ListingNumber", unique: true
  end

  create_table "t_listing_numbers", primary_key: "t_listing_number_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
    t.datetime "stock_date", null: false
    t.integer "make_id", null: false
    t.integer "model_id", null: false
    t.integer "make_model_stock", default: 0, null: false
    t.index ["make_id", "model_id", "stock_date"], name: "index_t_listing_numbers_on_make_id_and_model_id_and_stock_date"
    t.index ["t_listing_number_id"], name: "PK_T_ListingNumber", unique: true
  end

  create_table "t_offer_response_rate_summaries", primary_key: "seller_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
    t.integer "offer_count"
    t.integer "response_count"
    t.integer "response_time"
  end

  create_table "t_popular_rankings", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "country_number", null: false
    t.integer "make_id", null: false
    t.integer "model_id", null: false
    t.integer "ranking_point", null: false
    t.string "media_template_path", limit: 400
    t.string "media_template_prefix", limit: 10
    t.string "media_template_suffix", limit: 50
    t.integer "id_merismus"
    t.integer "id_make"
    t.integer "id_model"
    t.integer "make_brand_id", null: false
    t.integer "model_master_id", null: false
    t.integer "model_year", null: false
    t.index ["country_number", "make_id", "model_id"], name: "index_t_popular_rankings", unique: true
  end

  create_table "t_port_listings", primary_key: "port_listing_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "country_number", null: false
    t.string "port_cd", limit: 3, null: false
    t.boolean "is_valid", null: false
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
    t.integer "category_id", null: false
  end

  create_table "t_trade_informations", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.datetime "publish_date", null: false
    t.integer "category", default: 1, null: false
    t.text "title", null: false
    t.text "comment", null: false
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
    t.boolean "is_valid", null: false
    t.boolean "is_post", null: false
    t.boolean "is_important", null: false
    t.datetime "show_start_date", null: false
    t.datetime "show_end_date", null: false
    t.index ["is_valid", "is_post", "category"], name: "idx_on_is_valid_is_post_category_57098f8ef7"
    t.index ["publish_date"], name: "index_t_trade_informations_on_publish_date"
    t.index ["show_start_date", "show_end_date"], name: "idx_on_show_start_date_show_end_date_c68f55b4d5"
  end

  create_table "t_trims", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "model_id", null: false
    t.integer "trim_id", null: false
    t.string "pattern_type", limit: 40, null: false
    t.boolean "is_valid", null: false
    t.datetime "created_date", null: false
    t.datetime "updated_date", null: false
    t.integer "vehicle_length"
    t.integer "vehicle_width"
    t.integer "vehicle_height"
    t.string "short_pattern_type", limit: 50
    t.index ["model_id", "trim_id"], name: "index_t_trims_on_model_id_and_trim_id", unique: true
  end

  create_table "trims", primary_key: "trim_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "sales_id"
    t.string "pattern_type", limit: 50
    t.string "trim_name", limit: 200
    t.string "trim_ascii_name", limit: 100
    t.integer "doors"
    t.integer "body_style_id"
    t.integer "transmission_id"
    t.integer "curb_weight"
    t.integer "wheelbase"
    t.integer "tread_front"
    t.integer "tread_rear"
    t.integer "length"
    t.integer "width"
    t.integer "height"
    t.integer "fuel_capacity"
    t.decimal "steer_dia", precision: 5
    t.integer "ground_clearance"
    t.integer "suspension_type_front_id"
    t.integer "suspension_type_rear_id"
    t.integer "brake_type_front_id"
    t.integer "brake_type_rear_id"
    t.integer "engine_location_id"
    t.integer "tire_front_id"
    t.integer "tire_rear_id"
    t.integer "spring_front_id"
    t.integer "spring_rear_id"
    t.integer "steering_type_id"
    t.integer "steering_location_id"
    t.integer "seat"
    t.decimal "price", precision: 12, scale: 2
    t.integer "drive_wheel_id"
    t.integer "engine_id"
    t.integer "power_source_id"
    t.integer "special_edition_id"
    t.string "remarks", limit: 2000
    t.integer "luggage_capacity"
    t.integer "total_power"
    t.decimal "total_torque", precision: 5, scale: 1
    t.integer "total_power_kw"
    t.integer "total_torque_nm"
    t.decimal "drive_distance", precision: 8, scale: 2
    t.decimal "max_speed", precision: 8, scale: 2
    t.decimal "co2_exhaust", precision: 8, scale: 2
    t.integer "limited_sales_count"
    t.integer "sales_end_year"
    t.integer "sales_end_month"
    t.boolean "is_active"
    t.boolean "is_delete"
    t.datetime "entry_date"
    t.datetime "update_date"
    t.string "short_pattern_type", limit: 50
    t.index ["trim_ascii_name"], name: "trims_index_trim_ascii_name"
  end

  create_table "u_marine_insurance_prices", primary_key: "marine_insurance_price_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "user_id", null: false
    t.decimal "invoice_price_max", precision: 20, scale: 2
    t.decimal "invoice_price_min", precision: 20, scale: 2, null: false
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
    t.boolean "is_valid", null: false
    t.decimal "icca_price", precision: 20, scale: 2
    t.decimal "iccb_price", precision: 20, scale: 2
    t.decimal "iccc_price", precision: 20, scale: 2
    t.decimal "iccd_price", precision: 20, scale: 2
    t.decimal "icce_price", precision: 20, scale: 2
  end

  create_table "u_sellers_inspection_masters", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "sellers_inspection_id", null: false
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
    t.integer "user_id", null: false
    t.integer "inspection_id", null: false
    t.integer "exchange_rate_id", null: false
    t.boolean "is_valid", null: false
    t.decimal "price", precision: 20, null: false
    t.integer "displacement_min", null: false
    t.integer "displacement_max", null: false
    t.string "displacement_name", limit: 50, null: false
  end

  create_table "u_sellers_port_masters", primary_key: "sellers_port_master_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "user_id", null: false
    t.datetime "create_date", null: false
    t.datetime "update_date", null: false
    t.boolean "is_valid", null: false
    t.integer "port_id", null: false
    t.integer "sort"
    t.index ["sellers_port_master_id"], name: "index_u_sellers_port_masters_on_sellers_port_master_id", unique: true
  end

  create_table "u_thankyou_offers_condition_details", id: false, charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "thankyou_offers_condition_detail_id"
    t.integer "thankyou_offers_condition_id", null: false
    t.integer "max_count", null: false
    t.integer "device", null: false
    t.integer "priority", null: false
    t.integer "search_type", null: false
    t.integer "sort_order", null: false
    t.datetime "create_date", null: false
  end

  create_table "u_thankyou_offers_conditions", primary_key: "thankyou_offers_condition_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "max_count_pc", null: false
    t.integer "max_count_sp", null: false
    t.boolean "is_valid", null: false
    t.integer "priority", null: false
    t.boolean "is_check_on", null: false
    t.datetime "start_date", null: false
    t.datetime "update_date", null: false
    t.datetime "create_date", null: false
  end

  create_table "u_thankyou_offers_seller_conditions", primary_key: "thankyou_offers_seller_condition_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "thankyou_offers_condition_detail_id", null: false
    t.integer "with_type", null: false
    t.integer "seller_id", null: false
    t.datetime "create_date", null: false
  end

  create_table "world_model_makes", primary_key: "world_model_id", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "world_make_id"
    t.integer "world_make_old_id"
    t.boolean "is_valid", null: false
    t.datetime "entry_date", null: false
    t.datetime "update_date", null: false
  end

  create_table "x_inspection_by_countries", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "country_id"
    t.integer "inspection_id"
    t.datetime "create_date"
    t.datetime "update_date"
    t.boolean "is_valid"
    t.datetime "start_date"
    t.decimal "inspection_price", precision: 20, scale: 2
  end

  create_table "x_inspections", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "inspection_id_by_country", null: false
    t.bigint "inspection_id_by_seller", null: false
    t.datetime "create_date", null: false
  end

end
