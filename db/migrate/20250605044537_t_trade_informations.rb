class TTradeInformations < ActiveRecord::Migration[7.1]
  def change
    create_table :t_trade_informations, id: false do |t|
      t.primary_key :id
      t.datetime :publish_date, null: false
      t.integer :category, null: false, default: 1
      t.text :title, null: false
      t.text :comment, null: false
      t.datetime :create_date, null: false
      t.datetime :update_date, null: false
      t.boolean :is_valid, null: false
      t.boolean :is_post, null: false
      t.boolean :is_important, null: false
      t.datetime :show_start_date, null: false
      t.datetime :show_end_date, null: false
    end
  end
end
