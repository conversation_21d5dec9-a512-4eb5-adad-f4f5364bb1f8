class AddOptimizedIpRangeIndexToCountryIpAddresses < ActiveRecord::Migration[7.1]
  def up
    add_index :t_country_ip_addresses, [:ip_number_end, :ip_number_start, :country_id], name: "three_index_t_country_ip_addresses_reserved"
    add_index :t_country_ip_addresses, [:ip_number_start, :id, :ip_number_end, :country_id], name: "four_index_t_country_ip_addresses"
  end

  def down
    remove_index :t_country_ip_addresses, name: 'three_index_t_country_ip_addresses_reserved'
    remove_index :t_country_ip_addresses, name: 'four_index_t_country_ip_addresses'
  end
end
