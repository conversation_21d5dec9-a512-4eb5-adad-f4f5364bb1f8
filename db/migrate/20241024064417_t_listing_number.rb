class TListingNumber < ActiveRecord::Migration[7.1]
  def change
    create_table :t_listing_numbers, id: false do |t|
      t.primary_key :t_listing_number_id
      t.datetime :create_date, null: false
      t.datetime :update_date, null: false
      t.datetime :stock_date, null: false
      t.integer :make_id, null: false
      t.integer :model_id, null: false
      t.integer :make_model_stock, null: false, default: 0
    end

    add_index :t_listing_numbers, :t_listing_number_id, name: "PK_T_ListingNumber", unique: true
  end
end
