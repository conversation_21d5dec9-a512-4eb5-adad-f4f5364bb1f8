class TExportPerformance < ActiveRecord::Migration[7.1]
  def change
    create_table :t_export_performances, id: false do |t|
      t.primary_key :t_export_performance_id
      t.datetime :create_date, null: false
      t.datetime :update_date, null: false
      t.datetime :stock_date, null: false
      t.integer :make_id, null: false
      t.integer :model_id, null: false
      t.integer :all_money_receive_count, null: false, default: 0
      t.integer :make_money_receive_count, null: false, default: 0
      t.integer :model_money_receive_count, null: false, default: 0
    end

    add_index :t_export_performances, :t_export_performance_id, name: "PK_T_ExportPerformance", unique: true
  end
end
