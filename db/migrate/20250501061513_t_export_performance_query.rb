class TExportPerformanceQuery < ActiveRecord::Migration[7.1]
  def change
    create_table :t_export_performance_queries, id: false do |t|
      t.primary_key :t_export_performance_id
      t.datetime :create_date, null: false
      t.datetime :update_date, null: false
      t.datetime :collection_month, null: false
      t.string :group_name, null: false
      t.string :param_name, null: false
      t.integer :param_value, null: false
      t.integer :all_count, null: false, default: 0
      t.integer :individual_count, null: false, default: 0
    end

    add_index :t_export_performance_queries, :t_export_performance_id, name: "PK_T_ExportPerformance", unique: true
  end
end
