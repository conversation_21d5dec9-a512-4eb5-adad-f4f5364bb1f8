class TListingNumberQuery < ActiveRecord::Migration[7.1]
  def change
    create_table :t_listing_number_queries, id: false do |t|
      t.primary_key :t_listing_number_id
      t.datetime :create_date, null: false
      t.datetime :update_date, null: false
      t.datetime :collection_day, null: false
      t.string :group_name, null: false
      t.string :param_name, null: false
      t.integer :param_value, null: false
      t.integer :stock_count, null: false, default: 0
    end

    add_index :t_listing_number_queries, :t_listing_number_id, name: "PK_T_ListingNumber", unique: true
  end
end
