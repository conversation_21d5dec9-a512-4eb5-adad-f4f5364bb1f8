services:
  db:
    image: mysql:8.0
    command: '--default-authentication-plugin=mysql_native_password'
    environment:
      MYSQL_USER: webapp
      MYSQL_DATABASE: webapp
      MYSQL_PASSWORD: 123456
      MYSQL_ROOT_USER: root
      MYSQL_ROOT_PASSWORD: 123456
    ports:
      - '43306:3306'
    volumes:
      - mysql-data:/var/lib/mysql
      - './docker/db/conf.d:/etc/mysql/conf.d'

  redis:
    image: redis:6.0
    ports:
      - "26379:6379"

  web:
    image: tradecarview-web:latest
    platform: linux/x86_64
    build:
      context: .
      dockerfile: Dockerfile.dev
    command: sh ./docker-entrypoint.dev.sh
    environment:
      PORT: 8080
      NEWRELIC_MONITOR_MODE_ENABLED: 'false'
    volumes:
      - ".:/webapp"
      - $GOOGLE_APPLICATION_CREDENTIALS:/root/sa_adc.json
    ports:
      - "18080:8080"
    depends_on:
      - db
      - redis
      - mailcatcher
    stdin_open: true
    tty: true

  webpacker:
    image: tradecarview-web:latest
    platform: linux/x86_64
    command: sh -lc "cd /webapp; bundle && yarn && yarn start"
    volumes:
      - .:/webapp
      - $GOOGLE_APPLICATION_CREDENTIALS:/root/sa_adc.json
    ports:
      - '3035:3035'
    depends_on:
      - web
    environment:
      WEBPACKER_DEV_SERVER_HOST: 0.0.0.0

  gcloudtasks:
    image: ghcr.io/aertje/cloud-tasks-emulator:latest
    command: -host 0.0.0.0 -port 8123 -queue "projects/tradecarview-dev/locations/asia-northeast1/queues/tradecarview-dev-default"
    ports:
      - "8123:8123"
    environment:
      APP_ENGINE_EMULATOR_HOST: http://web:8080

  mailcatcher:
    image: sj26/mailcatcher:latest
    command: mailcatcher --foreground --ip 0.0.0.0
    ports:
      - '21080:1080'

volumes:
  mysql-data:
