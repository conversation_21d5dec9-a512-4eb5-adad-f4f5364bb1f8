GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    active_hash (2.3.0)
      activesupport (>= 5.0.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activerecord-import (1.5.1)
      activerecord (>= 4.2)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      mutex_m
      securerandom (>= 0.3)
      tzinfo (~> 2.0)
    addressable (2.8.6)
      public_suffix (>= 2.0.2, < 6.0)
    base64 (0.2.0)
    benchmark (0.4.0)
    bigdecimal (3.1.8)
    bindex (0.8.1)
    bootsnap (1.18.3)
      msgpack (~> 1.2)
    brakeman (6.1.2)
      racc
    builder (3.3.0)
    bundler-audit (0.9.1)
      bundler (>= 1.2.0, < 3)
      thor (~> 1.0)
    byebug (11.1.3)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    coderay (1.1.3)
    concurrent-ruby (1.3.4)
    config (5.5.2)
      deep_merge (~> 1.2, >= 1.2.1)
      ostruct
    connection_pool (2.4.1)
    crass (1.0.6)
    crawler_detect (1.2.3)
      qonfig (~> 0.24)
    database_cleaner (2.0.2)
      database_cleaner-active_record (>= 2, < 3)
    database_cleaner-active_record (2.1.0)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.4.1)
    declarative (0.0.20)
    deep_merge (1.2.2)
    diff-lcs (1.5.1)
    digest-crc (0.6.5)
      rake (>= 12.0.0, < 14.0.0)
    docile (1.4.0)
    drb (2.2.1)
    dry-configurable (0.11.6)
      concurrent-ruby (~> 1.0)
      dry-core (~> 0.4, >= 0.4.7)
      dry-equalizer (~> 0.2)
    dry-core (0.9.1)
      concurrent-ruby (~> 1.0)
      zeitwerk (~> 2.6)
    dry-equalizer (0.3.0)
    erubi (1.13.0)
    factory_bot (6.4.6)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.4.3)
      factory_bot (~> 6.4)
      railties (>= 5.0.0)
    faker (3.2.3)
      i18n (>= 1.8.11, < 2)
    faraday (2.9.0)
      faraday-net_http (>= 2.0, < 3.2)
    faraday-multipart (1.0.4)
      multipart-post (~> 2)
    faraday-net_http (3.1.0)
      net-http
    faraday-retry (2.2.0)
      faraday (~> 2.0)
    ffi (1.16.3)
    figaro (1.2.0)
      thor (>= 0.14.0, < 2)
    gapic-common (0.20.0)
      faraday (>= 1.9, < 3.a)
      faraday-retry (>= 1.0, < 3.a)
      google-protobuf (~> 3.14)
      googleapis-common-protos (>= 1.3.12, < 2.a)
      googleapis-common-protos-types (>= 1.3.1, < 2.a)
      googleauth (~> 1.0)
      grpc (~> 1.36)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-apis-cloudresourcemanager_v1 (0.38.0)
      google-apis-core (>= 0.14.0, < 2.a)
    google-apis-core (0.14.0)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (~> 1.9)
      httpclient (>= 2.8.1, < 3.a)
      mini_mime (~> 1.0)
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
      rexml
    google-apis-iamcredentials_v1 (0.19.0)
      google-apis-core (>= 0.14.0, < 2.a)
    google-apis-storage_v1 (0.34.0)
      google-apis-core (>= 0.14.0, < 2.a)
    google-cloud-core (1.6.1)
      google-cloud-env (>= 1.0, < 3.a)
      google-cloud-errors (~> 1.0)
    google-cloud-env (2.1.1)
      faraday (>= 1.0, < 3.a)
    google-cloud-error_reporting (0.42.3)
      concurrent-ruby (~> 1.1)
      google-cloud-core (~> 1.5)
      google-cloud-error_reporting-v1beta1 (~> 0.0)
      stackdriver-core (~> 1.3)
    google-cloud-error_reporting-v1beta1 (0.8.0)
      gapic-common (>= 0.20.0, < 2.a)
      google-cloud-errors (~> 1.0)
    google-cloud-errors (1.3.1)
    google-cloud-location (0.6.0)
      gapic-common (>= 0.20.0, < 2.a)
      google-cloud-errors (~> 1.0)
    google-cloud-logging (2.3.3)
      concurrent-ruby (~> 1.1)
      google-cloud-core (~> 1.5)
      google-cloud-logging-v2 (~> 0.0)
      stackdriver-core (~> 1.3)
    google-cloud-logging-v2 (0.11.0)
      gapic-common (>= 0.20.0, < 2.a)
      google-cloud-errors (~> 1.0)
    google-cloud-resource_manager (0.37.0)
      google-apis-cloudresourcemanager_v1 (~> 0.1)
      google-cloud-core (~> 1.6)
      googleauth (>= 0.16.2, < 2.a)
    google-cloud-storage (1.49.0)
      addressable (~> 2.8)
      digest-crc (~> 0.4)
      google-apis-core (~> 0.13)
      google-apis-iamcredentials_v1 (~> 0.18)
      google-apis-storage_v1 (~> 0.33)
      google-cloud-core (~> 1.6)
      googleauth (~> 1.9)
      mini_mime (~> 1.0)
    google-cloud-tasks-v2 (0.9.0)
      gapic-common (>= 0.20.0, < 2.a)
      google-cloud-errors (~> 1.0)
      google-cloud-location (>= 0.4, < 2.a)
      grpc-google-iam-v1 (~> 1.1)
    google-cloud-trace (0.42.2)
      concurrent-ruby (~> 1.1)
      google-cloud-core (~> 1.5)
      google-cloud-trace-v1 (~> 0.0)
      google-cloud-trace-v2 (~> 0.0)
      stackdriver-core (~> 1.3)
    google-cloud-trace-v1 (0.7.0)
      gapic-common (>= 0.20.0, < 2.a)
      google-cloud-errors (~> 1.0)
    google-cloud-trace-v2 (0.7.0)
      gapic-common (>= 0.20.0, < 2.a)
      google-cloud-errors (~> 1.0)
    google-iam-credentials-v1 (0.4.0)
      gapic-common (>= 0.10, < 2.a)
      google-cloud-errors (~> 1.0)
    google-protobuf (3.25.5-aarch64-linux)
    google-protobuf (3.25.5-x86_64-linux)
    googleapis-common-protos (1.5.0)
      google-protobuf (~> 3.18)
      googleapis-common-protos-types (~> 1.7)
      grpc (~> 1.41)
    googleapis-common-protos-types (1.13.0)
      google-protobuf (~> 3.18)
    googleauth (1.11.0)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    grpc (1.53.2)
      google-protobuf (~> 3.21)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.53.2-x86_64-linux)
      google-protobuf (~> 3.21)
      googleapis-common-protos-types (~> 1.0)
    grpc-google-iam-v1 (1.7.0)
      google-protobuf (~> 3.18)
      googleapis-common-protos (~> 1.4)
      grpc (~> 1.41)
    hiredis (0.6.3)
    httpclient (2.8.3)
    i18n (1.14.6)
      concurrent-ruby (~> 1.0)
    inline_svg (1.9.0)
      activesupport (>= 3.0)
      nokogiri (>= 1.6)
    io-console (0.7.2)
    irb (1.14.1)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.11.5)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jpmobile (7.1.0)
      mail (~> 2.7.0)
      rexml
      scanf
    jwt (2.8.0)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.6.6)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.7.1)
      mini_mime (>= 0.1.1)
    marcel (1.0.4)
    matrix (0.4.2)
    meta-tags (2.20.0)
      actionpack (>= 6.0.0, < 7.2)
    method_source (1.0.0)
    mini_mime (1.1.5)
    mini_portile2 (2.8.9)
    minitest (5.25.1)
    msgpack (1.7.2)
    multi_json (1.15.0)
    multipart-post (2.4.0)
    mustermann (3.0.0)
      ruby2_keywords (~> 0.0.1)
    mutex_m (0.2.0)
    mysql2 (0.5.6)
    net-http (0.4.1)
      uri
    net-imap (0.5.8)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    newrelic-infinite_tracing (9.7.1)
      grpc (~> 1.34)
      newrelic_rpm (= 9.7.1)
    newrelic_rpm (9.7.1)
    nio4r (2.7.3)
      racc (~> 1.4)
    nokogiri (1.18.9)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    os (1.1.4)
    ostruct (0.6.1)
    parallel (1.24.0)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-byebug (3.10.1)
      byebug (~> 11.0)
      pry (>= 0.13, < 0.15)
    psych (3.3.4)
    public_suffix (5.0.4)
    puma (6.4.3)
      nio4r (~> 2.0)
    qonfig (0.28.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-mini-profiler (3.3.1)
      rack (>= 1.2.0)
    rack-protection (4.1.1)
      base64 (>= 0.1.0)
      logger (>= 1.6.0)
      rack (>= 3.0.0, < 4)
    rack-proxy (0.7.7)
      rack
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.1.0)
      rack (>= 1.3)
    rack-utf8_sanitizer (1.10.1)
      rack (>= 1.0, < 4.0)
    rackup (2.2.1)
      rack (>= 3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rake (13.2.1)
    rb-fsevent (0.11.2)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    rdoc (*******)
    recaptcha (5.16.0)
    redis (4.8.1)
    redis-actionpack (5.4.0)
      actionpack (>= 5, < 8)
      redis-rack (>= 2.1.0, < 4)
      redis-store (>= 1.1.0, < 2)
    redis-activesupport (5.3.0)
      activesupport (>= 3, < 8)
      redis-store (>= 1.3, < 2)
    redis-namespace (1.11.0)
      redis (>= 4)
    redis-rack (3.0.0)
      rack-session (>= 0.2.0)
      redis-store (>= 1.2, < 2)
    redis-rails (5.0.2)
      redis-actionpack (>= 5.0, < 6)
      redis-activesupport (>= 5.0, < 6)
      redis-store (>= 1.2, < 2)
    redis-store (1.10.0)
      redis (>= 4, < 6)
    regexp_parser (2.10.0)
    reline (0.5.10)
      io-console (~> 0.5)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    retriable (3.1.2)
    rexml (3.3.9)
    rsolr (2.3.0)
      builder (>= 2.1.2)
      faraday (>= 0.9.0)
    rsolr-ext (1.0.3)
      rsolr (>= 1.0.2)
    rspec-core (3.13.0)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (6.1.1)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      railties (>= 6.1)
      rspec-core (~> 3.12)
      rspec-expectations (~> 3.12)
      rspec-mocks (~> 3.12)
      rspec-support (~> 3.12)
    rspec-support (3.13.1)
    ruby2_keywords (0.0.5)
    rubystats (0.4.1)
      matrix
    rubyzip (2.3.2)
    sass-rails (6.0.0)
      sassc-rails (~> 2.1, >= 2.1.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    scanf (1.0.0)
    securerandom (0.4.1)
    selenium-webdriver (4.10.0)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    semantic_range (3.0.0)
    shoulda-matchers (5.3.0)
      activesupport (>= 5.2.0)
    signet (0.19.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov_json_formatter (0.1.4)
    sinatra (4.1.1)
      logger (>= 1.6.0)
      mustermann (~> 3.0)
      rack (>= 3.0.0, < 4)
      rack-protection (= 4.1.1)
      rack-session (>= 2.0.0, < 3)
      tilt (~> 2.0)
    slim (5.2.1)
      temple (~> 0.10.0)
      tilt (>= 2.1.0)
    slim-rails (3.6.3)
      actionpack (>= 3.1)
      railties (>= 3.1)
      slim (>= 3.0, < 6.0, != 5.0.0)
    split (4.0.3)
      matrix
      redis (>= 4.2)
      rubystats (>= 0.3.0)
      sinatra (>= 1.2.6)
    spring (4.1.3)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    stackdriver (0.20.1)
      google-cloud-error_reporting (~> 0.41)
      google-cloud-logging (~> 2.1)
      google-cloud-trace (~> 0.40)
    stackdriver-core (1.5.0)
      google-cloud-core (~> 1.2)
    temple (0.10.3)
    thor (1.4.0)
    tilt (2.3.0)
    timeout (0.4.3)
    trailblazer-option (0.1.2)
    turbolinks (5.2.1)
      turbolinks-source (~> 5.2)
    turbolinks-source (5.2.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    uri (0.13.2)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webdrivers (5.3.1)
      nokogiri (~> 1.6)
      rubyzip (>= 1.3.0)
      selenium-webdriver (~> 4.0, < 4.11)
    webpacker (5.4.4)
      activesupport (>= 5.2)
      rack-proxy (>= 0.6.1)
      railties (>= 5.2)
      semantic_range (>= 2.3.0)
    websocket (1.2.10)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.7.0)
    zgcp_toolkit (1.2.3)
      dry-configurable (~> 0.11.6)
      google-cloud-error_reporting (~> 0.42.0)
      stackdriver (~> 0.20.1)

PLATFORMS
  aarch64-linux
  x86_64-linux

DEPENDENCIES
  active_hash (~> 2.3.0)
  activerecord-import
  bootsnap (>= 1.4.4)
  brakeman
  bundler-audit
  byebug
  capybara (>= 3.26)
  config
  crawler_detect
  database_cleaner (~> 2.0, >= 2.0.2)
  factory_bot_rails (~> 6.2)
  faker (~> 3.2, >= 3.2.2)
  faraday-multipart (~> 1.0, >= 1.0.4)
  figaro
  google-cloud-resource_manager
  google-cloud-storage
  google-cloud-tasks-v2
  google-iam-credentials-v1 (~> 0.4.0)
  hiredis (~> 0.6.3)
  inline_svg
  jbuilder (~> 2.7)
  jpmobile
  kaminari (~> 1.2.2)
  listen (~> 3.3)
  meta-tags (~> 2.1)
  mysql2
  newrelic-infinite_tracing (~> 9.2)
  newrelic_rpm (~> 9.2)
  nokogiri
  parallel (~> 1.11, >= 1.11.2)
  pry-byebug
  psych (~> 3.1)
  puma (~> 6.4, >= 6.4.3)
  rack (~> 3.0)
  rack-mini-profiler (~> 3.0)
  rack-utf8_sanitizer
  rails (~> *******)
  rails-controller-testing (~> 1.0, >= 1.0.5)
  rdoc (~> 6.3, >= *******)
  recaptcha
  redis (~> 4.8, >= 4.8.1)
  redis-namespace (~> 1.10)
  redis-rails (~> 5.0, >= 5.0.2)
  rsolr (~> 2.3.0)
  rsolr-ext (~> 1.0.3)
  rspec-rails (~> 6.0, >= 6.0.3)
  sass-rails (>= 6)
  selenium-webdriver
  shoulda-matchers (~> 5.3)
  simplecov (~> 0.22.0)
  slim-rails
  split (~> 4.0, >= 4.0.3)
  spring
  stackdriver
  turbolinks (~> 5)
  tzinfo-data
  uri (~> 0.13.2)
  web-console (>= 4.1.0)
  webdrivers
  webpacker (~> 5.0)
  zgcp_toolkit

RUBY VERSION
   ruby 3.2.2p53

BUNDLED WITH
   2.4.10
