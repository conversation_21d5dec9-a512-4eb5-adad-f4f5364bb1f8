.guide-title.d-flex.table
  .guide-table-structure.d-flex.gap-20
    .component.d-flex
      - contents.each do |block|
        .guide-table.d-flex data-controller='dropdown'
          .table-title.d-flex data-dropdown-target='title' data-action='click->dropdown#toggle'
            .table-text.font-guide
              = block[:title]
            .icon-btn data-dropdown-target='iconUp'
              = image_pack_tag 'icons/vector-up.svg', alt: 'arrow up', class: 'icon'
            .icon-btn data-dropdown-target='iconDown'
              = image_pack_tag 'icons/vector-down.svg', alt: 'arrow down', class: 'icon'
          .table-content.d-flex data-dropdown-target='content'
            .table-content__structure.d-flex
              .table-text__structure.d-flex.gap-5
                - block[:content].each do |line|
                  span.table-text-2.font-guide
                    = line[:sub_title]
                    span.table-text-3.font-guide
                      = line[:sub_content]
