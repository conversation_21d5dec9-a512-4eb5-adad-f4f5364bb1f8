.guide-title.d-flex data-controller='change-table'
  .guide-title.d-flex.content
    .guide-content-title.font-guide
      | Shipping Methods: What's your priority?
  .guide-title.d-flex.box-wrapper
    .guide-box-structure.d-flex
      .guide-box-list.d-flex
        .guide-box-item.d-flex data-change-table-target='button' data-action='click->change-table#handleClick'
          .box-item-text
            | Cost & Control
        .guide-box-item.d-flex data-change-table-target='button' data-action='click->change-table#handleClick'
          .box-item-text
            | Convenience
  - @recommended_vehicle_logistics.each do |recommended|
    .guide-title.d-flex.box-wrapper.d-none data-change-table-target='table'
      .guide-box-recommended.d-flex
        .recommended-title.font-guide
          = recommended[:title]
        .recommended-content.d-flex
          - recommended[:content].each do |content|
            .recommended-text.font-guide
              = content[:sub_title]
            .recommended-text__list.font-guide
              ul
                - content[:sub_content].each do |item|
                  li
                    = item
