.guide-title.d-flex.table
  .guide-table-structure.d-flex
    - @strategies_content.each do |section|
      .guide-table.d-flex data-controller='dropdown'
        .table-title.d-flex data-dropdown-target='title' data-action='click->dropdown#toggle'
          .table-text.font-guide = section[:title]
          .icon-btn data-dropdown-target='iconUp'
            = image_pack_tag 'icons/vector-up.svg', alt: 'arrow up', class: 'icon'
          .icon-btn data-dropdown-target='iconDown'
            = image_pack_tag 'icons/vector-down.svg', alt: 'arrow down', class: 'icon'
        .table-content.d-flex data-dropdown-target='content'
          - section[:content].each_with_index do |item, index|
            .table-content__structure.d-flex class="#{'border-bottom' unless index == section[:content].size - 1}"
              .table-text-1.font-guide = item[:sub_title]
              - item[:sub_content].each do |sub|
                .table-text
                  span.table-text-2.font-guide = sub[:sub_content_1]
                  span.table-text-3.font-guide
                    = sub[:sub_content_2]
                    - if sub[:sub_content_link]
                      span.guide-content-link.font-guide
                        = link_to sub[:sub_content_link], tcv_url(path: 'ja/region/all/'), class: 'mx-4'
