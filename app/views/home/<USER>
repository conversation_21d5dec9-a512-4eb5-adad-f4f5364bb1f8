.search-block
  h2.search-title
    span.ico.icon-search
    | Vehicles In Stock
  ul.search-content.pt-10
    - @vehicle_stock_not_kenya.each do |country|
      li.each-item
        = link_to country.pc_url, class: 'item-link ps-4 py-7'
          = internal_svg('symbol-flags', country.number, attributes = { width: 32, height: 21 })
          / 410 is korea country code
          .item-name.pt-2.ps-10 = country.number == 410 ? 'Korea' : country.name
