.guide-title.d-flex.checklist
  .guide-checklist.d-flex
    .guide-checklist__table.d-flex
      .table-column.d-flex
        .column-structure.d-flex
          .column-1.d-flex
            .column-text.font-guide = columns_name[0]
          .column-2.d-flex
            .column-text.font-guide = columns_name[1]
          .column-3.d-flex
            .column-text.font-guide = columns_name[2]
      .table-row.d-flex
        - rows_name.each do |row|
          .row-structure.d-flex
            .block-1.d-flex.flex-shrink-0
              .block-text-1.font-guide = row[:block_1]
            .block-2.d-flex.align-self-stretch.flex-shrink-0
              .block-text.font-guide = row[:block_2]
            .block-3.d-flex
              .block-text.font-guide = row[:block_3]
