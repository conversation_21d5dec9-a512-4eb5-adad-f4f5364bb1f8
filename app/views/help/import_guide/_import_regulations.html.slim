.guide-title.d-flex.content#item1
  .guide-content-title.title-main.font-guide
    | Understanding Import Regulations and Compliance
- @regulations.each_with_index do |content, index|
  .guide-title.d-flex.content-text
    .guide-content-text.font-guide
      = content
  - if index == 1
    .guide-title.d-flex.content-text
      .guide-content-link.font-guide
        = link_to tcv_url(path: 'region/all/')
          | Import procedures and regulations of each country
= render partial: 'help/import_guide/import_regulations_checklist', locals: { columns_name: checklist[:column_name], rows_name: checklist[:row_name] }
