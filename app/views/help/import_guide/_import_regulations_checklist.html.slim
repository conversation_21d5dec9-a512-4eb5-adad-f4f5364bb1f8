.guide-title.d-flex.checklist
  .guide-checklist.d-flex
    .guide-checklist__table.d-flex
      .table-column.d-flex
        .column-structure.d-flex
          .column-1.d-flex
            .column-text.font-guide = columns_name[0]
          .column-2.d-flex
            .column-text.font-guide = columns_name[1]
          .column-3.d-flex
            .column-text.font-guide = columns_name[2]
      .table-row.d-flex.height
        - rows_name.each do |row, index_rol|
          .row-structure.d-flex class="#{'flex-attribute align-self-stretch' if index_rol == 1 }"
            .block-1.d-flex
              .block-text-1.font-guide = row[:block_1]
            .block-2.d-flex.align-self-stretch
              .block-text.font-guide = row[:block_2]
            .block-3.d-flex
              .block-text.font-guide = row[:block_3]
