- set_meta_og_image_tags make_model_image_url(@model_groups)
.make-models-list data-controller='make-models'
  = render 'shared/main_breadcrumb'
  .container.stock-top-content
    nav.nav__search
      = render 'home/search_by_make'
    .main-list
      - if @maker_name
        p.search-result-text
          span Search result for
          | &nbsp;
          - if SYMBOL_MAKER_IDS.include?(@maker_id)
            svg.ico.icon-maker width="56" height="44" role="img" aria-labelledby="title"
              use xlink:href="#{resolve_path_to_image('icons/symbol-maker.svg')}?k=605c959c21#maker-#{@maker_id}"
            | &nbsp;
          span = @maker_name
      - if @err_message
        span.error-message.align-left = @err_message
      - else
        = link_to tcv_url(path: "used_car/#{@maker_name.downcase}/all/"), class: 'all-model-block' do
          span.text All Models&nbsp;
          span.number = "(#{@total_count})"
          span.ico.icon-chevron-right
        .nav-bar-block
          .grid-container
            - is_show_popular = @popular_models&.dig(:show) && @popular_models[:rankings].present?
            - popular_options = is_show_popular ? { class: 'link-scroll focus', data: { 'element-id' => "carmodel-group-popular-models", action: 'click->make-models#scrollToGroup' } } : { class: 'link-scroll focus disabled' }
            = link_to 'javascript:void(0)', popular_options
              .grid-item
                .ico.icon-expand-more
                | Popular Models
            - @dictionary_groups.each do |group_name|
              - is_model_group_present = @model_groups[group_name].present?
              - options = is_model_group_present ? { class: 'link-scroll', data: { 'element-id' => "carmodel-group-#{group_name.downcase}", action: 'click->make-models#scrollToGroup' } } : { class: 'link-scroll disabled' }
              = link_to 'javascript:void(0)', options
                .grid-item
                  .ico.icon-expand-more
                  = group_name

        .each-block
          / Get popular models
          - if @popular_models&.dig(:show) && @popular_models[:rankings].present?
            .section-carmodel-default
              h2.block-title(id='carmodel-group-popular-models')
                span.text Popular Models
              .popular-gird
                - @popular_models[:rankings].each_with_index do |ranking, index|
                  - valid_name = @models&.find { |model| model[:id] == ranking.model_id }
                  - model_name = valid_name&.dig(:name)
                  - model_stock = valid_name&.dig(:stock)
                  - if model_name.present?
                    = link_to tcv_url(path: "used_car/#{@maker_name.downcase}/#{escape_param(model_name.downcase)}/"), class: 'car-item' do
                      = pc_make_model_ranking_image_tag(@ranking_images, ranking, "#{@maker_name} #{model_name}")
                      .car-link
                        .car_model
                          span.name = model_name
                          | &nbsp;
                          span.number
                            = "(#{model_stock})"
                        .ico.icon-expand-more

          / - get model dictionary
          - @model_groups&.each do |group, items|
            .section-carmodel
              h2.block-title(id="carmodel-group-#{group.downcase}")
                span.text = group
              - sub_model_groups = grouping_model(items)
              - sub_model_groups.each do |sub_group_name, models|
                - unless sub_group_name == '#'
                  .alphabe-title = sub_group_name
                .dictionary-gird
                  - models.each do |item|
                    = link_to tcv_url(path: "used_car/#{@maker_name.downcase}/#{escape_param(item[:name].downcase)}/"), class: 'car-item' do
                      = make_model_image_tag(item[:image_url], "#{@maker_name} #{item[:name]}")
                      .car-link
                        .car_model
                          span.name = item[:name]
                          | &nbsp;
                          span.number
                            = "(#{item[:stock]})"
                        .ico.icon-expand-more
