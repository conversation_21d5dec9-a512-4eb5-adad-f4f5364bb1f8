section.car__price-block
  .currency__unit-area
    h2.currency__unit-ttl  Car Price (FOB)
    .car__price-body
      span.me-8 = @car_presenter.display_fob_price
      = show_discount_car(@discount_percent['DiscountRate1Month'])

.accordion-block
  .ac
    .js-accordion.ac-header
    = text_field_tag nil, 'undefined', id: :ac_panel, class: 'd-none'
    .ac-panel
      section.calculate__est_area data-car-target='boxSelectCountry' data-estimated=ship_estimated?
        h2.calculate__area-ttl Calculate your Total Price
        .calculate__area-body
          dl.dest__select-block
            dt.dest__select-ttl
              label#your_country Country
            dd.dest__select-body
              span.dest__select-wrap.select__wrapper
                = text_field_tag nil, :undefined, id: :ett_select_country, class: 'd-none', autocomplete: 'off'
                = select_tag :select_your_country, options_for_select(@list_country_can_shipestimate.unshift(['Please Select', 0]), current_est_location[:country]&.number),
                  { class: 'select--inner',
                    id: 'sl_country',
                    'data-car-target': 'countrySelectOptions',
                    'data-action': 'change->car#loadPortInfo',
                    autocomplete: 'off',
                    'aria-labelledby': 'your_country' }

            dt.dest__select-ttl
              label#your_port Nearest port
            dd.dest__select-body
              span.dest__select-wrap.select__wrapper
                = text_field_tag nil, :undefined, id: :ett_select_port, class: 'd-none', autocomplete: 'off'
                = select_tag :select_your_port, options_for_select({}),
                  { prompt: 'Please Select',
                    class: 'select--inner',
                    disabled: 'disabled',
                    id: 'sl_port',
                    'data-port-selected': current_est_location[:port_id],
                    'data-car-target': 'portSelectOptions',
                    'data-action': 'change->car#toggleCheckbox',
                    autocomplete: 'off',
                    'aria-labelledby': 'your_port' }
          = text_field_tag nil, :undefined, id: :ett_country_port, class: 'd-none', autocomplete: 'off'
          = text_field_tag nil, :undefined, id: :ett_tradetpots, class: 'd-none'
          ul.calculate__checkbox-block.fl-left
            - hide_shp_class = 'd-none' unless @checkbox_price_options&.dig(:shipping)
            li.checkbox-item class=hide_shp_class data-car-target='checkboxShipping'
              span.custom-cb
                = check_box_tag :total_price_checkbox_shipping, nil, shipping_checked?, { disabled: !@checkbox_price_options&.dig(:shipping), id: 'dt_checkbox_shipping', 'data-action': 'change->car#handleChangeTradetpots' }
                label for='dt_checkbox_shipping'
                  | Shipping
                  button.ico.icon-help.icon-blue.tooltip-btn type='button' data-action='click->car#openTooltip'
                  .checkbox-help-tooltip.tooltip-block data-car-target='tooltipBlock'
                    button.ico.icon-close type='button'
                    p = t('estimate_price_help_text.shipping')
            - hide_ins_class = 'd-none' unless @checkbox_price_options&.dig(:insurance)
            li.checkbox-item class=hide_ins_class data-car-target='checkboxInsurance'
              span.custom-cb
                = check_box_tag :total_price_checkbox_shipping, nil, insurance_checked?, { disabled: !@checkbox_price_options&.dig(:insurance), id: 'dt_checkbox_insurance', 'data-action': 'change->car#handleChangeTradetpots' }
                label for='dt_checkbox_insurance'
                  | Insurance
                  button.ico.icon-help.icon-blue.tooltip-btn type='button' data-action='click->car#openTooltip'
                  .checkbox-help-tooltip.tooltip-block data-car-target='tooltipBlock'
                    button.ico.icon-close type='button'
                    p = t('estimate_price_help_text.insurance')
            - hide_insp_class = 'd-none' unless @checkbox_price_options&.dig(:pre_ship_inspection)
            li.checkbox-item class=hide_insp_class data-car-target='checkboxInspection'
              span.custom-cb
                = check_box_tag :total_price_checkbox_shipping, nil, pre_ship_inspection_checked?, { disabled: !@checkbox_price_options&.dig(:pre_ship_inspection), id: 'dt_checkbox_inspection', 'data-action': 'change->car#handleChangeTradetpots' }
                label for='dt_checkbox_inspection'
                  | Pre-ship inspection
                  span.symbol_asterisk data-car-target='symbolAsterisk' *
                  button.ico.icon-help.icon-blue.tooltip-btn type='button' data-action='click->car#openTooltip'
                  .checkbox-help-tooltip.tooltip-block data-car-target='tooltipBlock'
                    button.ico.icon-close type='button'
                    p = t('estimate_price_help_text.inspection')
          button.vehicle-search-btn--general.vehicle-search-modal-btn[
            disabled=true
            type='button'
            data-car-target='btnCalculate'
            data-action='click->car#calculateEstTotalPrice click->car#enableFinanceTab'
          ]Calculate

    - display_class = ship_estimated? ? { estimated: 'd-block', not_estimate: 'd-none' } : { estimated: 'd-none', not_estimate: 'd-block' }
    = text_field_tag nil, :undefined, id: :ett_incoterm_port, class: 'd-none', autocomplete: 'off'
    .total__price_result-area.d-flex
      h2.price__result-ttl Estimated Total Price
      .price__result-block class=display_class[:estimated] data-car-target='elEstimated'
        p.price__result-body.total-price
          span data-car-target='displayTotalPriceEl'
        p.price__result-body.send__suggest-txt ※The price is not fixed. Let's try to negotiate!
        dl.selection__detail-list.d-flex
          dt.selection__detail-item-ttl Selected :
          dd.selection__detail-item
            span.select-item data-car-target='incoterm' = display_incoterms
          dt.selection__detail-item-ttl Nearest port :
          dd.selection__detail-item data-car-target='nearestPort' = current_port&.port_name
        .insurance-alert.mb-4 data-car-target='insuranceAlert'
        button.btn__change-dest.ac-trigger type='button' data-car-target='btnChangeDest'
          span.ico.icon-cached.me-3
          | Change destination
        .send-contact-seller-btn.ask-btn data-action='click->car#handleOfferWithUserAuth'
          | Ask the best price
      .price__result-block class=display_class[:not_estimate] data-car-target='elNotEstimated'
        p.price__result-body.lh-18
          | Select your Country &
          br
          | Nearest Port
        .insurance-alert data-car-target='insuranceAlert'
