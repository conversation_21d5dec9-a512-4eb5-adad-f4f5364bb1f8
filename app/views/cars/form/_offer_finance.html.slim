.total__price_result-area.d-flex
  p.price__result-bnpl-ttl 50% Due Today
  .price__result-block.d-block data-car-target='elEstimatedBNPL'
    p.price__result-body.bnpl-price
      span data-car-target='displayBNPLPriceEl'
    .price__result-bnpl-descr
      p Pay 50% today and
      p pay the rest when receiving the Vehicle
      = bnpl_learn_more_tag('link--simple')
    dl.selection__detail-list.d-flex
      dt.selection__detail-item-ttl Selected :
      dd.selection__detail-item
        span.select-item data-car-target='incoterm' = display_incoterms
      dt.selection__detail-item-ttl Nearest port :
      dd.selection__detail-item data-car-target='nearestPort' = current_port&.port_name
    .send-contact-seller-btn.ask-btn data-action='click->car#handleOfferWithUserAuth'
      | Ask the best price
