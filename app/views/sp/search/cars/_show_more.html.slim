.show-more-content
  .accordion-sell-point
    - if valid_options.any?
      h3 Selling Points
      ul.option-list
        - valid_options.each do |option|
          li = option.name
  .accordion-information
    h3 Selling Information
    .box-line
      span.selling-info
        span.dealer-name = additional_car_info[:DealerName].upcase
        .seller-name__icons
          .total Total Score
          .grade-box
            svg.ico.icon-stars width='88' height='15' role='img' aria-labelledby='title'
              use xlink:href='#{resolve_path_to_image('icons/symbol-defs.svg')}?#icon-star#{additional_car_info[:FeedbackTotal].to_s.sub('.', '_').presence || '0_0'}'
          span.dealer-score
            = "#{additional_car_info[:FeedbackTotal].to_s.presence || '0.0'}"
      span.accord-img
        = seller_award_icon(additional_car_info[:SellerAwardIconName].presence)
