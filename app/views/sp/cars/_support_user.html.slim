.support-link-block
  p.title
    | To Our User
  ul.link-list
    - if support_links_info[:region_link].present?
      li.each-item
        = link_to support_links_info[:region_link], class: 'item-link' do
          span.content #{support_links_info[:region_name]}'s Region Page
          span.ico.icon-chevron-right
    - if support_links_info[:support_center].present?
      li.each-item
        = link_to support_links_info[:support_center], class: 'item-link' do
          span.content Customer Support Page
          span.ico.icon-chevron-right
    li.each-item
      = link_to support_links_info[:how_to_buy], class: 'item-link' do
        span.content How to Buy
        span.ico.icon-chevron-right
