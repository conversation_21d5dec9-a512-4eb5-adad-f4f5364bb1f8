.title-detail Vehicle infomation
table.vehicle-infomation
  tbody.vehicle-infomation__body
    tr
      th.title.fs-16
        span
          | 50% Due Today
      td.content
        label.bnpl-price.table-label.fs-24 data-car-target='displayBNPLPriceEl'
        .price-block.pt-5.d-block data-car-target='priceEsimatedBlock'
          p Pay 50% today and
          p pay the rest when receiving the Vehicle
          = bnpl_learn_more_tag('link--simple')
          = text_field_tag nil, :undefined, id: :ett_incoterm_port, class: 'd-none'
          ul.suggest-text-wrapper
            li.sugest-text-row
              dl.suggest-text
                dt.item Selected:
                dd.description
                  span.total-price-incoterms data-car-target='incoterm' = display_incoterms
            li.sugest-text-row
              dl.suggest-text
                dt.item Nearest port:
                dd.description data-car-target='nearestPort' = current_port&.port_name
