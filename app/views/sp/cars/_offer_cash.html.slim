- display_class = ship_estimated? ? { estimated: 'd-block', not_estimate: 'd-none' } : { estimated: 'd-none', not_estimate: 'd-block' }
.title-detail.text-dark Vehicle infomation
table.vehicle-infomation
  tbody.vehicle-infomation__body
    tr
      th.title
        span
          | Car Price
          br
          | (FOB)
      td.content
        .have-discount-percent
          label.table-label.me-8
            = @car_presenter.display_fob_price
          = show_discount_car(@discount_percent['DiscountRate1Month'])
        - if @car_presenter.show_market_price?
          .d-flex
            .special-price-wrapper.mt-4
              .special-price-text SPECIAL PRICE
            button.ico.icon-help.icon-blue.tooltip-btn type='button' data-action='click->car#showSpecialPricePopup'
    tr.total-price-area
      th.title Total Price
      td.content
        .price-block class=display_class[:estimated] data-car-target='priceEsimatedBlock'
          = text_field_tag nil, :undefined, id: :ett_incoterm_port, class: 'd-none', autocomplete: 'off'
          label.table-label.price-text data-car-target='displayTotalPriceEl'
          ul.suggest-text-wrapper
            li.sugest-text-row
              dl.suggest-text
                dt.item Selected:
                dd.description
                  span.total-price-incoterms data-car-target='incoterm' = display_incoterms
            li.sugest-text-row
              dl.suggest-text
                dt.item Nearest port:
                dd.description data-car-target='nearestPort' = current_port&.port_name
        .price-block class=display_class[:not_estimate] data-car-target='priceNotEsimatedBlock'
          label.table-label data-car-target='displayTotalPriceEl'
            span.price--undefined
          p.message Select your Country & Nearest Port
        .insurance-alert data-car-target='insuranceAlert'
        .change-info-area.accordion-wrapper
          .ac
            p.change-info-text.accordion-js.ac-header.ac-trigger
              span.arrow-right.me-3
              | Change Destination or Currency
            .change-country-port-area.ac-panel
              dl.destination-list
                dt.logi-fl Country:
                dd.logi-fl
                  = text_field_tag nil, :undefined, id: :ett_select_country, class: 'd-none', autocomplete: 'off'
                  = select_tag :select_your_country, options_for_select(@list_country_can_shipestimate.unshift(['Please Select', 0]), current_est_location[:country]&.number),
                    { id: 'select_your_country',
                      'data-car-target': 'countrySelectOptions',
                      'data-action': 'change->car#loadPortInfo',
                      autocomplete: 'off' }
                dt.logi-fl Nearest port:
                dd.logi-fl
                  = text_field_tag nil, :undefined, id: :ett_select_port, class: 'd-none', autocomplete: 'off'
                  = select_tag :select_your_port, options_for_select({}),
                    { prompt: 'Please Select',
                      id: 'select_your_port',
                      disabled: 'disabled',
                      'data-port-selected': current_est_location[:port_id],
                      'data-car-target': 'portSelectOptions',
                      'data-action': 'change->car#toggleCheckbox',
                      autocomplete: 'off' }
              .select-country-port-warning.d-none data-car-target='elNotEstimated'
                span.d-inline
                  span.ico.icon-warning.me-4
                  | Select your Country & Nearest Port
              .currency-text Currency:
              = select_tag :currency, options_for_select(CURRENCY_MAPPINGS.map { |c| [c[:ascii_name], c[:id]] }, current_exrate_id),
                { class: 'currency-select mb-14', data: { action: 'change->car#changeCurrency', 'car-target': 'selectCurrency' } }
              = text_field_tag nil, :undefined, id: :ett_country_port, class: 'd-none', autocomplete: 'off'
              = text_field_tag nil, :undefined, id: :ett_tradetpots, class: 'd-none'
              ul.checkbox-area.mb-16
                - hide_shp_class = 'd-none' unless @checkbox_price_options&.dig(:shipping)
                li.cb class=hide_shp_class data-car-target='checkboxShipping'
                  span.custom-cb.option-checkbox
                    = check_box_tag :total_price_checkbox_shipping, nil, shipping_checked?, { disabled: !@checkbox_price_options&.dig(:shipping), id: 'dt_checkbox_shipping', 'data-action': 'change->car#handleChangeTradetpots' }
                    label for='dt_checkbox_shipping'
                      | Shipping
                - hide_ins_class = 'd-none' unless @checkbox_price_options&.dig(:insurance)
                li.cb class=hide_ins_class data-car-target='checkboxInsurance'
                  span.custom-cb.option-checkbox
                    = check_box_tag :total_price_checkbox_shipping, nil, insurance_checked?, { disabled: !@checkbox_price_options&.dig(:insurance), id: 'dt_checkbox_insurance', 'data-action': 'change->car#handleChangeTradetpots' }
                    label for='dt_checkbox_insurance'
                      | Insurance
                - hide_insp_class = 'd-none' unless @checkbox_price_options&.dig(:pre_ship_inspection)
                li.cb class=hide_insp_class data-car-target='checkboxInspection'
                  span.custom-cb.option-checkbox
                    = check_box_tag :total_price_checkbox_shipping, nil, pre_ship_inspection_checked?, { disabled: !@checkbox_price_options&.dig(:pre_ship_inspection), id: 'dt_checkbox_inspection', 'data-action': 'change->car#handleChangeTradetpots' }
                    label for='dt_checkbox_inspection'
                      | Pre-ship inspection
                      span.note data-car-target='symbolAsterisk'
              .button-calculate
                .button-calculate__wrapper
                  = link_to 'javascript:;', 'data-car-target': 'btnCalculate', 'data-action': 'click->car#calculateEstTotalPrice click->car#enableFinanceTab', 'disabled': 'true' do
                    span.ico.icon-calculator
                    button.calculate-btn Calculate
