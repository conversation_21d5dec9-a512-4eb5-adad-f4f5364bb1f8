- @logistics.each_with_index do |logistic, index|
  .guide-title.d-flex.content id="#{'item2' if index == 0}"
    .guide-content-title.font-guide class="#{'title-main' if index == 0}"
      = logistic[:title]
  - logistic[:content].each do |content|
    .guide-title.d-flex.content-text
      .guide-content-text.font-guide
        = content
.guide-title.d-flex.content
  .guide-content-title.font-guide
    | RoRo vs. Container Shipping: Which Should You Choose?
.checklist-container
  = render partial: 'sp/help/import_guide/vehicle_logistics_checklist',
            locals: { first_column_name: checklist[:column_name][0], rest_columns_name: checklist[:column_name][1..], rows_name: checklist[:row_name] }
= render 'shared/help/import_guide/recommended_vehicle_logistics'
