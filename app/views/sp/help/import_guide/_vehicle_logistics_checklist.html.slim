- rest_columns_name.each_with_index do |column, index|
  .guide-title.d-flex.checklist
    .guide-checklist.d-flex
      .guide-checklist__table.d-flex
          .table-column.d-flex
            .column-structure.d-flex
              div class="column-1 d-flex"
                .column-text.font-guide = first_column_name
              div class="column-2 d-flex"
                .column-text.font-guide = column
          - rows_name.each do |row|
            .table-row.d-flex.height-100
              .row-structure.d-flex class="flex-attribute align-self-stretch"
                .block-1.d-flex
                  .block-text-1.font-guide = row[:block_1]
                .block-2.d-flex.align-self-stretch
                  .block-text.font-guide = row["block_#{index + 2}".to_sym]
