class HelpController < ApplicationController
  include Metas::Help
  before_action :prepare_data, only: :import_guide

  def import_guide
    fetch_master_data

    prepare_template
  end

  private

  def fetch_master_data
    data = MasterInfo::ImportGuide.data
    @benefits_content = data[:benefits_content]
    @imports_content = data[:imports_content]
    @documents_content = data[:documents_content]
    @strategies_content = data[:strategies_content]
    @checklist = data[:checklist]
    @cost_payment = data[:cost_payment]
    @documents = data[:documents]
    @regulations = data[:regulations]
    @logistics = data[:logistics]
    @recommended_vehicle_logistics = data[:recommended_vehicle_logistics]
  end
end
