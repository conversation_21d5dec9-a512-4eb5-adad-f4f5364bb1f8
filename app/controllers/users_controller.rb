class UsersController < ApplicationController
  def signin
    response = ::Users::Signin.exec(signin_params[:email], signin_params[:password])

    return handle_successful_signin(response) if response['status'] == 200

    render json: { msg: error_messages.internal_error_msg }, status: :internal_server_error
  end

  def signup
    recaptcha_result = verify_recaptcha(secret_key: Settings.recaptcha_secretkey, response: signup_params[:recaptcha_response])
    return render json: { msg: error_messages.invalid_captcha_msg }, status: :bad_request unless recaptcha_result

    response = ::Users::Signup.new(signup_params).create

    return handle_successful_signup(response) if response['status'] == 200

    render json: { msg: error_messages.internal_error_msg }, status: :internal_server_error
  end

  def shortcutmember
    result = ::Users::ShortcutMember.new(cookies['CVPD']).create

    return render json: { msg: error_messages.fail_shortcut_member_msg }, status: :bad_request unless result

    render json: { result: result }, status: :ok
  end

  def user_signed_in
    render json: { is_signed_in: user_signed_in? }, status: :ok
  end

  private

  def signin_params
    @signin_params ||= params.permit(:email, :password, :remember_me)
  end

  def signup_params
    @signup_params ||= params.permit(:email, :country_code, :number, :purpose, :password, :country, :recaptcha_response)
  end

  def handle_successful_signin(response)
    user_id = response&.dig('UserID')&.to_i

    if user_id&.positive?
      save_cvpd(response.merge(remember_me: ActiveModel::Type::Boolean.new.cast(signin_params[:remember_me])))
      return render json: { user: response, temporary_contact_id: params.permit(:ti) }, status: :ok
    end

    render json: { msg: error_messages.wrong_credentials_msg }, status: :unauthorized
  end

  def handle_successful_signup(response)
    user_id = response&.dig('UserID')&.to_i

    if user_id&.positive?
      save_cvpd(response)
      return render json: { user: response }, status: :created
    end

    render json: { msg: error_messages.existing_user_msg }, status: :unprocessable_entity
  end

  def error_messages
    @error_messages ||= Settings.tcv_core_api.error_messages
  end
end
