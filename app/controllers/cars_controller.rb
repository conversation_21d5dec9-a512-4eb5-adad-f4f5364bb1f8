class CarsController < ApplicationController
  NEW_STOCK_VAL = '1'.freeze
  include Metas::Cars
  include SearchBreadcrumbs

  before_action :prepare_data, :fetch_car, :set_car_breadcrumbs, only: [:show]
  before_action :tcv_user_authentication!, :add_favorite_callback, :favorites_merge_fgkey, only: [:show]
  before_action :require_xhr_request,
                only: %i[catalog seller_info fetch_inquiries_counter fetch_modals fetch_recommend_cars show_more]

  def show
    raise_error_404 if @car_stock.blank?

    @account_state = ::Cars::AccountState.new(
      car: @car_stock,
      current_user: current_user,
      is_login: user_signed_in?,
      is_new: params[:isNew],
      is_preview: params[:pr].to_i == 1,
      is_smart_phone: smart_phone?,
    ).fetch

    @car_presenter = CarStockPresenter.new(@car_stock, current_currency, @master_make, @master_model)

    if smart_phone?
      @car_options = MasterInfo::CarOptionGroup.all.map do |group|
        [group.name, group.option_list]
      end
    else
      @car_option_groups = MasterInfo::CarOptionGroup.all
    end

    @h1_content = h1_content
    @list_country_can_shipestimate = Rails.cache.fetch('list_country_can_shipestimate', expires_in: 3.hours) do
      MCountry.list_country_can_shipestimate.pluck(:country, :number).uniq.sort
    end
    @checkbox_price_options = ::Price::EstimateTotal::Options.new(current_est_location[:country]&.number).all if ship_estimated?
    reset_tradetpopts(@checkbox_price_options) if @checkbox_price_options
    save_browshistry_into_cookies(@car_stock['itemId'])
    Measurement::ViewCounter.new(@car_stock, cookies, user_signed_in?, current_user).exec

    @discount_percent = ::Search::CarDiscountService.new(@car_presenter.car[:itemId]).call

    @internal_links = Cars::InternalLinks.new(@master_make, @master_model, @car_stock['modelCode']).call
    terminate_date = @car_stock['terminateDate'].in_time_zone(JST_TIME_ZONE)
    status_code = terminate_date.present? && DateTime.now > terminate_date ? 410 : nil
    prepare_template(status_code: status_code)
  end

  def show_more
    # Option lists: https://github.com/ZIGExN/tcv-web-v2/issues/56
    # 45: Non-smoker
    # 41: No accident history
    # 42: One owner
    # 38: Maintenance Records Available
    # 37: Fully loaded
    # 43: Performance tires
    # Sort the options base on the order specified

    api_url = "#{Settings.tcv_core_api.domain}#{Settings.tcv_core_api.v1_paths.stocks}/#{params[:car_id]}"
    car_response = ::TcvCoreApi::Request.new(url: api_url).send do |request|
      request.headers['X-Tcv-Zigexn-CVPD'] = ::CvpdCookies.escape(cookies['CVPD']) if cookies['CVPD']
    end

    acceptable_option_ids = [45, 41, 42, 38, 37, 43]
    valid_option_ids = car_response.body['vehicleOption'] & acceptable_option_ids
    valid_options = MasterInfo::CarOption.where(id: valid_option_ids).sort_by { |option| acceptable_option_ids.index(option.id) }

    additional_car_info = {
      DealerName: params[:dealer],
      FeedbackTotal: params[:feed],
      SellerAwardIconName: params[:award]
    }
    render partial: 'sp/search/cars/show_more', layout: false, locals: { additional_car_info:, valid_options: }
  end

  def catalog
    return head :bad_request if !params[:model_id] || !params[:model_year] || !params[:trim_name]

    @catalog_result = Rails.cache.fetch("car_catalog_#{params[:model_id]}_#{params[:model_year]}_#{params[:trim_name]}", expires_in: 3.hours) do
      Cars::CatalogBuildUrl.new(params[:model_id], params[:model_year], params[:trim_name]).call
    end
    template = smart_phone? ? 'sp/cars/catalog' : 'cars/catalog'

    render partial: template, layout: false, status: :ok
  end

  def seller_info
    return head :bad_request if !params[:allow_inquiry] || !params[:seller_id]

    @allow_inquiry = ActiveModel::Type::Boolean.new.cast(params[:allow_inquiry])
    @seller_info = ::SellerInfoPresenter.new(params[:seller_id], params[:seller_name]).compose

    if smart_phone?
      render json: { seller_info: render_to_string(partial: 'sp/cars/seller_info', layout: false).html_safe }, status: :ok
    else
      render json: {
        seller_info: render_to_string(partial: 'cars/seller_info', layout: false).html_safe,
        seller_rating: render_to_string(partial: 'cars/form/seller_name_and_rating', layout: false).html_safe
      }, status: :ok
    end
  end

  def fetch_inquiries_counter
    car_id = params[:car_id].to_i
    inquiry_counter = Inquiries::OfferCount.new([car_id], cookies['CVPD']).exec
    @inquiry_counter_car = inquiry_counter[car_id].to_i

    render partial: 'cars/inquires_counter', layout: false, status: :ok
  end

  def fetch_modals
    @country_list = Rails.cache.fetch('prepare_search_data_country_list', expires_in: 1.hour) do
      MCountry.country_for_select_options.pluck(:number, :country, :country_code)
    end

    @current_country_code = user_country_code

    render json: {
      modal_signin: render_to_string(partial: 'search/modals/modal_signin', layout: false).html_safe,
      modal_signup: render_to_string(partial: 'search/modals/modal_signup', layout: false).html_safe
    }, status: :ok
  end

  def fetch_recommend_cars
    @recommend_cars = Cars::RecommendCars.new(params, current_currency).call
    @recommend_car_ids = @recommend_cars.map { |car| car['ItemID'] }
    @allow_inquiry = ActiveModel::Type::Boolean.new.cast(params[:allow_inquiry])

    render partial: 'sp/cars/recommend_cars/index', layout: false, status: :ok
  end

  private

  # TODO: implement detail page
  def search_params
    { queries: "ItemID:#{params[:car_id]}" }
  end

  def fetch_car
    return unless params[:car_id]

    raise_error_404 unless ::Search::ParamsService::NUMBER_REGEX === params[:car_id]

    api_url = "#{Settings.tcv_core_api.domain}#{Settings.tcv_core_api.v1_paths.stocks}/#{params[:car_id]}"
    @car_response = ::TcvCoreApi::Request.new(url: api_url).send do |request|
      request.headers['X-Tcv-Zigexn-CVPD'] = ::CvpdCookies.escape(cookies['CVPD']) if cookies['CVPD']
    end

    return render_gone_error if @car_response.status == 410

    raise_error_404 unless @car_response.success?

    @car_stock = @car_response.body
    return redirect_to new_stock_path, status: :moved_permanently if @car_stock['newStockUrl'].present?

    @master_make = MasterMake.find_by(id_make: @car_stock['makeId'])
    @master_model = MasterModel.find_by(id_model: @car_stock['modelId'])
    make_name = @master_make&.vc_name_e&.downcase
    model_name = @master_model&.vc_name_e&.downcase

    raise_error_404 if make_name.nil? || model_name.nil?

    redirect_url = "/used_car/#{escape_param(make_name)}/#{escape_param(model_name)}/#{params[:car_id]}/"
    return redirect_to redirect_url, status: :moved_permanently if params[:isNew] == NEW_STOCK_VAL

    @allow_inquiry = Cars::ConditionChecking.new(@car_stock, user_signed_in?, current_user).allow_inquiry?
    return if params[:make]&.downcase == make_name && params[:model]&.downcase == model_name

    redirect_to redirect_url, status: :moved_permanently
  end

  def raise_error_404
    raise ActiveRecord::RecordNotFound
  end

  def new_stock_path
    @car_stock['newStockUrl'].gsub('+', '%20')
  end
end
