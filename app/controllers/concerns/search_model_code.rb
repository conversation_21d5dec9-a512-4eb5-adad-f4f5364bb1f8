module SearchModelCode
  def redirect_model_code_keyword
    trim = TTrim.valid.by_pattern(params[:fd]).first
    return if trim.blank?

    make = trim.make_vc_name_e&.downcase || 'all'
    model = trim.model_vc_name_e&.downcase || 'all'
    make_model_code = ("#{make}_#{params[:fd].upcase}" unless make == 'all' && model == 'all')
    model_code_path = search_path(make: make, model: model, make_model_code: make_model_code)

    query_params = params.except(:make, :model, :make_model_code, :controller, :action, :fd).to_unsafe_h.compact
    query_string = query_params.any? ? "?#{query_params.to_query}" : ''

    redirect_to "#{model_code_path}/#{query_string}"
  end

  def setup_model_code_data
    return if invalid_model_code?

    @car_model_code = parse_model_code(@car_make, @car_model, params[:make_model_code])
  end

  def parse_model_code(make, model, make_model_code)
    raw_model_code = make_model_code.delete_prefix("#{make.vc_name_e.downcase}_")
    TTrim.valid
      .by_model_id(model.id_model)
      .by_pattern(raw_model_code)
      .first
  end

  private

  def invalid_model_code?
    !params[:make_model_code] || !make_model_valid? || params[:make_model_code].exclude?(@car_make.vc_name_e.downcase)
  end
end
