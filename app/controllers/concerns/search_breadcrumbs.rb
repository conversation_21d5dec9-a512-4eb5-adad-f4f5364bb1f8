# frozen_string_literal: true

module SearchBreadcrumbs
  extend ActiveSupport::Concern

  included do
    helper_method :breadcrumbs
    helper_method :search_path_with_trailing_slash
  end

  def breadcrumbs
    @breadcrumbs ||= []
  end

  def add_breadcrumb(name, path = nil)
    breadcrumbs << Breadcrumb::Base.new(name, path)
  end

  def set_make_model_breadcrumbs(make_name, path)
    @breadcrumbs = []
    setup_root_segment
    add_breadcrumb(make_name, path)
  end

  def set_error_breadcrumbs
    # clear all breadcrumbs before
    @breadcrumbs = []
    setup_root_segment
    add_breadcrumb('Error Page', '/errorpage')
  end

  def set_breadcrumbs
    # Enable only used_car router for testing
    return if params[:make].blank? || params[:model].blank?

    set_pgcl_info_into_cookies if kenya?

    setup_root_segment

    setup_make_segment if make_valid?
    setup_model_segment if make_model_valid?
    setup_trailing_segment if params[:make_model_code].blank?
  end

  def registration_year_breadcrumbs(master_make, master_model)
    validate_param_numeric(:fid, :jid, :smo, :emo)
    return if params[:fid].blank? && params[:jid].blank?

    name = ''
    name += "#{master_make.vc_name_e} " if master_make
    name += "#{master_model.vc_name_e} " if master_model

    if params[:fid]
      name += params[:fid]
      name += "/#{params[:smo]}" if params[:smo]
    end

    name += '-'

    if params[:jid]
      name += params[:jid]
      name += "/#{params[:emo]}" if params[:emo]
    end

    path = unless smart_phone?
             search_path_with_trailing_slash(make: params[:make], model: escape_param(params[:model]),
                                             fid: params[:fid], smo: params[:smo],
                                             jid: params[:jid], emo: params[:emo])
           end

    add_breadcrumb(name, path) if name.present?
  end

  def set_car_breadcrumbs
    set_pgcl_info_into_cookies if kenya?

    make_name = @master_make&.vc_name_e
    model_name = @master_model&.vc_name_e
    model_year = @car_stock['modelYear']

    setup_root_segment
    add_breadcrumb(make_name, search_path_with_trailing_slash(make: make_name&.downcase, model: 'all'))
    add_breadcrumb("#{make_name} #{model_name}",
                   search_path_with_trailing_slash(make: make_name&.downcase,
                                                   model: escape_param(model_name&.downcase)))
    add_breadcrumb("#{make_name} #{model_name} #{model_year}",
                   search_path_with_trailing_slash(make: make_name&.downcase,
                                                   model: escape_param(model_name&.downcase),
                                                   fid: model_year, jid: model_year))
    add_breadcrumb("#{make_name} #{model_name} #{model_year} #{@car_stock['trimName']} #{@car_stock['sellerName']}")
  end

  def validate_param_numeric(*name)
    name.each do |param_name|
      value = params[param_name]

      value.to_s.strip.match(Search::ParamsService::NUMBER_REGEX) ? params[param_name] = value.to_i.to_s : params.delete(param_name)
    end
  end

  def search_path_with_trailing_slash(params)
    uri = URI.parse(search_path(params))
    query_string = uri.query
    uri.query = nil
    uri.path += '/'

    CGI.unescape(uri.to_s + (query_string.present? ? "?#{query_string}" : ''))
  end

  def add_model_code_breadcrumbs(car_count)
    return if params[:make_model_code].blank?

    if car_count.zero?
      @breadcrumbs = []
      setup_root_segment
      add_breadcrumb("Page #{params[:pn] ? params[:pn].to_i + 1 : 1}") unless smart_phone?
      return
    end

    setup_model_code_segment
    setup_trailing_segment
  end

  private

  def kenya?
    params[:pgcl]&.to_i == MCountry::KENYA_COUNTRY_CODE
  end

  def setup_root_segment
    add_breadcrumb(kenya? ? 'TCV | Kenya Used Cars for Sale' : 'TCV | japan used car/japanese used car', root_path)
  end

  def setup_make_segment
    @tdk_make_name = @car_make.vc_name_e
    add_breadcrumb(@tdk_make_name, search_path_with_trailing_slash(make: params[:make], model: 'all'))
  end

  def setup_model_segment
    @tdk_model_name = @car_model.vc_name_e
    add_breadcrumb("#{@tdk_make_name} #{@tdk_model_name}",
                   search_path_with_trailing_slash(make: params[:make], model: escape_param(params[:model])))
  end

  def setup_model_code_segment
    tdk_model_code = @car_model_code&.model_code_show_text(params['make_model_code'])
    return if tdk_model_code.blank?

    add_breadcrumb(tdk_model_code,
                   search_path_with_trailing_slash(make: params[:make],
                                                   model: escape_param(params[:model]),
                                                   make_model_code: "#{@tdk_make_name.downcase}_#{tdk_model_code}"))
  end

  def setup_trailing_segment
    registration_year_breadcrumbs(@car_make, @car_model)
    add_breadcrumb("Page #{params[:pn] ? params[:pn].to_i + 1 : 1}") unless smart_phone?
  end
end
