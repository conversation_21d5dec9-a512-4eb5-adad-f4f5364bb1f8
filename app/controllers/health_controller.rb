class HealthController < ApplicationController
  # Skip các filter kh<PERSON><PERSON> c<PERSON><PERSON> thi<PERSON><PERSON> cho health check
  skip_before_action :verify_authenticity_token
  skip_before_action :prepare_data, if: :skip_prepare_data?
  
  def schema_cache
    begin
      result = Monitoring::SchemaCacheMonitor.health_check_endpoint
      
      render json: JSON.parse(result[:body]), status: result[:status]
    rescue => e
      Rails.logger.error "Health check endpoint failed: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      
      render json: {
        status: 'error',
        message: 'Health check failed',
        error: e.message,
        timestamp: Time.current
      }, status: 500
    end
  end
  
  private
  
  def skip_prepare_data?
    # Skip prepare_data cho health check để tránh circular dependency
    action_name == 'schema_cache'
  end
end
