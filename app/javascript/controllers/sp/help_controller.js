import { Controller } from "stimulus"

export default class extends Controller {
  static targets = ["importRegulationsSection", "vehicleLogisticsSection", "costPaymentSection", "customsDocumentationSection", "postImportSection", "TCVBenefitsSection"]

  connect() {
    this.boundHandleScroll = this.handleScroll.bind(this)
    window.addEventListener('scroll', this.boundHandleScroll)
    this.handleScroll()
  }

  disconnect() {
    window.removeEventListener('scroll', this.boundHandleScroll)
  }

  handleScroll() {
    const sections = [
      { element: this.importRegulationsSectionTarget, headerId: 'importRegulationsHeader' },
      { element: this.vehicleLogisticsSectionTarget, headerId: 'vehicleLogisticsHeader' },
      { element: this.costPaymentSectionTarget, headerId: 'costPaymentHeader' },
      { element: this.customsDocumentationSectionTarget, headerId: 'customsDocumentationHeader' },
      { element: this.postImportSectionTarget, headerId: 'postImportHeader' },
      { element: this.TCVBenefitsSectionTarget, headerId: 'TCVBenefitsHeader' }
    ]

    const THRESHOLD_TOP = -70, THRESHOLD_BOTTOM = 60;
    let activeHeaderId = null;

    sections.forEach(section => {
      const header = document.getElementById(section.headerId)

      if (section.element) {
        const rect = section.element.getBoundingClientRect();

        const isActive = rect.top <= THRESHOLD_TOP && rect.bottom > THRESHOLD_BOTTOM

        if (header) {
          header.classList.toggle('visible', isActive)
        }
      }
    })
  }
}
