import { Controller } from 'stimulus'
import { setOfferErrorMessage } from '../shared/thankyou_error_handle'
import { signUpValidateField, signUpvalidateFieldWhenSubmitting } from '../shared/signup'
import passAjaxInvalidAuthenticityToken from '../shared/authenticity_hanlder'

export default class extends Controller {
  static targets = ['ipEmailTxt', 'ipNumberTxt', 'ipPurposeTxt',
                    'ipPasswordTxt', 'countryNum', 'countryCode',
                    'errorMessage', 'emailInputField', 'emailInputBox',
                    'phoneNumInputField', 'phoneNumInputBox', 'passwordInputField',
                    'passwordInputBox', 'countryInputField', 'countryInputBox',
                    'purposeInputField', 'purposeInputBox', 'personalUse',
                    'forCustomer', 'phoneNumChatBox', 'passwordChatBox',
                    'countryChatBox', 'purposeChatBox', 'grecaptchaChatbox']

  showFloatingLabel(event) {
    event.target.nextSibling.classList.add('input-label--floating')
    this.removeErrorMessage(event)
  }

  validateField(event) {
    let target = event.target
    const nextTarget = []
    const self = this

    if (target.dataset.nextTarget) {
      target.dataset.nextTarget.split(',').forEach(
        (target) => {
          nextTarget.push(self[`${target}Target`])
        }
      )
    }

    signUpValidateField(target, nextTarget)
  }

  showPassword() {
    if (document.querySelector('input#show_password').checked) {
      this.ipPasswordTxtTarget.type = 'password'
    } else {
      this.ipPasswordTxtTarget.type = 'text'
    }
  }

  async validateFieldWhenSubmitting(event) {
    event.preventDefault()
    signUpvalidateFieldWhenSubmitting(this, this.handleSubmit.bind(this))
  }

  async handleSubmit(data) {
    const mainData = JSON.parse($('.loginregist__main')[0].dataset.offer)
    const errorMessage = this.errorMessageTarget

    try {
      $('#overlay_loading').show()

      await this.ajaxSignInStep('/ajax_v2/users/signup', data)
      $('body').data('logged', true);

      await this.ajaxSignInStep('/ajax_v2/users/shortcutmember', '', 'GET')


      if (mainData.type === 'backorder') {
        this.submitBackOrder(mainData)
      } else {
        this.createOffer(mainData)
      }
    } catch (err) {
      $('#overlay_loading').hide()
      errorMessage.innerHTML = err?.responseJSON?.msg || 'An error occurred. Please try again.'
      $('.send-button').addClass('inactive')
      $('.gcaptcha-error-message').css('display', 'block')
      grecaptcha.reset();
    }
  }

  async ajaxSignInStep(url, dataString, method = 'POST') {
    return new Promise((resolve, reject) => {
      $.ajax({
        url: url,
        type: method,
        data: dataString,
        success: function(data) {
          resolve(data)
        },
        error: function(response) {
          passAjaxInvalidAuthenticityToken(response)
          reject(response)
        }
      });
    })
  }

  async createOffer(mainData) {
    const tempOfferData = {
      temporary_contact_id: mainData.ti,
      hs: mainData.hs
    }

    const tempOfferResponse = await this.ajaxSignInStep('/ajax_v2/transactions/offers/temp', tempOfferData, 'GET')
    if (!tempOfferResponse.success) throw 'Get Temp offer failed'

    const offerData = {
      item_id: tempOfferResponse.response.itemId,
      insurance: mainData.iir,
      inspection: mainData.iip,
      shipping: mainData.ifr,
      country_number: tempOfferResponse.response.dischargePortCountryNumber,
      port_id: tempOfferResponse.response.dischargePortId,
      receive_promotion: $('#receive_email').is(':checked'),
      message: tempOfferResponse.response.comment,
      referrer: sessionStorage.getItem("referrer"),
      is_bnpl: mainData.is_bnpl,
      seller_id: mainData.seller_id
    }

    const offerResponse = await this.ajaxSignInStep('/ajax_v2/transactions/offers/create_offer_with_ab_test_simple_signup', offerData)
    setOfferErrorMessage(offerResponse)
    $('#overlay_loading').hide()
    window.location.href = offerResponse.url
  }

  submitBackOrder(dataJson) {
    const fileName = dataJson['file']

    if (fileName) {
      const imgBase64Data = localStorage['backOrderFile']
      let file = this.dataURLtoFile(imgBase64Data, fileName)
      dataJson['file'] = file
    }

    const formData = new FormData();

    for (let key in dataJson) {
      formData.append(key, dataJson[key])
    }

    localStorage.removeItem('backOrderFile')

    $.ajax({
      url: '/ajax_v2/backorders/create',
      data: formData,
      type: 'POST',
      processData: false,
      contentType: false,
      success: function() {
        window.location.href = '/backorder2/thanks'
        $('#overlay_loading').hide()
      },
      error: function(response) {
        passAjaxInvalidAuthenticityToken(response)
      }
    })
  }

  dataURLtoFile(dataurl, filename) {
    var arr = dataurl.split(','),
      mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[arr.length - 1]),
      n = bstr.length,
      u8arr = new Uint8Array(n);
    while(n--){
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, {type:mime});
  }

  removeErrorMessage(event) {
    event.target.classList.remove('error')
  }

  openSignInTab() {
    const signUpArea = document.querySelector('.sign-up_area') || document.querySelector('.sign-up_area--new')
    signUpArea.classList.add('d-none')
    $('.sign-in_area').removeClass('d-none')
  }

  showInputBox(e) {
    const inputField = this[`${e.target.dataset.field}InputFieldTarget`]
    const inputBox = this[`${e.target.dataset.field}InputBoxTarget`]

    inputField.classList.add('d-none')
    inputField.nextSibling.classList.add('d-none')
    inputBox.classList.remove('d-none')
  }

  choosePurpose(e) {
    if (e.target.dataset.purpose == 'personalUse') {
      this.personalUseTarget.classList.add('checked')
      this.forCustomerTarget.classList.remove('checked')
      this.purposeInputFieldTarget.querySelector('.input-text').textContent = 'Personal Use'
    } else {
      this.personalUseTarget.classList.remove('checked')
      this.forCustomerTarget.classList.add('checked')
      this.purposeInputFieldTarget.querySelector('.input-text').textContent = 'For Customer'
    }

    this.purposeInputFieldTarget.classList.remove('d-none')
    this.purposeInputFieldTarget.nextSibling.classList.remove('d-none')
    this.purposeInputBoxTarget.classList.add('d-none')
    this[`${e.target.dataset.nextTarget}Target`].classList.add('visible')
  }

  showPasswordField() {
    if (document.querySelector('input#show_password_field').checked) {
      this.passwordInputFieldTarget.querySelector('.input-text').textContent = '**********'
    } else {
      this.passwordInputFieldTarget.querySelector('.input-text').textContent = this.ipPasswordTxtTarget.value
    }
  }

  handleKeyPress(e) {
    if (e.keyCode === 13) {
      e.target.blur()
    }
  }
}
