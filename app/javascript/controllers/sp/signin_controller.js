import { Controller } from "stimulus"
import Cookies from 'js-cookie'
import { setOfferErrorMessage } from "../shared/thankyou_error_handle"
import passAjaxInvalidAuthenticityToken from '../shared/authenticity_hanlder'

const PREVENT_LEAVING = 'hidePopupPreventLeavingLoginRegist'
export default class extends Controller {
  static targets = ['dSignInInput', 'dBlockLoader', 'btnSend', 'emailInput', 'passwordInput']

  connect() {
    this.inputSigninHandler()
    this.setupPopUpPreventLeaving()
  }

  inputSigninHandler() {
    const signInInputs = this.dSignInInputTarget.querySelectorAll('input[type=text], input[type=password]')
    signInInputs.forEach((ipTarget) => {
      ipTarget.addEventListener('focus', () => this.resetSignInButton() )
    })
  }

  toggleSignInDisplay(displayInput = true) {
    if (displayInput) {
      this.dSignInInputTarget.classList.remove('d-none')
      this.dBlockLoaderTarget.classList.remove('active')
    } else {
      this.dSignInInputTarget.classList.add('d-none')
      this.dBlockLoaderTarget.classList.add('active')
    }
  }

  async handleSignIn() {
    this.toggleSignInDisplay(false);
    const mainData = JSON.parse($('.loginregist__main')[0].dataset.offer)
    const notice = this.btnSendTarget.closest('.modal__btn-wrap').querySelector('.notice')

    try {
      const signInData = {
        email: this.emailInputTarget.value,
        password: this.passwordInputTarget.value,
        remember_me: $('#remember_me').is(':checked')
      }

      await this.ajaxSignInStep('/ajax_v2/users/signin', signInData)

      $('body').data('logged', true)
      $('#overlay_loading').show()

      if (mainData.type === 'backorder') {
        this.submitBackOrder(mainData)
      } else {
        this.createOffer(mainData)
      }
    } catch (err) {
      notice.innerText = err?.responseJSON?.msg || 'An error occurred. Please try again.'
      $('#overlay_loading').hide()
      this.btnSendTarget.classList.add('inactive')
      this.toggleSignInDisplay(true)
    }
  }

  async ajaxSignInStep(url, dataString, method = 'POST') {
    return new Promise((resolve, reject) => {
      $.ajax({
        url: url,
        type: method,
        data: dataString,
        success: function(data) {
          resolve(data)
        },
        error: function(response) {
          passAjaxInvalidAuthenticityToken(response)
          reject(response)
        }
      });
    })
  }

  async createOffer(mainData) {
    const tempOfferData = {
      temporary_contact_id: mainData.ti,
      hs: mainData.hs
    }

    const tempOfferResponse = await this.ajaxSignInStep('/ajax_v2/transactions/offers/temp', tempOfferData, 'GET')
    if (!tempOfferResponse.success) throw 'Get Temp offer failed'

    const offerData = {
      item_id: tempOfferResponse.response.itemId,
      insurance: mainData.iir,
      inspection: mainData.iip,
      shipping: mainData.ifr,
      country_number: tempOfferResponse.response.dischargePortCountryNumber,
      port_id: tempOfferResponse.response.dischargePortId,
      message: tempOfferResponse.response.comment,
      referrer: sessionStorage.getItem("referrer"),
      is_bnpl: mainData.is_bnpl,
      seller_id: mainData.seller_id
    }

    const offerResponse = await this.ajaxSignInStep('/ajax_v2/transactions/offers', offerData)
    setOfferErrorMessage(offerResponse)
    $('#overlay_loading').hide()
    this.toggleSignInDisplay(true)
    window.location.href = offerResponse.url
  }

  submitBackOrder(dataJson) {
    const fileName = dataJson['file']

    if (fileName) {
      const imgBase64Data = localStorage['backOrderFile']
      let file = this.dataURLtoFile(imgBase64Data, fileName)
      dataJson['file'] = file
    }

    const formData = new FormData()

    for (let key in dataJson) {
      formData.append(key, dataJson[key]);
    }

    localStorage.removeItem('backOrderFile')

    $.ajax({
      url: '/ajax_v2/backorders/create',
      data: formData,
      type: 'POST',
      processData: false,
      contentType: false,
      success: function() {
        window.location.href = '/backorder2/thanks'
        $('#overlay_loading').hide()
      },
      error: function(response) {
        passAjaxInvalidAuthenticityToken(response)
      }
    })
  }

  dataURLtoFile(dataurl, filename) {
    var arr = dataurl.split(','),
      mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[arr.length - 1]),
      n = bstr.length,
      u8arr = new Uint8Array(n);
    while(n--){
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, {type:mime});
  }

  resetSignInButton() {
    this.btnSendTarget.classList.remove('inactive')
  }

  openSignUpTab() {
    $('.sign-in_area').addClass('d-none')
    const signUpArea = document.querySelector('.sign-up_area') || document.querySelector('.sign-up_area--new')
    signUpArea.classList.remove('d-none')
  }

  setupPopUpPreventLeaving() {
    if (Cookies.get(PREVENT_LEAVING) === undefined) {
      let status = false
      document.onclick = () => {
        if (status) return
        history.pushState({}, '', window.location.href)
        status = true
      }

      window.onpopstate = (event) => {
        if (window.location.hash !== '') return event.preventDefault()
        Cookies.set(PREVENT_LEAVING, 'true', { expires: 90 })
        $('#modal-prevent-leaving').modal()
      }
    }
  }
}
