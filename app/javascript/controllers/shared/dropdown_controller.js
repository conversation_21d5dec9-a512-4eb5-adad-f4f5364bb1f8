import { Controller } from "stimulus"

export default class extends Controller {
  static targets = ["title", "content", "iconUp", "iconDown"]

  connect() {
    this.updateIconVisibility();
  }

  toggle() {
    this.contentTarget.classList.toggle('d-none');
    this.updateIconVisibility();
  }

  updateIconVisibility() {
    const isHidden = this.contentTarget.classList.contains('d-none');
    this.iconDownTarget.classList.toggle('d-none', !isHidden);
    this.iconUpTarget.classList.toggle('d-none', isHidden);
  }
}
