import { Controller } from "stimulus";

export default class extends Controller {
  static targets = ["button", "table"];

  handleClick(event) {
    const clickedIndex = this.buttonTargets.indexOf(event.currentTarget);
    this.showTable(clickedIndex);
  }

  showTable(indexToShow) {
    this.tableTargets.forEach((table, index) => {
      if (index === indexToShow) {
        table.classList.remove('d-none');
        this.buttonTargets[index].classList.add('active');
      } else {
        table.classList.add('d-none');
        this.buttonTargets[index].classList.remove('active');
      }
    });
  }
}
