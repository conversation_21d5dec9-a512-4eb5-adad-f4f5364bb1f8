.thank-you-wrapper {
  width: 100%;
  max-width: 910px;
  padding: 40px 0 0;
  margin: 0 auto;
  text-align: center;

  .title {    
    margin: 0 0 8px;
    text-align: center;
    font-size: 32px;
    font-weight: 700;
    line-height: 1;
    color: $dark-blue-500;
  }

  .icon-wrapp {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    margin-bottom: 10px;
  }

  .message {
    margin-top: 0;
    font-size: 22px;
    color: $dark-blue-500;
    margin-bottom: 10px;

    &.sub-message {
      font-size: 18px;
      margin-bottom: 30px;
    }

    &.message--red {
      color: red;
    }
  }

  .supporters-text {
    h2 {
      font-size: 22px;
      font-weight: 700;
      color: $orange;
      text-align: center;
      margin: 18px 0;
    }
  }

  .btn-wrapp {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    margin-top: 15px;

    .button {
      color: #fff;
      font-weight: 500;
      font-size: 24px;
      text-shadow: 0 -1px 0.5px $dark-orange-200;
      border-radius: 5px;
      text-decoration: none;
      cursor: pointer;
      transition: opacity .3s,position .3s,color .3s;
      max-width: 320px;
      height: 56px;
      display: flex;
      width: 100%;
      align-items: center;
      -webkit-box-align: center;
      -webkit-box-pack: center;
      justify-content: space-around;

      &:before {
        content: "";
        display: inline-block;
        width: 20px;
        height: 22px;
        vertical-align: 0;
      }

      &:hover {
        opacity: .7;
      }

      &.btn--orange {
        background-color: #f38d10;
        box-shadow: 0 0 2px 0 rgb(51 51 51 / 25%) inset, 0 4px 0 0 #ae6409;
        margin-right: 20px;
      }

      &.btn--gray {
        background-color: $light-500;
        color: $gray-300;
        text-shadow: none;
        margin-left: 20px;
        box-shadow: 0 0 0 0.5px rgb(112 112 93 / 20%) inset, 0 4px 0 0 #70705d;
      }

      &.button__whatsapp {
        background-color: $spring-green;
        margin-left: 20px;
        max-width: 420px;
        font-weight: 700;
        border-radius: 10px;
        text-shadow: none;
        font-size: 30px;

        .icon-chevron-right {
          margin-left: -10px;
          margin-right: 15px;
        }
      }
    }

    span.ico {
      font-size: 37px;
      font-weight: 900;
    }
  }

  .error-message {
    background-color: #feeeed;
    padding: 10px 0;
    font-weight: 500;
    color: #e2362f;
  }

  .badge {
    position: absolute;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 17px;
    font-weight: normal;
    top: -20px;
    left: -40px;

    &.badge--orange {
      background-color: $light-orange;
      color: #fff;
    }
  }
}
