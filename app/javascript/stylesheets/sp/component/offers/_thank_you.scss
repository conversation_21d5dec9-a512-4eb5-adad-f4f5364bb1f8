.thank-you-wrapper {
  width: 100%;
  padding: 15px 0;
  margin: 0 auto;
  text-align: center;

  .title {    
    margin: 0 0 8px;
    text-align: center;
    font-size: 25px;
    font-weight: 700;
    line-height: 1;
    color: $dark-blue-500;
    display: flex;
    align-items: flex-end;
    justify-content: center;

    span {
      margin-bottom: 10px;
    }
  }

  .icon-wrapp {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    margin-bottom: 10px;
  }

  .message {
    margin-top: 0;
    font-size: 15px;
    color: $dark-blue-500;
    margin-bottom: 5px;
    padding: 0 5px;

    &.break-line {
      margin-bottom: 30px;
    }

    &.message--red {
      color: red;
    }
  }

  .supporters-text {
    h2 {
      font-size: 22px;
      font-weight: 700;
      color: $orange;
      justify-content: center;
      margin: 18px 0;
      flex-wrap: wrap;
    }
  }

  .supporters-banner {
    padding: 0 10px;
  }

  .btn-wrapp {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    margin-top: 15px;
    flex-wrap: wrap;

    .button {
      color: #fff;
      font-weight: 500;
      font-size: 24px;
      text-shadow: 0 -1px 0.5px $dark-orange-200;
      border-radius: 5px;
      text-decoration: none;
      cursor: pointer;
      max-width: 320px;
      height: 56px;
      display: flex;
      width: 100%;
      align-items: center;
      -webkit-box-align: center;
      -webkit-box-pack: center;
      justify-content: space-around;

      &:before {
        content: "";
        display: inline-block;
        width: 20px;
        height: 22px;
        vertical-align: 0;
      }

      &.btn--orange {
        background-color: #f38d10;
        box-shadow: 0 0 2px 0 rgb(51 51 51 / 25%) inset, 0 4px 0 0 #ae6409;
        margin-bottom: 15px;
      }

      &.btn--gray {
        background-color: $light-500;
        color: $gray-300;
        text-shadow: none;
        box-shadow: 0 0 0 0.5px rgb(112 112 93 / 20%) inset, 0 4px 0 0 #70705d;
      }

      &.button__whatsapp {
        background-color: $spring-green;
        max-width: 350px;
        font-weight: 600;
        border-radius: 10px;
        text-shadow: none;
        font-size: 4.5vw;

        .icon-chevron-right {
          margin-left: -5px;
          margin-right: 10px;
        }
      }
    }

    span.ico {
      font-size: 37px;
      font-weight: 900;
    }
  }

  .error-message {
    padding: 10px 0;
    font-weight: 500;
    color: #e2362f;
    margin: 0 10px;
    border: 2px solid #ff0000;
  }

  .badge {
    position: absolute;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 3.5vw;
    font-weight: normal;
    top: -20%;
    left: -12%;

    &.badge--orange {
      background-color: $light-orange;
      color: #fff;
    }
  }
}
