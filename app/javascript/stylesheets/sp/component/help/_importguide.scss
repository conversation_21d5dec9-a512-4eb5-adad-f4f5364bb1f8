.guide-area {
  width: 100%;

  .guide-body {
    width: 100%;
    padding: 0 16px;
  }

  .nav-bar {
    .list-item {
      .item {
        width: calc(100%/3);
        background-color: $gray-500;
        border-left: 1px solid #fff;
        border-bottom: 1px solid #fff;
        text-decoration: none;
        color: #fff;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 8px 0 2px;

        span.ico {
          font-size: 20px;
        }

        .text {
          margin-top: 4px;
          font-size: 12px;
        }
      }
    }
  }
}


.guide-area {
  .main {
    width: 100%;
    flex: 1 0 0;
  }

  .guide-title-sticky-header {
    position: fixed;
    width: 100%;
    color: #333;
    font-size: 14px;
    line-height: 170%;
    padding: 16px;
    border-bottom: 1px solid #000;
    background: #EDEDED;
    z-index: 100;
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.25s ease-in-out;

    &.visible {
      top: 0;
      transform: translateY(0);
      opacity: 1;
      visibility: visible;
    }
  }

  .table-index-item,
  .guide-content-link {
    text-decoration-line: underline;
    text-decoration-style: solid;
    text-decoration-skip-ink: auto;
    text-decoration-thickness: auto;
    text-underline-offset: auto;
    text-underline-position: from-font;
  }

  .icon-btn {
    cursor: pointer;
  }

  .table-text__structure {
    display: flex;
  }

  .main,
  .guide-title,
  .guide-checklist__table,
  .table-column,
  .column-1,
  .column-2,
  .column-3,
  .table-row,
  .guide-box-structure,
  .guide-box-recommended,
  .recommended-content,
  .guide-table-structure,
  .guide-table,
  .guide-table-index,
  .component,
  .table-content,
  .table-content__structure,
  .table-text__structure {
    align-items: flex-start;
    flex-direction: column;
  }

  .guide-background,
  .guide-checklist,
  .column-structure,
  .row-structure {
    align-items: flex-start;
  }

  .block-1,
  .block-2,
  .block-3,
  .search-button {
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .search-button {
    width: 100%;
    padding: 20px 0px 40px 0;

    &__style {
      width: 100%;
      height: 56px;
      padding: 10px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      border-radius: 8px;
      background: #FF541A;
      box-shadow: 0px 5px 0px 0px #C84D23;
    }

    &__text {
      color: #FFF;
      font-size: 18px;
      font-weight: 700;
      line-height: 170%;
    }

    a {
      text-decoration: none;
    }
  }

  .no-padding {
    padding: 0;
  }

  .no-gap {
    gap: 0;
  }

  .align-self-stretch {
    align-self: stretch;
  }

  .flex-shrink-0 {
    flex-shrink: 0;
  }

  .flex-attribute {
    flex: 1 0 0;
  }

  .gap-4 {
    gap: 4px;
  }

  .gap-5 {
    gap: 5px;
  }

  .gap-15 {
    gap: 15px;
  }

  .gap-20 {
    gap: 20px;
  }

  .gray-100 {
    color: #333;
  }
}

.guide-title {
  align-self: stretch;

  &.text {
    padding: 24px 16px 12px 16px;
  }

  &.content {
    padding: 20px 0px 12px 0px;
  }

  &.time,
  &.table-index {
    padding: 4px 16px 12px 16px;
  }


  &.box-wrapper,
  &.content-text,
  &.table {
    padding: 4px 0px 12px 0px;
  }

  &.background {
    flex-direction: column;
    padding: 12px 0px;
    background: #FAFAFA;
  }
}

.checklist-container {
  padding: 12px 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.guide-text {
  color: #333;
  font-feature-settings: 'dlig' on;
  font-size: 32px;
  font-weight: 700;
  line-height: 40px;
}

.guide-time {
  gap: 20px;

  .time-content {
    gap: 5px;

    .icon {
      width: 16px;
      height: 16px;
      flex-shrink: 0;
    }

    .text-content {
      color: #5C738A;
      font-size: 14px;
      font-weight: 400;
      line-height: normal;
    }
  }
}

.guide-background {
  gap: 8px;
  flex: 1 0 0;
  width: 100%;

  .background-content {
    height: 220px;
    flex: 1 0 0;
    background: url('~images/help/sp_background_guide.webp') lightgray 50% / cover no-repeat;
  }
}

.guide-table-index {
  width: 100%;
  padding: 20px;
  gap: 10px;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  background: #F9FAFB;

  .table-index-title {
    gap: 4px;
    align-self: stretch;

    .icon-table-index {
      width: 20px;
      height: 20px;
      aspect-ratio: 1/1;
    }

    .text-table-index {
      color: #333;
      font-size: 18px;
      font-weight: 700;
      line-height: 170%;
    }
  }

  .table-index-item {
    color: #666;
    align-self: stretch;
    font-size: 16px;
    font-weight: 400;
    line-height: 170%;
  }
}

.guide-content-title {
  align-self: stretch;
  color: #333;
  font-feature-settings: 'dlig' on;
  font-size: 18px;
  font-weight: 700;
  line-height: 28px;

  &.title-main {
    font-size: 22px;
  }
}

.guide-content-text,
.guide-content-link {
  font-feature-settings: 'dlig' on;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
}

.guide-content-text {
  align-self: stretch;
  color: #333;
}

.guide-content-link {
  align-self: stretch;

  a {
    color: #5E92EC;
  }
}

.guide-checklist {
  align-self: stretch;
  border-radius: 12px;
  border: 1px solid #D4DBE3;
  background: #FAFAFA;
}

.guide-checklist__table {
  flex: 1 0 0;

  .table-column {
    align-self: stretch;
  }

  .column-structure {
    flex: 1 0 0;
    align-self: stretch;
    background: #FAFAFA;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
  }

  .column-1,
  .column-2,
  .column-3 {
    padding: 12px 16px;
    align-self: stretch;
  }

  .column-1 {
    width: 41%;
  }

  .column-2 {
    width: 59%;
  }

  .column-3 {
    flex: 1 0 0;
  }

  .column-text {
    color: #333;
    font-weight: 500;
  }

  .table-row {
    align-self: stretch;

    &.height {
      height: 413px;
    }
  }

  .row-structure {
    border-top: 1px solid #E5E8EB;
    width: 100%;
  }

  .block-1,
  .block-2,
  .block-3 {
    padding: 8px 16px;
  }

  .block-1 {
    width: 41%;
    align-self: stretch;
  }

  .block-2 {
    width: 59%;
  }

  .block-3 {
    flex: 1 0 0;
    align-self: stretch;
  }

  .column-text,
  .block-text,
  .block-text-1 {
    align-self: stretch;
    font-feature-settings: 'dlig' on;
    font-size: 14px;
    line-height: 21px;
  }

  .block-text,
  .block-text-1 {
    font-weight: 400;
  }

  .block-text {
    color: #5C738A;
  }

  .block-text-1 {
    color: #333;
  }

}

.guide-box-structure {
  gap: 10px;
  align-self: stretch;
}

.guide-box-list {
  align-items: center;
  gap: 16px;
  align-self: stretch;
}

.guide-box-item {
  width: 240px;
  padding: 10px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 4px;
  background: #F4F3F1;
  box-shadow: 0px 5px 0px 0px #DDD;

  &.active {
    background: #D5D5D5;
  }
}

.box-item-text {
  color: #333;
  font-family: Roboto;
  font-style: normal;
  font-size: 16px;
  font-weight: 700;
  line-height: 170%;
}

.guide-box-recommended {
  padding: 20px;
  gap: 11px;
  align-self: stretch;
  border-radius: 8px;
  background: #F9FAFB;

  .recommended-title {
    align-self: stretch;
    color: #333;
    font-size: 18px;
    font-weight: 700;
    line-height: 170%;
  }

  .recommended-content {
    gap: 4px;
    align-self: stretch;
  }

  .recommended-text {
    align-self: stretch;
    color: #333;
    font-size: 16px;
    font-weight: 700;
    line-height: 170%;
  }

  .recommended-text__list {
    align-self: stretch;
    color: #5C738A;
    font-size: 16px;
    font-weight: 400;
    line-height: 150%;

    ul {
      list-style-type: disc;
      padding-left: 20px;
    }
  }
}

.guide-table-structure {
  gap: 12px;
  align-self: stretch;

  .component {
    gap: 20px;
    align-self: stretch;

    &.background {
      background: #FAFAFA;
    }
  }
}

.guide-table {
  padding: 16px;
  gap: 16px;
  align-self: stretch;
  border-radius: 8px;
  border: 1px solid #E5E5E5;
  background: #FAFAFA;

  .table-title {
    align-items: center;
    gap: 10px;
    align-self: stretch;

    .icon {
      width: 10px;
      height: 10px;
      aspect-ratio: 1/1;
    }

    &.has-style-title {
      padding: 15px;
      border-bottom: 1px solid #E5E5E5;
      background: #FAFAFA;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }

    &-text {
      font-weight: 700;
    }
  }

  .table-text,
  .table-content-text,
  .table-title-text {
    color: #333;
    font-size: 16px;
    line-height: 150%;
  }

  .table-text,
  .table-content-text {
    font-weight: 400;
    flex: 1 0 0;
  }

  .table-content {
    gap: 15px;
    align-self: stretch;

    &__structure {
      gap: 4px;
      align-self: stretch;
    }

    &.has-style-content {
      padding: 15px;
      gap: 10px;
      background: #FFF;
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
    }

    .border-bottom {
      border-bottom: 1px solid #E5E8EB;
      padding-bottom: 12px;
    }
  }

  .table-text {

    &-1,
    &-2,
    &-3 {
      font-size: 16px;
      line-height: 150%;
      align-self: stretch;
    }

    &-1 {
      color: #555;
      font-weight: 700;
    }

    &__structure {
      align-self: stretch;
    }

    &-2 {
      color: #5C738A;
      font-weight: 700;
    }

    &-3 {
      color: #5C738A;
      font-weight: 400;
      padding-left: 4px;
    }
  }
}
