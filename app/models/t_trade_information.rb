class TTradeInformation < ApplicationRecord
  MAX_NEWS_DISPLAY = 4
  NEWS_CATEGORY = 1

  self.primary_key = :id

  scope :published_news, lambda {
    jp_time = Time.current + 9.hours

    where(
      is_valid: true,
      is_post: true,
      category: NEWS_CATEGORY,
    ).where(
      'show_start_date <= :now AND show_end_date >= :now', now: jp_time
    ).order(publish_date: :desc)
      .select(:id, :title, :publish_date, :is_important)
      .limit(MAX_NEWS_DISPLAY)
  }
end
