class TListingNumberQuery < ApplicationRecord
  self.primary_key = :t_listing_number_id

  validates :create_date, :update_date, :collection_day, presence: true
  validates :param_value, presence: true, numericality: { only_integer: true }
  validates :stock_count, presence: true, numericality: { only_integer: true }

  class << self
    def stock_count_30_days(param_name, param_value)
      start_date = 30.days.ago.to_date
      end_date = 1.days.ago.to_date

      where(param_name:, param_value:, collection_day: start_date..end_date)
        .order(collection_day: :desc, update_date: :desc)
        .group_by(&:collection_day)
        .map do |_, records|
          records.first
        end
    end
  end
end
