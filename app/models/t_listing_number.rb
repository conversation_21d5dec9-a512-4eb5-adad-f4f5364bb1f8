class TListingNumber < ApplicationRecord
  self.primary_key = :t_listing_number_id

  validates :create_date, :update_date, :stock_date, presence: true
  validates :make_id, :model_id, presence: true, numericality: { only_integer: true }
  validates :make_model_stock, presence: true, numericality: { only_integer: true }

  class << self
    def stock_count_30_days(make_id, model_id)
      start_date = 30.days.ago.to_date
      end_date = 1.days.ago.to_date

      where(make_id:, model_id:, stock_date: start_date..end_date)
        .order(stock_date: :desc, update_date: :desc)
        .group_by(&:stock_date)
        .map do |_, records|
          records.first
        end
    end
  end
end
