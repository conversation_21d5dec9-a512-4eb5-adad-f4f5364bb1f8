class TTrim < ApplicationRecord
  belongs_to :master_model, foreign_key: :model_id, primary_key: :id_model

  delegate :master_make, to: :master_model, allow_nil: true
  delegate :vc_name_e, to: :master_model, prefix: :model, allow_nil: true
  delegate :vc_name_e, to: :master_make, prefix: :make, allow_nil: true
  delegate :t_trims, to: :master_model, prefix: :model, allow_nil: true

  validates :model_id, :trim_id, presence: true, numericality: { only_integer: true }
  validates :pattern_type, presence: true, length: { maximum: 40 }
  validates :is_valid, exclusion: [nil]
  validates :created_date, :updated_date, presence: true
  validates :vehicle_length, :vehicle_width, :vehicle_height, numericality: { only_integer: true, allow_nil: true }
  validates :short_pattern_type, length: { maximum: 50 }
  validates_uniqueness_of :model_id, scope: :trim_id

  scope :valid, -> { where(is_valid: true) }
  scope :by_model_id, ->(model_id) { where(model_id:) }
  scope :by_pattern, ->(model_code) { where(pattern_type: model_code&.upcase).or(where(short_pattern_type: model_code&.upcase)) }
  scope :same_variants, lambda { |current_model_code|
    valid
      .pluck(:pattern_type, :short_pattern_type)
      .flatten.uniq
      .filter { |model_code| model_code != current_model_code }
  }

  def model_code_show_text(make_model_code)
    @model_code_show_text ||= begin
      raw_model_code = make_model_code.delete_prefix("#{make_vc_name_e.downcase}_")&.upcase

      case raw_model_code
      when short_pattern_type
        short_pattern_type
      when pattern_type
        pattern_type
      else
        ''
      end
    end
  end
end
