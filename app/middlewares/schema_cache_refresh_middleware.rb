class SchemaCacheRefreshMiddleware
  def initialize(app)
    @app = app
    @logger = Rails.logger
    @last_refresh = Time.current
    @refresh_interval = 1.hour # Refresh mỗi 1 giờ
    @error_count = 0
    @max_errors_before_refresh = 3 # Refresh sau 3 lỗi liên tiếp
  end

  def call(env)
    response = @app.call(env)
    response
  rescue => error
    # Kiểm tra nếu là lỗi undefined method liên quan đến database attributes
    if schema_related_error?(error)
      @error_count += 1
      @logger.warn "Schema-related error detected (count: #{@error_count}): #{error.message}"
      
      # Refresh schema cache nếu đủ điều kiện
      if should_refresh_schema_cache?
        refresh_schema_cache
        @error_count = 0 # Reset error count sau khi refresh
        
        # Retry request một lần sau khi refresh
        begin
          @logger.info "Retrying request after schema cache refresh"
          return @app.call(env)
        rescue => retry_error
          @logger.error "Request still failed after schema cache refresh: #{retry_error.message}"
          # Không retry nữa, để error bubble up
        end
      end
    end
    
    # Re-raise original error
    raise error
  end

  private

  def schema_related_error?(error)
    # Kiểm tra các loại lỗi có thể liên quan đến schema cache
    error_message = error.message.to_s.downcase
    
    # Lỗi undefined method cho database attributes
    return true if error_message.include?('undefined method') && 
                   error_message.match?(/`\w+(_nm|_id|_date|_count)'/) &&
                   error_message.include?('aggregate_ranking_point')
    
    # Lỗi unknown attribute
    return true if error_message.include?('unknown attribute')
    
    # Lỗi column not found
    return true if error_message.include?('column') && error_message.include?('not found')
    
    # Lỗi table doesn't exist (ít có thể xảy ra nhưng cũng nên handle)
    return true if error_message.include?("table") && error_message.include?("doesn't exist")
    
    false
  end

  def should_refresh_schema_cache?
    # Refresh nếu:
    # 1. Đã đủ số lỗi liên tiếp
    # 2. Hoặc đã quá thời gian refresh interval
    @error_count >= @max_errors_before_refresh || 
    (Time.current - @last_refresh) > @refresh_interval
  end

  def refresh_schema_cache
    @logger.info "Refreshing schema cache due to potential schema mismatch"
    
    begin
      # Clear toàn bộ schema cache
      ActiveRecord::Base.connection.schema_cache.clear!
      
      # Reset column information cho các model có thể bị ảnh hưởng
      models_to_refresh = [
        TAggregateRankingPoint,
        TAggregateOffer,
        TPopularRanking
      ]
      
      models_to_refresh.each do |model|
        begin
          model.reset_column_information
          @logger.debug "Reset column information for #{model.name}"
        rescue => model_error
          @logger.error "Failed to reset column information for #{model.name}: #{model_error.message}"
        end
      end
      
      # Preload schema cho các table quan trọng
      important_tables = %w[
        t_aggregate_ranking_points
        t_aggregate_offers  
        t_popular_rankings
      ]
      
      important_tables.each do |table|
        begin
          ActiveRecord::Base.connection.columns(table)
          @logger.debug "Preloaded schema for #{table}"
        rescue => table_error
          @logger.error "Failed to preload schema for #{table}: #{table_error.message}"
        end
      end
      
      @last_refresh = Time.current
      @logger.info "Schema cache refresh completed successfully"
      
    rescue => refresh_error
      @logger.error "Failed to refresh schema cache: #{refresh_error.message}"
      @logger.error refresh_error.backtrace.join("\n")
    end
  end
end
