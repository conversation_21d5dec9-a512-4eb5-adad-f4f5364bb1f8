module Users
  class Signin
    class << self
      def exec(user_name, password)
        request_params = {
          url: Settings.users_api.signin,
          method: 'POST',
          params: params(user_name, password),
          audience: Settings.users_api.auth_audience,
          retry_options: {
            methods: [],
            retry_if: ->(env, _exc) { env&.response&.success? == false },
            retry_statuses: [500]
          }
        }

        response = ::TcvCoreApi::Request.new(**request_params).send

        response.body.merge('status' => response.status)
      end

      private

      def params(user_name, password)
        {
          'Account' => user_name,
          'Password' => password
        }.to_json
      end
    end
  end
end
