module Users
  class Signup
    def initialize(params)
      @params = params
    end

    def create
      request_params = {
        url: Settings.users_api.signup,
        method: 'POST',
        params: signup_params,
        audience: Settings.users_api.auth_audience,
        retry_options: {
          methods: [],
          retry_if: ->(env, _exc) { env&.response&.success? == false },
          retry_statuses: [500]
        }
      }

      response = ::TcvCoreApi::Request.new(**request_params).send

      response.body.merge('status' => response.status)
    end

    private

    def signup_params
      {
        'Mail' => @params[:email],
        'Password' => @params[:password],
        'CountryNum' => @params[:country],
        'PhoneCountryNum' => @params[:country_code],
        'PhoneNumber' => @params[:number],
        'UserTypeForCRM' => @params[:purpose]
      }.to_json
    end
  end
end
