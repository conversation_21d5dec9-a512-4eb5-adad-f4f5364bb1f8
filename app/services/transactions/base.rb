module Transactions
  class Base
    def initialize(params)
      @item_id = params[:item_id]
      @ship_estimated_country_number = params[:country_number]
      @ship_estimated_port_id = params[:port_id]
      @source = params.dig(:cookies, :source)
      @source3 = params.dig(:cookies, :source3)
      @guid = params.dig(:cookies, :guid)
      @cookie_cvpd = params.dig(:cookies, :cookie_cvpd)
      @temporary_contact_id = params[:temporary_contact_id]
      @hs = params[:hs]
      @expected_error_status = 400
    end

    def create
      response = ::TcvCoreApi::Request.new(url: url, params: params, method: 'POST').send do |request|
        request.headers['Content-Type'] = 'application/json'
        request.headers['X-Tcv-Zigexn-CVPD'] = CvpdCookies.escape(@cookie_cvpd) if @cookie_cvpd
      end

      response_body = response.body || {}
      response_body['errorMessage'] = Settings.tcv_core_api.error_messages.internal_error_msg if unexpected_error?(response)

      [response_body, response.success?]
    end

    private

    def url
      raise StandardError, "You must implement #{self.class}##{__method__}"
    end

    def params
      {
        'itemId' => @item_id,
        'dischargePortCountryNumber' => @ship_estimated_country_number,
        'dischargePortId' => @ship_estimated_port_id,
        'guid' => @guid,
        'src' => @source,
        'src3' => @source3
      }
    end

    def unexpected_error?(response)
      !response.success? && response.status != @expected_error_status && response.status.between?(400, 599)
    end
  end
end
