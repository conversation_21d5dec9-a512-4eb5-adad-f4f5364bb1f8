module Tdk
  class MetaGenerator
    COMBINATIONS = [
      Combinations::BodyStylePriceRangeCombination,
      Combinations::BodyStyleCategoryCombination,
      Combinations::PriceRangeCategoryCombination,
      Combinations::MakeBodyStylePriceRangeCombination,
      Combinations::MakeBodyStyleCategoryCombination,
      Combinations::MakeCategoryPriceRangeCombination,
    ].freeze

    def initialize(searcher, car_count: 0, page_num: '')
      @searcher = searcher
      @car_count = car_count
      @page_num = page_num
    end

    def generate_meta_info
      COMBINATIONS.each do |combination_class|
        combination = combination_class.new(@searcher, car_count: @car_count, page_num: @page_num)
        return combination.generate_meta_info if combination.applicable?
      end
      nil
    end
  end
end
