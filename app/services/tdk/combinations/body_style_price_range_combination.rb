module Tdk
  module Combinations
    class BodyStylePriceRangeCombination < BaseCombination
      def applicable?
        supported_body_style? && supported_price_range? && valid_condition_size?
      end

      def generate_meta_info
        text_parts = [body_style_name, price_range_text]

        build_meta_info(text_parts)
      end

      private

      def valid_condition_size?
        condition_hash.except(:prct, :prcf).size == EXPECTED_SINGLE_CONDITION_SIZE
      end
    end
  end
end
