module Tdk
  module Combinations
    class MakeCategoryPriceRangeCombination < BaseCombination
      CONDITION_PARAMS = %i[make prct prcf].freeze

      def applicable?
        supported_make? && supported_category? && supported_price_range? && valid_condition_size?
      end

      def generate_meta_info
        text_parts = [make_name, category_text, price_range_text]

        build_meta_info(text_parts)
      end

      private

      def valid_condition_size?
        condition_hash.except(*CONDITION_PARAMS).size == EXPECTED_SINGLE_CONDITION_SIZE
      end
    end
  end
end
