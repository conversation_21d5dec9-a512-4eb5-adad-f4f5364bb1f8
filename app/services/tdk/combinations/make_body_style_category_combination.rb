module Tdk
  module Combinations
    class MakeBodyStyleCategoryCombination < BaseCombination
      CONDITION_PARAMS = %i[make bsty].freeze

      def applicable?
        supported_make? && supported_body_style? && supported_category? && valid_condition_size?
      end

      def generate_meta_info
        text_parts = [make_name, body_style_name, category_text]

        build_meta_info(text_parts)
      end

      private

      def valid_condition_size?
        condition_hash.except(*CONDITION_PARAMS).size == EXPECTED_SINGLE_CONDITION_SIZE
      end
    end
  end
end
