module Tdk
  module Combinations
    class BodyStyleCategoryCombination < BaseCombination
      def applicable?
        supported_body_style? && supported_category? && valid_condition_size?
      end

      def generate_meta_info
        text_parts = [body_style_name, category_text]

        build_meta_info(text_parts)
      end

      private

      def valid_condition_size?
        condition_hash.size == EXPECTED_DUAL_CONDITION_SIZE
      end
    end
  end
end
