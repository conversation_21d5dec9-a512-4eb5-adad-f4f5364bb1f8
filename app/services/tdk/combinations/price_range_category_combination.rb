module Tdk
  module Combinations
    class PriceRangeCategoryCombination < BaseCombination
      def applicable?
        supported_price_range? && supported_category? && valid_condition_size?
      end

      def generate_meta_info
        text_parts = [category_text, price_range_text]

        build_meta_info(text_parts)
      end

      private

      def valid_condition_size?
        condition_hash.except(:prct, :prcf).size == EXPECTED_SINGLE_CONDITION_SIZE
      end
    end
  end
end
