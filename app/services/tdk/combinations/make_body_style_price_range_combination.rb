module Tdk
  module Combinations
    class MakeBodyStylePriceRangeCombination < BaseCombination
      CONDITION_PARAMS = %i[make bsty prct prcf].freeze

      def applicable?
        supported_make? && supported_body_style? && supported_price_range? && valid_condition_size?
      end

      def generate_meta_info
        text_parts = [make_name, body_style_name, price_range_text]

        build_meta_info(text_parts)
      end

      private

      def valid_condition_size?
        condition_hash.except(*CONDITION_PARAMS).blank?
      end
    end
  end
end
