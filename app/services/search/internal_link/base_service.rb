module Search
  module InternalLink
    class BaseService
      include SharedData
      FETCH_VIA_POST = %w[ModelCode].freeze

      def initialize
        @solr_facet = Solr::FacetService.new
        load_shared_data
      end

      def call
        return unless show_internal_link?

        Rails.cache.fetch(cache_key, expires_in: 1.hour) do
          self.class::SECTIONS.filter_map do |_, handler|
            link_info = send(handler)
            next if link_info&.dig(:condition).blank?

            link_info
          end
        end
      end

      private

      def show_internal_link?
        raise NotImplementedError, 'Subclasses must implement a show_internal_link? method'
      end

      def cache_key
        raise NotImplementedError, 'Subclasses must implement a cache_key method'
      end

      def build_condition_data(collection, condition_type, &block)
        collection.each_with_index.filter_map do |item, index|
          count = fetch_condition_counts["#{condition_type}_#{index}"] || 0
          next if count.zero?

          [block.call(item), count]
        end
      end

      def fetch_condition_counts
        @all_facet_counts ||= begin
          queries = combined_facet_queries
          if queries.blank?
            {}
          else
            method = FETCH_VIA_POST.include?(self.class.name.demodulize) ? :post : :get
            result = @solr_facet.call_facet(queries: queries.values, method:).dig('facet_counts', 'facet_queries') || {}
            queries.keys.zip(result.values).to_h
          end
        end
      end

      def combined_facet_queries
        raise NotImplementedError, 'Subclasses must implement a combined_facet_queries method'
      end
    end
  end
end
