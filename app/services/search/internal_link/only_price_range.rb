module Search
  module InternalLink
    class OnlyPriceRange < BaseService
      SECTIONS = {
        japan_maker: :handle_japan_maker,
        body_style: :handle_body_style,
        option: :handle_option,
        popular_model: :handle_popular_model
      }.freeze

      def initialize(price_params)
        super()
        @price_range_query = price_params.to_query
        @popular_for_all_make_service = Search::GetPopularForAllMakeService.new
      end

      private

      def show_internal_link?
        price_range.present?
      end

      def cache_key
        "internal_links_price_range_#{@price_range_query}"
      end

      def price_range
        MasterInfo::InternalLink::PriceRange.find_by(query: @price_range_query)
      end

      def handle_japan_maker
        condition = build_condition_data(JAPAN_MAKER, 'maker') do |make|
          ["used_car/#{make[:maker_name].downcase}/all/?#{@price_range_query}", "#{make[:maker_name]} x #{price_range[:text]}"]
        end
        { title: "Make x #{price_range[:text]}", condition: condition }
      end

      def handle_body_style
        condition = build_condition_data(@body_style, 'body_style') do |body_style|
          ["used_car/all/all/?#{@price_range_query}&#{body_style[:query]}", "#{body_style[:text]} x #{price_range[:text]}"]
        end
        { title: "BodyStyles x #{price_range[:text]}", condition: condition }
      end

      def handle_option
        condition = build_condition_data(@option, 'option') do |option|
          ["used_car/all/all/?#{@price_range_query}&#{option[:query]}", "#{price_range[:text]} x #{option[:text]}"]
        end
        { title: "#{price_range[:text]} x Option", condition: condition }
      end

      def handle_popular_model
        return if popular_models.blank?

        condition = build_condition_data(popular_models, 'model') do |model|
          ["used_car/#{model.maker_nm.downcase}/#{model.model_nm.downcase}/?#{@price_range_query}", "#{model.maker_nm} #{model.model_nm}"]
        end
        { title: 'Popular Models', condition: condition }
      end

      def combined_facet_queries
        queries = []

        JAPAN_MAKER.each_with_index do |maker, index|
          queries << ["maker_#{index}", "MakeID:#{maker[:maker_id]} AND Price:#{price_range[:value]}"]
        end

        @body_style.each_with_index do |body_style, index|
          queries << ["body_style_#{index}", "BodyStyle1:#{body_style[:id]} AND Price:#{price_range[:value]}"]
        end

        @option.each_with_index do |option, index|
          queries << ["option_#{index}", "#{option[:solr_query]} AND Price:#{price_range[:value]}"]
        end

        popular_models.each_with_index do |model, index|
          queries << ["model_#{index}", "MakeID:#{model[:maker_id]} AND ModelID:#{model[:model_id]} AND Price:#{price_range[:value]}"]
        end

        queries.to_h
      end

      def popular_models
        @popular_models ||= @popular_for_all_make_service.list_all_model_in_price_range(price_range[:master_info_id])
      end
    end
  end
end
