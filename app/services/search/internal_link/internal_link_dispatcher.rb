module Search
  module InternalLink
    class InternalLinkDispatcher
      def initialize(params, user_country_code, current_user_id, make = nil, model = nil)
        @params = params
        @user_country_code = user_country_code
        @current_user_id = current_user_id
        @make = make
        @model = model
      end

      def call
        case url_context
        when :all_make_with_params
          handle_all_make_with_params
        when :make_with_params
          handle_make_with_params
        when :model_code_exist
          handle_model_code_exist
        end
      end

      private

      def url_context
        return :all_make_with_params if @make.blank?
        return :model_code_exist unless contain_additional_search_params?('mdlnum')

        :make_with_params if @model.blank?
      end

      def handle_all_make_with_params
        # /all/all/{params}
        case detected_param_type
        when :body_type
          Search::InternalLink::OnlyBodyType.new(@user_country_code, @params[:bsty], @current_user_id)
        when :price_range
          price_params = @params.permit(:prcf, :prct)
          Search::InternalLink::OnlyPriceRange.new(price_params)
        when :category
          category_data = find_category_data
          Search::InternalLink::OnlyCategory.new(category_data[:param_name], category_data[:param_value])
        end
      end

      def handle_make_with_params
        # /#{make}/all/{params}
        case detected_param_type
        when :body_type
          Search::InternalLink::MakeBodyStyle.new(@make, @params[:bsty])
        when :price_range
          price_params = @params.permit(:prcf, :prct)
          Search::InternalLink::MakePriceRange.new(@make, price_params)
        when :category
          category_data = find_category_data
          Search::InternalLink::MakerCategory.new(@make, category_data[:param_name], category_data[:param_value])
        end
      end

      def handle_model_code_exist
        Search::InternalLink::ModelCode.new(@make, @model, @params[:make_model_code])
      end

      def detected_param_type
        return :body_type if body_type_condition?
        return :price_range if price_range_condition?
        return :category if category_condition?

        nil
      end

      def body_type_condition?
        return false if @params[:bsty].blank? || @params[:bsty].include?('*')

        !contain_additional_search_params?('bsty')
      end

      def price_range_condition?
        price_params_only? && valid_price_range?
      end

      def category_condition?
        find_category_data.present?
      end

      def price_params_only?
        !contain_additional_search_params?(%w[prcf prct])
      end

      def valid_price_range?
        (@current_price_range_data ||= MasterInfo::StandardPageAllMake.find_price_range(@params[:prcf], @params[:prct])).present?
      end

      def find_category_data
        @find_category_data ||= begin
          category_data = MasterInfo::StandardPageAllMake.find_category_param_and_value(@params)

          if category_data.present? && !contain_additional_search_params?(category_data.param_name)
            {
              param_name: category_data.param_name,
              param_value: category_data.param_value.to_s
            }
          end
        end
      end

      def contain_additional_search_params?(rejected_params = nil)
        keys = @params.keys
        keys << :mdlnum if @params[:make_model_code].present?
        search_params = MasterInfo::SearchKeyMappingSolr.where(key: keys)
        search_params = search_params.reject { |param| Array(rejected_params).include?(param.key) } if rejected_params.present?
        search_params.present?
      end
    end
  end
end
