module Search
  module InternalLink
    class MakerCategory < BaseService
      SECTIONS = {
        price: :handle_price,
        body_style: :handle_body_style
      }.freeze

      def initialize(maker, category_param, category_value)
        super()
        @make = maker
        @category_data = MasterInfo::InternalLink::OtherCategory.find_by_params({ category_param => category_value })
        @category_name = @category_data&.text
      end

      private

      def show_internal_link?
        @category_data.present? && @make.present?
      end

      def cache_key
        "internal_links_maker_category_#{@make.id_make}_#{@category_name}"
      end

      def handle_body_style
        build_maker_section(@body_style, 'body_style', 'Same Brand and Category, Varying BodyStyle')
      end

      def handle_price
        build_maker_section(@price_range, 'price', 'Same Brand and Category, Varying Prices')
      end

      def build_maker_section(collection, type, title)
        condition = build_condition_data(collection, type) do |item|
          [
            "used_car/#{make_name}/all/?#{@category_data.query}&#{item[:query]}",
            "#{make_title} x #{@category_name} x #{item[:text]}",
          ]
        end
        { title: title, condition: condition }
      end

      def combined_facet_queries
        queries = []

        @price_range.each_with_index do |p, i|
          queries << ["price_#{i}", "Price:#{p[:value]} AND MakeID:#{@make.id_make} AND #{@category_data&.solr_query}"]
        end

        @body_style.each_with_index do |bs, i|
          queries << ["body_style_#{i}", "BodyStyle1:#{bs[:id]} AND MakeID:#{@make.id_make} AND #{@category_data&.solr_query}"]
        end

        queries.to_h
      end

      def make_name
        @make_name ||= @make.vc_name_e.downcase
      end

      def make_title
        @make_title ||= @make.vc_name_e.titleize
      end
    end
  end
end
