module Search
  module InternalLink
    class OnlyBodyType < BaseService
      TOP_POPULAR_RANKING = 30
      THREADS_COUNT = 4
      NUMBER_OF_MODEL_TO_SHOW = 8
      FIELD_LISTS = %w[Make Model BodyStyle1 BodyStyle2 Price UserID].freeze

      SECTIONS = {
        recommend_model: :handle_recommend_model,
        japan_maker: :handle_japan_maker,
        price: :handle_price,
        option: :handle_option
      }.freeze

      def initialize(country_code, bsty, current_user_id)
        super()
        @country_code = country_code
        @bsty = bsty
        @body_type_name = MPrimaryBodyStyle.find_by(primary_body_style_id: bsty)&.name
        @search_condition_filter_service = Search::SearchConditionFilter
        @current_user_id = current_user_id
      end

      private

      def show_internal_link?
        single_body_type = @bsty.present? && @bsty.split('*').length == 1
        @bsty = @bsty.to_i == @bsty.to_d ? @bsty.to_i.to_s : @bsty
        only_body_type_1 = @bsty.exclude?('.')
        single_body_type && only_body_type_1
      end

      def cache_key
        "internal_links_body_type_#{@bsty}"
      end

      def handle_recommend_model
        return if fetch_popular_ranking.blank?

        valid_model_ranking = []
        final_query = fetch_popular_ranking.map do |model|
          params = {
            aid: model.make_id,
            oid: model.model_id,
            bsty: @bsty
          }
          @search_condition_filter_service.new(params, @country_code).query_data
        end

        docs = Solr::GroupService.new.group_query(queries: final_query, field_list: FIELD_LISTS)['grouped']
        valid_objects = docs.values.select { |doc| doc.dig('doclist', 'numFound').positive? }
        return if valid_objects.empty?

        valid_objects.each do |obj|
          car = obj.dig('doclist', 'docs')[0]
          valid_model_ranking << car if valid_car?(car)
        end
        return if valid_model_ranking.empty?

        models = valid_model_ranking.first(NUMBER_OF_MODEL_TO_SHOW).map { |car| [car['Make'], car['Model']] }
        condition = models.map { |item| [["used_car/#{item[0].downcase}/#{item[1].downcase}/", "#{item[0]} #{item[1]}"], 1] }

        { title: "Recommended model of #{@body_type_name}", condition: condition }
      end

      def handle_japan_maker
        condition = build_condition_data(JAPAN_MAKER, 'maker') do |popular_make|
          ["used_car/#{popular_make[:maker_name].downcase}/all/?bsty=#{@bsty}", "#{popular_make[:maker_name]} x #{@body_type_name}"]
        end
        { title: "Make x #{@body_type_name}", condition: condition }
      end

      def handle_price
        condition = build_condition_data(@price_range, 'price') do |price_range|
          ["used_car/all/all/?#{price_range.query}&bsty=#{@bsty}", "#{@body_type_name} x #{price_range.text}"]
        end
        { title: "#{@body_type_name} x Price (FOB-US$)", condition: condition }
      end

      def handle_option
        condition = build_condition_data(@option, 'option') do |option|
          ["used_car/all/all/?#{option.query}&bsty=#{@bsty}", "#{@body_type_name} x #{option.text}"]
        end
        { title: "#{@body_type_name} x Option", condition: condition }
      end

      def combined_facet_queries
        queries = []

        @price_range.each_with_index do |p, i|
          queries << ["price_#{i}", "Price:#{p.value} AND BodyStyle1:#{@bsty}"]
        end

        JAPAN_MAKER.each_with_index do |m, i|
          queries << ["maker_#{i}", "MakeID:#{m[:maker_id]} AND BodyStyle1:#{@bsty}"]
        end

        @option.each_with_index do |o, i|
          queries << ["option_#{i}", "#{o.solr_query} AND BodyStyle1:#{@bsty}"]
        end

        queries.to_h
      end

      def fetch_popular_ranking
        @fetch_popular_ranking ||= TPopularRanking.fetch_popular_ranking(@country_code, TOP_POPULAR_RANKING)
          .select(:make_id, :model_id)
      end

      def valid_car?(car)
        car['Price'].to_i != ASK_PRICE && car['UserID'].to_i != @current_user_id
      end
    end
  end
end
