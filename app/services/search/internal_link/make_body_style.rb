module Search
  module InternalLink
    class MakeBodyStyle < BaseService
      SECTIONS = {
        popular_models: :handle_popular_models,
        body_style: :handle_body_style,
        price_range: :handle_price_range,
        options: :handle_options
      }.freeze

      def initialize(car_make, body_style_id)
        super()
        @car_make = car_make
        @body_style = MasterInfo::InternalLink::BodyStyle.where.not(id: flatten_number(body_style_id.to_f))
        @current_body_style = MasterInfo::InternalLink::BodyStyle.find_by(id: flatten_number(body_style_id.to_f))
        @popular_for_all_make_service = Search::GetPopularForAllMakeService.new
      end

      private

      def handle_popular_models
        return if popular_models.blank?

        condition = build_condition_data(popular_models, 'model') do |model|
          [
            "used_car/#{model.maker_nm.downcase}/#{model.model_nm.downcase}/?bsty=#{@current_body_style.id}",
            "#{model.maker_nm} #{model.model_nm}",
          ]
        end

        { title: "#{@car_make.vc_name_e} x #{@current_body_style.text} Popular Models", condition: }
      end

      def popular_models
        @popular_models ||= @popular_for_all_make_service.list_all_model_in_maker_body_style(@car_make.id, @current_body_style.id)
      end

      def handle_body_style
        condition = build_condition_data(@body_style, 'body_style') do |body_style|
          ["used_car/#{@car_make.vc_name_e.downcase}/all/?#{body_style[:query]}", "#{@car_make.vc_name_e} x #{body_style[:text]}"]
        end

        { title: 'Same Brand, Varying Body Styles', condition: }
      end

      def handle_price_range
        condition = build_condition_data(@price_range, 'price') do |price_range|
          [
            "used_car/#{@car_make.vc_name_e.downcase}/all/?#{@current_body_style.query}&#{price_range[:query]}",
            "#{@car_make.vc_name_e} x #{@current_body_style.text} x #{price_range[:text]}",
          ]
        end

        { title: 'Same Brand and Body Type, Varying Prices', condition: }
      end

      def handle_options
        condition = build_condition_data(@option, 'option') do |option|
          [
            "used_car/#{@car_make.vc_name_e.downcase}/all/?#{@current_body_style.query}&#{option[:query]}",
            "#{@car_make.vc_name_e} x #{@current_body_style.text} x #{option[:text]}",
          ]
        end

        { title: 'Same Brand and Body Type, Varying Features', condition: }
      end

      def combined_facet_queries
        queries = []

        @body_style.each_with_index do |bs, i|
          queries << ["body_style_#{i}", "BodyStyle1:#{bs[:id]} AND MakeID:#{@car_make.id_make}"]
        end

        @price_range.each_with_index do |p, i|
          queries << ["price_#{i}", "Price:#{p[:value]}  AND MakeID:#{@car_make.id_make} AND BodyStyle1:#{@current_body_style.id}"]
        end

        @option.each_with_index do |option, index|
          queries << ["option_#{index}", "#{option[:solr_query]} AND MakeID:#{@car_make.id_make} AND BodyStyle1:#{@current_body_style.id}"]
        end

        if popular_models.present?
          popular_models.each_with_index do |model, index|
            solr_query = "MakeID:#{model[:maker_id]} AND ModelID:#{model[:model_id]} AND " \
                         "BodyStyle1:#{@current_body_style.id}"
            queries << ["model_#{index}", solr_query]
          end
        end

        queries.to_h
      end

      def show_internal_link?
        @current_body_style.present?
      end

      def cache_key
        "internal_link_make_body_type_#{@car_make.vc_name_e.downcase}_#{@current_body_style.text.downcase}"
      end

      def flatten_number(number)
        (number % 1).zero? ? number.to_i : number
      end
    end
  end
end
