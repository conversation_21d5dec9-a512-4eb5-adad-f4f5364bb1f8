module Search
  module InternalLink
    class ModelCode < BaseService
      include SearchModelCode

      SECTIONS = {
        same_model_different_variants: :handle_same_model_different_variants,
        same_variant_different_years: :handle_same_variant_different_years,
        same_model_different_price_ranges: :handle_same_model_different_price_ranges
      }.freeze

      def initialize(make, model, make_model_code)
        super()
        @make = make
        @model = model
        @model_code = parse_model_code(make, model, make_model_code)
        @model_code_show_text = @model_code&.model_code_show_text(make_model_code)
      end

      private

      def show_internal_link?
        @model_code.present?
      end

      def cache_key
        "internal_links_model_code_#{@model.id_model}_#{@model_code_show_text}"
      end

      def combined_facet_queries
        queries = []

        # Same Model, Different Variants
        same_model_variants.each_with_index do |model_code, index|
          queries << ["same_model_#{index}", model_code_query(model_code)]
        end

        # Same Model and Variant, Different Price Ranges
        @price_range.each_with_index do |price_range, index|
          queries << ["price_range_#{index}",
                      "#{model_code_query(@model_code_show_text)} AND Price:#{price_range[:value]}"]
        end

        queries.to_h
      end

      def handle_same_model_different_variants
        condition = build_condition_data(same_model_variants, 'same_model') do |model_code|
          [model_code_path(model_code), model_code]
        end
        { title: 'Same Model, Different Variants', condition: condition, show_count: true }
      end

      def handle_same_variant_different_years
        data = @solr_facet.call_facet(group_field_name: 'ModelYear', query: model_code_query(@model_code_show_text))
        facet_counts = data.dig('facet_counts', 'facet_pivot', 'ModelYear')&.sort_by { |item| item['value'] }
        return if facet_counts.blank?

        condition = facet_counts.map do |facet_count|
          year, count = facet_count.values_at('value', 'count')
          condition_pair = ["#{model_code_path(@model_code_show_text)}?fid=#{year}&jid=#{year}",
                            "#{@model_code_show_text} x #{year}"]

          [condition_pair, count]
        end
        { title: 'Same Model and Variant, Different Model Years', condition: condition, show_count: true }
      end

      def handle_same_model_different_price_ranges
        condition = build_condition_data(@price_range, 'price_range') do |price_range|
          ["#{model_code_path(@model_code_show_text)}?#{price_range[:query]}",
           "#{@model_code_show_text} x #{price_range[:text]}"]
        end
        { title: 'Same Model and Variant, Different Price Ranges', condition: condition, show_count: true }
      end

      def same_model_variants
        @same_model_variants ||= @model_code.model_t_trims.same_variants(@model_code_show_text)
      end

      def model_code_path(model_code)
        "used_car/#{make_name}/#{model_name}/#{make_name}_#{model_code}/"
      end

      def model_code_query(model_code)
        "ModelNumber:\"#{model_code}\" AND ModelID:#{@model.id_model}"
      end

      def make_name
        @make_name ||= @make.vc_name_e.downcase
      end

      def model_name
        @model_name ||= @model.vc_name_e.downcase
      end
    end
  end
end
