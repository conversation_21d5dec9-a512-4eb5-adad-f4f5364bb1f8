module Search
  module InternalLink
    class OnlyCategory < BaseService
      SECTIONS = {
        maker: :handle_maker,
        body_style: :handle_body_style,
        price: :handle_price
      }.freeze

      def initialize(category_param, category_value)
        super()
        @category_data = MasterInfo::InternalLink::OtherCategory.find_by_params({ category_param => category_value })
        @category_name = @category_data&.text
      end

      private

      def show_internal_link?
        @category_data.present?
      end

      def cache_key
        "internal_links_category_#{@category_name}"
      end

      def handle_maker
        condition = build_condition_data(JAPAN_MAKER, 'maker') do |popular_make|
          ["used_car/#{popular_make[:maker_name].downcase}/all/?#{@category_data.query}",
           "#{popular_make[:maker_name]} x #{@category_name}"]
        end
        { title: "Make x #{@category_name}", condition: condition }
      end

      def handle_body_style
        condition = build_condition_data(@body_style, 'body_style') do |body_style|
          ["used_car/all/all/?#{body_style[:query]}&#{@category_data.query}", "#{body_style[:text]} x #{@category_name}"]
        end
        { title: "BodyStyles x #{@category_name}", condition: condition }
      end

      def handle_price
        condition = build_condition_data(@price_range, 'price') do |price_range|
          ["used_car/all/all/?#{price_range[:query]}&#{@category_data.query}", "#{price_range[:text]} x #{@category_name}"]
        end
        { title: "Car Price(FOB) x #{@category_name}", condition: condition }
      end

      def combined_facet_queries
        queries = []

        @price_range.each_with_index do |p, i|
          queries << ["price_#{i}", "Price:#{p[:value]} AND #{@category_data&.solr_query}"]
        end

        JAPAN_MAKER.each_with_index do |m, i|
          queries << ["maker_#{i}", "MakeID:#{m[:maker_id]} AND #{@category_data&.solr_query}"]
        end

        @body_style.each_with_index do |bs, i|
          queries << ["body_style_#{i}", "BodyStyle1:#{bs[:id]} AND #{@category_data&.solr_query}"]
        end

        queries.to_h
      end
    end
  end
end
