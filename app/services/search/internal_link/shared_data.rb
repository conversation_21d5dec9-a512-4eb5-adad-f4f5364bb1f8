module Search
  module InternalLink
    module SharedData
      JAPAN_MAKER = [
        { maker_id: 1, maker_name: 'Toyota' },
        { maker_id: 2, maker_name: 'Nissan' },
        { maker_id: 3, maker_name: 'Honda' },
        { maker_id: 5, maker_name: 'Mazda' },
        { maker_id: 4, maker_name: 'Mitsubishi' },
      ].freeze

      private

      def load_shared_data
        @price_range ||= MasterInfo::InternalLink::PriceRange.all
        @body_style ||= MasterInfo::InternalLink::BodyStyle.all
        @option ||= MasterInfo::InternalLink::OtherCategory.all
      end
    end
  end
end
