module Charts
  class ExportPerformanceRateQuery < BaseChart
    def initialize(param_name, param_value)
      @param_name = param_name
      @param_value = param_value
      super(@param_name, @param_value)
    end

    private

    def data
      @data ||= ::TExportPerformanceQuery.export_performance_12_months(@param_name, @param_value)
    end

    def generate_labels
      latest_stock_date = Date.today
      12.downto(1).map { |month| (latest_stock_date.beginning_of_month - month.months).strftime('%Y/%m') }
    end

    def generate_data_values
      counts_by_month = data.each_with_object({}) do |record, hash|
        formatted_date = record.collection_month.strftime('%Y/%m')
        number_count = record.individual_count
        hash[formatted_date] = ((number_count.to_f / record.all_count) * 100).round(1)
      end

      generate_labels.map { |month| counts_by_month[month] || 0 }
    end
  end
end
