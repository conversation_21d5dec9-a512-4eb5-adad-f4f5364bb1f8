module Charts
  class ExportPerformanceRate < BaseChart
    def initialize(make_id, model_id)
      @make_id = make_id
      @model_id = model_id || 0
      super(@make_id, @model_id)
    end

    private

    def data
      @data ||= ::TExportPerformance.export_performance_12_months(@make_id, @model_id)
    end

    def generate_labels
      latest_stock_date = Date.today
      12.downto(1).map { |month| (latest_stock_date.beginning_of_month - month.months).strftime('%Y/%m') }
    end

    def generate_data_values
      counts_by_month = data.each_with_object({}) do |record, hash|
        formatted_date = record.stock_date.strftime('%Y/%m')
        number_count = @model_id.zero? ? record.make_money_receive_count : record.model_money_receive_count
        hash[formatted_date] = ((number_count.to_f / record.all_money_receive_count) * 100).round(1)
      end

      generate_labels.map { |month| counts_by_month[month] || 0 }
    end
  end
end
