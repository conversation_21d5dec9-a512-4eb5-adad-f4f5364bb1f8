module Charts
  class StockCount < BaseChart
    def initialize(make_id, model_id)
      @make_id = make_id
      @model_id = model_id || 0
      super(@make_id, @model_id)
    end

    private

    def data
      @data ||= ::TListingNumber.stock_count_30_days(@make_id, @model_id)
    end

    def generate_labels
      latest_stock_date = Date.today
      30.downto(1).map { |day| latest_stock_date - day }
    end

    def generate_data_values
      data_hash = data.each_with_object({}) do |item, hash|
        hash[item.stock_date.to_date] = item.make_model_stock
      end

      generate_labels.map { |date| data_hash.fetch(date, 0) }
    end
  end
end
