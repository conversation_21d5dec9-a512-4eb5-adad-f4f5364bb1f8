module Charts
  class BaseChart
    attr_reader :last_updated

    def initialize(*args)
      @args = args
    end

    def build_data
      {
        last_updated: format_last_updated,
        data: {
          labels: generate_labels,
          datasets: [{
            data: generate_data_values
          }]
        }
      }
    end

    private

    def format_last_updated
      data&.first&.update_date&.strftime('%Y/%m/%d')
    end

    def data
      raise NotImplementedError, 'Subclasses must implement #data'
    end

    def generate_labels
      raise NotImplementedError, 'Subclasses must implement #generate_labels'
    end

    def generate_data_values
      raise NotImplementedError, 'Subclasses must implement #generate_data_values'
    end
  end
end
