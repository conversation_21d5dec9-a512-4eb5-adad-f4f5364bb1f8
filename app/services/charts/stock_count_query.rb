module Charts
  class StockCountQuery < BaseChart
    def initialize(param_name, param_value)
      @param_name = param_name
      @param_value = param_value
      super(@param_name, @param_value)
    end

    private

    def data
      @data ||= ::TListingNumberQuery.stock_count_30_days(@param_name, @param_value)
    end

    def generate_labels
      latest_stock_date = Date.today
      30.downto(1).map { |day| latest_stock_date - day }
    end

    def generate_data_values
      data_hash = data.each_with_object({}) do |item, hash|
        hash[item.collection_day.to_date] = item.stock_count
      end

      generate_labels.map { |date| data_hash.fetch(date, 0) }
    end
  end
end
