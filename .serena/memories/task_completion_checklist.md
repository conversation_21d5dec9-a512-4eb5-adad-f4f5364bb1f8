# Task Completion Checklist for TCV Web V2

## Before Committing Code
- [ ] **Code Style**: Run Rubocop to ensure code follows style guidelines
  ```bash
  docker container run --rm -t -u "$(id -u):$(id -g)" -v "$PWD":/work:ro ghcr.io/shakiyam/rubocop
  ```

- [ ] **Tests**: Run RSpec tests to ensure all tests pass
  ```bash
  docker-compose exec web bundle exec rspec
  ```

- [ ] **Security**: <PERSON>eman for security analysis
  ```bash
  docker-compose exec web bundle exec brakeman
  ```

- [ ] **Dependencies**: Run Bundler audit for gem vulnerabilities
  ```bash
  docker-compose exec web bundle exec bundle-audit
  ```

## For New Features
- [ ] **Database**: Run migrations if database schema changed
  ```bash
  docker-compose exec web bundle exec rails db:migrate
  ```

- [ ] **Frontend**: Build assets if JavaScript/CSS changed
  ```bash
  docker-compose exec web yarn build
  ```

- [ ] **Documentation**: Update README if needed

## For Bug Fixes
- [ ] **Reproduction**: Ensure bug can be reproduced
- [ ] **Test Coverage**: Add tests for the fix
- [ ] **Regression**: Ensure fix doesn't break existing functionality

## Before Deployment
- [ ] **Environment**: Ensure all environment variables are set
- [ ] **Database**: Run migrations on production
- [ ] **Assets**: Precompile assets for production
- [ ] **Health Check**: Verify application starts correctly

## Code Review Checklist
- [ ] **Functionality**: Code works as intended
- [ ] **Performance**: No obvious performance issues
- [ ] **Security**: No security vulnerabilities
- [ ] **Maintainability**: Code is readable and well-structured
- [ ] **Testing**: Adequate test coverage
- [ ] **Documentation**: Code is self-documenting or documented

## Git Workflow
- [ ] **Branch**: Create feature branch from main
- [ ] **Commits**: Make atomic, descriptive commits
- [ ] **Pull Request**: Create PR with clear description
- [ ] **Review**: Address review comments
- [ ] **Merge**: Merge only after approval and tests pass