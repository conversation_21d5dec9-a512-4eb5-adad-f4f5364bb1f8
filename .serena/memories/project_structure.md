# TCV Web V2 Project Structure

## Root Directory
```
tcv-web-v2/
├── app/                    # Main application code
│   ├── controllers/        # Rails controllers
│   ├── models/            # ActiveRecord models
│   ├── views/             # Slim templates
│   ├── helpers/           # View helpers
│   ├── services/          # Business logic services
│   ├── jobs/              # Background jobs
│   ├── mailers/           # Email functionality
│   ├── presenters/        # View presenters
│   ├── assets/            # Stylesheets, images
│   ├── javascript/        # JavaScript modules
│   ├── channels/          # ActionCable channels
│   └── middlewares/       # Custom middleware
├── config/                # Rails configuration
├── db/                    # Database migrations and seeds
├── lib/                   # Custom libraries
├── spec/                  # RSpec tests
├── public/                # Static files
├── bin/                   # Executable scripts
├── docker/                # Docker configuration
├── data/                  # Data files
└── tmp/                   # Temporary files
```

## Key Configuration Files
- `Gemfile` - Ruby dependencies
- `package.json` - JavaScript dependencies
- `docker-compose.yml` - Container orchestration
- `.rubocop.yml` - Code style configuration
- `config.ru` - Rack configuration
- `Rakefile` - Rake tasks

## Development Tools
- **Docker**: Containerized development environment
- **Webpacker**: Asset compilation
- **RSpec**: Testing framework
- **Rubocop**: Code style enforcement
- **Brakeman**: Security analysis
- **Bundler Audit**: Dependency vulnerability checking

## Database Structure
- **Primary Database**: MySQL 8.0
- **Cache**: Redis 6.0
- **Models**: Extensive car-related models (makes, models, listings, offers)
- **Master Data**: Import system for car data

## Frontend Structure
- **JavaScript**: Webpacker 5.3.0 with Stimulus
- **CSS**: SCSS with PostCSS
- **Templates**: Slim templates
- **Assets**: Chart.js, jQuery, Bootstrap components

## Cloud Integration
- **Google Cloud Tasks**: Background job processing
- **Google Cloud Storage**: File storage
- **Stackdriver**: Logging and monitoring
- **Cloud Build**: CI/CD pipeline