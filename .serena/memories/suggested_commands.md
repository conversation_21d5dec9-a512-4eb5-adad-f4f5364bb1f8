# Suggested Commands for TCV Web V2 Development

## Development Setup
```bash
# Start database container first
docker-compose up db

# Start all containers (development)
docker-compose up

# Start containers in background
docker-compose up -d

# Stop containers
docker-compose stop

# Start containers again
docker-compose start
```

## Database Operations
```bash
# Import master data (after web container is ready)
docker-compose exec web rake import_master_data:carsensor

# Create test database
docker-compose exec web bundle exec rails db:create RAILS_ENV=test

# Run migrations for test environment
docker-compose exec web bundle exec rails db:migrate RAILS_ENV=test
```

## Testing
```bash
# Run all tests
docker-compose exec web bundle exec rspec

# Run tests with the provided script
./run-tests.sh

# Run specific test file
docker-compose exec web bundle exec rspec spec/path/to/test_spec.rb
```

## Code Quality
```bash
# Run Rubocop for code style checking
docker container run --rm -t -u "$(id -u):$(id -g)" -v "$PWD":/work:ro ghcr.io/shakiyam/rubocop

# Run Brakeman for security analysis
docker-compose exec web bundle exec brakeman

# Run Bundler audit for gem vulnerabilities
docker-compose exec web bundle exec bundle-audit
```

## Rails Commands
```bash
# Rails console
docker-compose exec web bundle exec rails console

# Rails server (if not using docker-compose)
bundle exec rails server

# Generate new model
docker-compose exec web bundle exec rails generate model ModelName

# Generate new controller
docker-compose exec web bundle exec rails generate controller ControllerName

# Run migrations
docker-compose exec web bundle exec rails db:migrate
```

## Frontend Development
```bash
# Start webpack dev server
docker-compose exec web yarn start

# Build frontend assets
docker-compose exec web yarn build
```

## Git Operations
```bash
# Check status
git status

# Add files
git add .

# Commit changes
git commit -m "Your commit message"

# Push changes
git push origin main
```

## Docker Operations
```bash
# Build development image
docker-compose build

# View logs
docker-compose logs web

# Execute commands in container
docker-compose exec web bash

# Clean up containers and volumes
docker-compose down -v
```