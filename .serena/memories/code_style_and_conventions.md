# Code Style and Conventions for TCV Web V2

## Ruby/Rails Conventions
- **Ruby Version**: 3.2.2
- **Rails Version**: 7.1.5.1
- **Indentation**: 2 spaces (not tabs)
- **Line Length**: Maximum 150 characters
- **String Literals**: Use single quotes by default (enforced by Rubocop)
- **Naming**: Use snake_case for methods and variables, CamelCase for classes

## Rubocop Configuration
- **Style/StringLiterals**: Enforced to use single quotes
- **Layout/LineLength**: Max 150 characters
- **Style/TrailingCommaInArguments**: Enforced for multiline
- **Style/TrailingCommaInArrayLiteral**: Enforced for multiline
- **Documentation**: Disabled for classes and modules
- **FrozenStringLiteralComment**: Disabled

## File Organization
- **Models**: `app/models/` - Use singular, CamelCase names
- **Controllers**: `app/controllers/` - Use plural, CamelCase names
- **Views**: `app/views/` - Use Slim templates
- **Services**: `app/services/` - For business logic
- **Jobs**: `app/jobs/` - For background jobs
- **Mailers**: `app/mailers/` - For email functionality
- **Presenters**: `app/presenters/` - For view logic
- **Helpers**: `app/helpers/` - For view helpers
- **Assets**: `app/assets/` - For stylesheets and images
- **JavaScript**: `app/javascript/` - For JS modules

## Database Conventions
- **Table Names**: Plural, snake_case (e.g., `users`, `car_listings`)
- **Model Names**: Singular, CamelCase (e.g., `User`, `CarListing`)
- **Migration Files**: Timestamped with descriptive names

## Testing Conventions
- **Framework**: RSpec with Capybara
- **Factories**: Use Factory Bot for test data
- **Matchers**: Use Shoulda Matchers for model validations
- **Coverage**: Use SimpleCov for coverage reporting
- **Test Files**: `spec/` directory mirroring app structure

## Frontend Conventions
- **JavaScript**: ES6+ syntax, camelCase for variables/functions
- **CSS**: Use SCSS, kebab-case for class names
- **Webpacker**: Version 5.3.0 for asset compilation
- **Stimulus**: For JavaScript controllers
- **jQuery**: Available for DOM manipulation

## Git Conventions
- **Branch Naming**: Use descriptive names (e.g., `feature/user-authentication`)
- **Commit Messages**: Use present tense, descriptive messages
- **Pull Requests**: Include description of changes and testing done

## Environment Configuration
- **Environment Variables**: Use `.env` file for local development
- **Application Config**: Use `config/application.yml` for Rails config
- **Google Cloud**: Configure credentials via `GOOGLE_APPLICATION_CREDENTIALS`

## Security Practices
- **Brakeman**: Run security analysis regularly
- **Bundler Audit**: Check for gem vulnerabilities
- **Environment Variables**: Never commit sensitive data
- **Database**: Use prepared statements and validations