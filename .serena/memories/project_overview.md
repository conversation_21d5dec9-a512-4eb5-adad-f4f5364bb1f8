# TCV Web V2 Project Overview

## Project Purpose
This is a Ruby on Rails web application called "TradeCarView Web V2" - a car trading/marketplace platform. The project appears to be focused on car listings, offers, and trading information with international scope.

## Tech Stack
- **Backend**: Ruby on Rails ******* with Ruby 3.2.2
- **Database**: MySQL 8.0
- **Frontend**: Webpacker 5.3.0, JavaScript with jQuery, Stimulus, Chart.js
- **Caching**: Redis 6.0
- **Containerization**: Docker with docker-compose
- **Cloud**: Google Cloud Platform integration (Cloud Tasks, Cloud Storage, Stackdriver)
- **Template Engine**: Slim templates
- **Testing**: RSpec with Capybara, Factory Bot, Shoulda Matchers

## Key Features
- Car search and listing functionality
- International car trading/marketplace
- User management and sessions
- Offer management system
- Master data management for cars, makes, models
- Multi-language support
- Google Cloud integration for tasks and storage
- Email functionality with Mailcatcher for development

## Development Environment
- Uses Docker containers for development
- MySQL database container
- Redis for caching
- Webpacker dev server for frontend assets
- Mailcatcher for email testing
- Google Cloud Tasks emulator