#!/usr/bin/env ruby

# Script để debug vấn đề maker_nm undefined method
# Chạy script này trong Rails console hoặc như một standalone script

puts "=== DEBUG MAKER_NM ISSUE ==="
puts "Rails environment: #{Rails.env}"
puts "Time: #{Time.current}"

# 1. Kiểm tra schema và table structure
puts "\n1. Checking table structure..."
begin
  columns = ActiveRecord::Base.connection.columns('t_aggregate_ranking_points')
  puts "Columns in t_aggregate_ranking_points:"
  columns.each do |col|
    puts "  - #{col.name}: #{col.type} (#{col.sql_type})"
  end
rescue => e
  puts "ERROR checking table structure: #{e.message}"
end

# 2. Kiểm tra model attributes
puts "\n2. Checking model attributes..."
begin
  puts "TAggregateRankingPoint.attribute_names:"
  puts TAggregateRankingPoint.attribute_names.inspect
  
  puts "\nTAggregateRankingPoint.column_names:"
  puts TAggregateRankingPoint.column_names.inspect
  
  puts "\nTAggregateRankingPoint.columns_hash keys:"
  puts TAggregateRankingPoint.columns_hash.keys.inspect
rescue => e
  puts "ERROR checking model attributes: #{e.message}"
end

# 3. Kiểm tra schema cache
puts "\n3. Checking schema cache..."
begin
  cache = ActiveRecord::Base.connection.schema_cache
  puts "Schema cache class: #{cache.class}"
  puts "Schema cache size: #{cache.size}" if cache.respond_to?(:size)
  
  # Kiểm tra cached columns cho table này
  cached_columns = cache.columns('t_aggregate_ranking_points') if cache.respond_to?(:columns)
  if cached_columns
    puts "Cached columns for t_aggregate_ranking_points:"
    cached_columns.each do |col|
      puts "  - #{col.name}: #{col.type}"
    end
  else
    puts "No cached columns found or method not available"
  end
rescue => e
  puts "ERROR checking schema cache: #{e.message}"
end

# 4. Test tạo và truy cập object
puts "\n4. Testing object creation and access..."
begin
  # Tìm một record có sẵn
  record = TAggregateRankingPoint.first
  if record
    puts "Found record with ID: #{record.id}"
    puts "Record attributes: #{record.attributes.keys.inspect}"
    
    # Test truy cập các attributes
    puts "\nTesting attribute access:"
    
    begin
      puts "  maker_id: #{record.maker_id}"
    rescue => e
      puts "  ERROR accessing maker_id: #{e.message}"
    end
    
    begin
      puts "  model_id: #{record.model_id}"
    rescue => e
      puts "  ERROR accessing model_id: #{e.message}"
    end
    
    begin
      puts "  maker_nm: #{record.maker_nm}"
    rescue => e
      puts "  ERROR accessing maker_nm: #{e.message}"
      puts "  ERROR class: #{e.class}"
      puts "  ERROR backtrace: #{e.backtrace.first(5).join("\n")}"
    end
    
    begin
      puts "  model_nm: #{record.model_nm}"
    rescue => e
      puts "  ERROR accessing model_nm: #{e.message}"
    end
    
    # Test method_missing
    puts "\nTesting method existence:"
    puts "  respond_to?(:maker_nm): #{record.respond_to?(:maker_nm)}"
    puts "  respond_to?(:model_nm): #{record.respond_to?(:model_nm)}"
    puts "  respond_to?(:maker_id): #{record.respond_to?(:maker_id)}"
    
    # Test direct attribute access
    puts "\nTesting direct attribute access:"
    puts "  record[:maker_nm]: #{record[:maker_nm]}"
    puts "  record['maker_nm']: #{record['maker_nm']}"
    puts "  record.attributes['maker_nm']: #{record.attributes['maker_nm']}"
    
  else
    puts "No records found in t_aggregate_ranking_points table"
  end
rescue => e
  puts "ERROR testing object: #{e.message}"
  puts "ERROR class: #{e.class}"
  puts "ERROR backtrace: #{e.backtrace.first(10).join("\n")}"
end

# 5. Test query với specific record từ error message
puts "\n5. Testing specific record from error message..."
begin
  record = TAggregateRankingPoint.find(1000074)
  if record
    puts "Found specific record ID 1000074"
    puts "Attributes: #{record.attributes.inspect}"
    
    # Test từng attribute một cách cẩn thận
    %w[id maker_id model_id maker_nm model_nm ranking_point create_date update_date].each do |attr|
      begin
        value = record.send(attr)
        puts "  #{attr}: #{value}"
      rescue => e
        puts "  ERROR accessing #{attr}: #{e.message}"
      end
    end
  else
    puts "Record ID 1000074 not found"
  end
rescue ActiveRecord::RecordNotFound
  puts "Record ID 1000074 not found in database"
rescue => e
  puts "ERROR finding specific record: #{e.message}"
end

# 6. Test reload schema cache
puts "\n6. Testing schema cache reload..."
begin
  puts "Clearing schema cache..."
  ActiveRecord::Base.connection.schema_cache.clear!
  
  puts "Reloading model..."
  TAggregateRankingPoint.reset_column_information
  
  puts "Testing after reload..."
  record = TAggregateRankingPoint.first
  if record
    puts "  maker_nm after reload: #{record.maker_nm}"
  end
rescue => e
  puts "ERROR during schema cache reload: #{e.message}"
end

# 7. Test raw SQL query
puts "\n7. Testing raw SQL query..."
begin
  result = ActiveRecord::Base.connection.execute(
    "SELECT id, maker_id, model_id, maker_nm, model_nm FROM t_aggregate_ranking_points LIMIT 1"
  )
  
  if result.any?
    row = result.first
    puts "Raw SQL result: #{row.inspect}"
  else
    puts "No results from raw SQL query"
  end
rescue => e
  puts "ERROR with raw SQL: #{e.message}"
end

puts "\n=== DEBUG COMPLETE ==="
