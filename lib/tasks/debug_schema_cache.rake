namespace :debug do
  desc "Debug schema cache issues with TAggregateRankingPoint"
  task schema_cache: :environment do
    puts "=== DEBUGGING SCHEMA CACHE ISSUES ==="
    puts "Environment: #{Rails.env}"
    puts "Time: #{Time.current}"
    
    # 1. Kiểm tra database connection
    puts "\n1. Database Connection Check"
    puts "-" * 40
    begin
      ActiveRecord::Base.connection.execute("SELECT 1")
      puts "✓ Database connection OK"
    rescue => e
      puts "✗ Database connection failed: #{e.message}"
      exit 1
    end
    
    # 2. Kiểm tra table structure
    puts "\n2. Table Structure Check"
    puts "-" * 40
    begin
      columns = ActiveRecord::Base.connection.columns('t_aggregate_ranking_points')
      puts "✓ Table exists with #{columns.length} columns:"
      columns.each do |col|
        puts "  - #{col.name}: #{col.type}"
      end
    rescue => e
      puts "✗ Failed to get table structure: #{e.message}"
      exit 1
    end
    
    # 3. Kiểm tra model attributes
    puts "\n3. Model Attributes Check"
    puts "-" * 40
    begin
      puts "Model attribute names: #{TAggregateRankingPoint.attribute_names.inspect}"
      puts "Model column names: #{TAggregateRankingPoint.column_names.inspect}"
      
      # Kiểm tra specific attributes
      required_attrs = %w[maker_nm model_nm maker_id model_id]
      required_attrs.each do |attr|
        if TAggregateRankingPoint.attribute_names.include?(attr)
          puts "✓ #{attr} attribute exists"
        else
          puts "✗ #{attr} attribute missing"
        end
      end
    rescue => e
      puts "✗ Failed to check model attributes: #{e.message}"
    end
    
    # 4. Test record access
    puts "\n4. Record Access Test"
    puts "-" * 40
    begin
      record = TAggregateRankingPoint.first
      if record
        puts "✓ Found record with ID: #{record.id}"
        
        # Test từng attribute
        test_attrs = %w[id maker_id model_id maker_nm model_nm ranking_point]
        test_attrs.each do |attr|
          begin
            value = record.send(attr)
            puts "✓ #{attr}: #{value}"
          rescue => attr_error
            puts "✗ #{attr}: ERROR - #{attr_error.message}"
          end
        end
      else
        puts "✗ No records found in table"
      end
    rescue => e
      puts "✗ Failed to access records: #{e.message}"
      puts "Error class: #{e.class}"
      puts "Backtrace: #{e.backtrace.first(3).join("\n")}"
    end
    
    # 5. Test schema cache
    puts "\n5. Schema Cache Test"
    puts "-" * 40
    begin
      cache = ActiveRecord::Base.connection.schema_cache
      puts "Schema cache class: #{cache.class}"
      
      # Test cached columns
      cached_columns = cache.columns('t_aggregate_ranking_points')
      puts "Cached columns count: #{cached_columns.length}"
      
      cached_column_names = cached_columns.map(&:name)
      puts "Cached column names: #{cached_column_names.inspect}"
      
      # So sánh với actual columns
      actual_columns = ActiveRecord::Base.connection.columns('t_aggregate_ranking_points')
      actual_column_names = actual_columns.map(&:name)
      
      missing_in_cache = actual_column_names - cached_column_names
      extra_in_cache = cached_column_names - actual_column_names
      
      if missing_in_cache.any?
        puts "✗ Columns missing in cache: #{missing_in_cache.inspect}"
      else
        puts "✓ All actual columns present in cache"
      end
      
      if extra_in_cache.any?
        puts "✗ Extra columns in cache: #{extra_in_cache.inspect}"
      else
        puts "✓ No extra columns in cache"
      end
      
    rescue => e
      puts "✗ Schema cache test failed: #{e.message}"
    end
    
    puts "\n=== DEBUG COMPLETE ==="
  end
  
  desc "Clear and refresh schema cache"
  task refresh_schema_cache: :environment do
    puts "=== REFRESHING SCHEMA CACHE ==="
    
    begin
      # Clear schema cache
      puts "Clearing schema cache..."
      ActiveRecord::Base.connection.schema_cache.clear!
      
      # Reset column information for affected models
      models = [TAggregateRankingPoint, TAggregateOffer, TPopularRanking]
      models.each do |model|
        puts "Resetting column information for #{model.name}..."
        model.reset_column_information
      end
      
      # Preload important tables
      tables = %w[t_aggregate_ranking_points t_aggregate_offers t_popular_rankings]
      tables.each do |table|
        puts "Preloading schema for #{table}..."
        ActiveRecord::Base.connection.columns(table)
      end
      
      puts "✓ Schema cache refresh completed"
      
      # Test after refresh
      puts "\nTesting after refresh..."
      record = TAggregateRankingPoint.first
      if record
        puts "✓ Can access maker_nm: #{record.maker_nm}"
        puts "✓ Can access model_nm: #{record.model_nm}"
      end
      
    rescue => e
      puts "✗ Schema cache refresh failed: #{e.message}"
      puts e.backtrace.first(5).join("\n")
    end
  end
  
  desc "Reproduce the maker_nm bug"
  task reproduce_bug: :environment do
    puts "=== REPRODUCING MAKER_NM BUG ==="
    
    # Test các scenario có thể gây ra bug
    scenarios = [
      {
        name: "Direct model access",
        code: -> { 
          record = TAggregateRankingPoint.find(1000074) rescue TAggregateRankingPoint.first
          record&.maker_nm
        }
      },
      {
        name: "Query with select",
        code: -> {
          TAggregateRankingPoint.select(:maker_nm, :model_nm).first&.maker_nm
        }
      },
      {
        name: "Query by make and model method",
        code: -> {
          results = TAggregateRankingPoint.query_by_make_and_model([[1, 74]], 1)
          results.first&.maker_nm
        }
      },
      {
        name: "Service usage",
        code: -> {
          service = Search::InternalLinksAllMake.new(1, 'USD')
          models = service.send(:query_ranking_point, TAggregateOffer.limit(1))
          models.first&.maker_nm
        }
      }
    ]
    
    scenarios.each_with_index do |scenario, index|
      puts "\n#{index + 1}. Testing: #{scenario[:name]}"
      puts "-" * 40
      
      begin
        result = scenario[:code].call
        puts "✓ SUCCESS: #{result}"
      rescue => e
        puts "✗ ERROR: #{e.message}"
        puts "Error class: #{e.class}"
        
        if e.message.include?("undefined method `maker_nm'")
          puts "*** THIS IS THE BUG WE'RE LOOKING FOR! ***"
          puts "Backtrace:"
          puts e.backtrace.first(5).join("\n")
        end
      end
    end
    
    puts "\n=== REPRODUCTION TEST COMPLETE ==="
  end
end
