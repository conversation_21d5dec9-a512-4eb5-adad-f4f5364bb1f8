module Monitoring
  class SchemaCacheMoni<PERSON>
    def self.check_health
      results = {
        timestamp: Time.current,
        environment: Rails.env,
        status: 'healthy',
        issues: [],
        details: {}
      }
      
      # 1. Kiểm tra database connection
      begin
        ActiveRecord::Base.connection.execute("SELECT 1")
        results[:details][:database_connection] = 'OK'
      rescue => e
        results[:status] = 'unhealthy'
        results[:issues] << "Database connection failed: #{e.message}"
        results[:details][:database_connection] = 'FAILED'
      end
      
      # 2. Kiểm tra schema cache
      begin
        cache = ActiveRecord::Base.connection.schema_cache
        results[:details][:schema_cache_class] = cache.class.name
        results[:details][:schema_cache_size] = cache.size if cache.respond_to?(:size)
      rescue => e
        results[:status] = 'unhealthy'
        results[:issues] << "Schema cache check failed: #{e.message}"
      end
      
      # 3. Kiểm tra các model quan trọng
      critical_models = [
        { model: TAggregateRankingPoint, table: 't_aggregate_ranking_points' },
        { model: TAggregateOffer, table: 't_aggregate_offers' },
        { model: TPopularRanking, table: 't_popular_rankings' }
      ]
      
      critical_models.each do |model_info|
        model = model_info[:model]
        table = model_info[:table]
        model_name = model.name
        
        begin
          # Kiểm tra column information
          columns = model.columns_hash
          results[:details]["#{model_name}_columns_count"] = columns.size
          
          # Kiểm tra specific attributes
          required_attrs = case model_name
          when 'TAggregateRankingPoint'
            %w[maker_nm model_nm maker_id model_id ranking_point]
          when 'TAggregateOffer'
            %w[maker_nm model_nm maker_id model_id offer_count]
          when 'TPopularRanking'
            %w[make_id model_id ranking_point]
          else
            []
          end
          
          missing_attrs = required_attrs - columns.keys
          if missing_attrs.any?
            results[:status] = 'unhealthy'
            results[:issues] << "#{model_name} missing attributes: #{missing_attrs.join(', ')}"
          end
          
          results[:details]["#{model_name}_status"] = 'OK'
          
        rescue => e
          results[:status] = 'unhealthy'
          results[:issues] << "#{model_name} check failed: #{e.message}"
          results[:details]["#{model_name}_status"] = 'FAILED'
        end
      end
      
      # 4. Test actual record access
      begin
        record = TAggregateRankingPoint.first
        if record
          # Test access to problematic attributes
          test_value = record.maker_nm
          results[:details][:record_access_test] = 'OK'
        else
          results[:details][:record_access_test] = 'NO_RECORDS'
        end
      rescue => e
        results[:status] = 'unhealthy'
        results[:issues] << "Record access test failed: #{e.message}"
        results[:details][:record_access_test] = 'FAILED'
        
        # Nếu đây là lỗi undefined method maker_nm, đây chính là bug
        if e.message.include?("undefined method `maker_nm'")
          results[:issues] << "CRITICAL: maker_nm undefined method bug detected!"
        end
      end
      
      results
    end
    
    def self.log_health_check
      results = check_health
      
      if results[:status] == 'healthy'
        Rails.logger.info "Schema cache health check: #{results[:status]}"
        Rails.logger.debug "Health check details: #{results[:details]}"
      else
        Rails.logger.error "Schema cache health check: #{results[:status]}"
        Rails.logger.error "Issues found: #{results[:issues].join('; ')}"
        Rails.logger.error "Health check details: #{results[:details]}"
      end
      
      results
    end
    
    def self.auto_fix_if_needed
      results = check_health
      
      if results[:status] == 'unhealthy'
        Rails.logger.warn "Unhealthy schema cache detected, attempting auto-fix..."
        
        begin
          # Attempt to refresh schema cache
          ActiveRecord::Base.connection.schema_cache.clear!
          
          [TAggregateRankingPoint, TAggregateOffer, TPopularRanking].each do |model|
            model.reset_column_information
          end
          
          # Preload critical tables
          %w[t_aggregate_ranking_points t_aggregate_offers t_popular_rankings].each do |table|
            ActiveRecord::Base.connection.columns(table)
          end
          
          Rails.logger.info "Schema cache auto-fix completed"
          
          # Re-check health
          new_results = check_health
          if new_results[:status] == 'healthy'
            Rails.logger.info "Auto-fix successful, schema cache is now healthy"
            return true
          else
            Rails.logger.error "Auto-fix failed, issues persist: #{new_results[:issues].join('; ')}"
            return false
          end
          
        rescue => e
          Rails.logger.error "Auto-fix failed with error: #{e.message}"
          return false
        end
      else
        Rails.logger.debug "Schema cache is healthy, no auto-fix needed"
        return true
      end
    end
    
    # Method để gọi từ monitoring system (như New Relic, DataDog, etc.)
    def self.health_check_endpoint
      results = check_health
      
      {
        status: results[:status] == 'healthy' ? 200 : 500,
        body: results.to_json,
        headers: { 'Content-Type' => 'application/json' }
      }
    end
  end
end
