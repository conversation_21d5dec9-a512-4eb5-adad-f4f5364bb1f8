#!/usr/bin/env ruby

# Script để reproduce bug maker_nm trong production
# Chạy trong Rails console production

puts "=== REPRODUCE MAKER_NM BUG ==="

# Test các service và presenter có thể gây ra lỗi
test_cases = [
  {
    name: "Search::InternalLinksAllMake service",
    code: -> {
      service = Search::InternalLinksAllMake.new(1, 'USD')
      service.send(:popular_model_data)
    }
  },
  {
    name: "Search::InternalLink::MakePriceRange service", 
    code: -> {
      # Cần tìm một make và price range hợp lệ
      make = MasterMake.first
      if make
        service = Search::InternalLink::MakePriceRange.new(make.vc_name_e, 'pr=1500-2000', 1)
        service.send(:handle_popular_model)
      else
        puts "No make found"
      end
    }
  },
  {
    name: "Cars::InternalLinks service",
    code: -> {
      make = MasterMake.first
      model = MasterModel.first
      if make && model
        service = Cars::InternalLinks.new(make, model, 1, 'USD')
        service.send(:popular_model_data)
      else
        puts "No make or model found"
      end
    }
  },
  {
    name: "Direct TAggregateRankingPoint query",
    code: -> {
      records = TAggregateRankingPoint.limit(5)
      records.each do |record|
        puts "ID: #{record.id}, maker_nm: #{record.maker_nm}, model_nm: #{record.model_nm}"
      end
    }
  },
  {
    name: "TAggregateRankingPoint.query_by_make_and_model",
    code: -> {
      couple_maker_model = [[1, 74], [2, 100]]  # Test với một số ID
      results = TAggregateRankingPoint.query_by_make_and_model(couple_maker_model, 5)
      results.each do |result|
        puts "Maker: #{result.maker_nm}, Model: #{result.model_nm}"
      end
    }
  },
  {
    name: "Search::GetPopularModelCarsService",
    code: -> {
      make = MasterMake.first
      model = MasterModel.first
      if make && model
        service = Search::GetPopularModelCarsService.new(make, model, 1, 'USD')
        list = service.list(5)
        if list
          list.each do |item|
            puts "Maker: #{item.maker_nm}, Model: #{item.model_nm}"
          end
        end
      end
    }
  }
]

test_cases.each_with_index do |test_case, index|
  puts "\n#{index + 1}. Testing: #{test_case[:name]}"
  puts "-" * 50
  
  begin
    result = test_case[:code].call
    puts "SUCCESS: #{result.inspect}" if result
  rescue => e
    puts "ERROR: #{e.message}"
    puts "ERROR class: #{e.class}"
    puts "Backtrace:"
    puts e.backtrace.first(5).join("\n")
    
    # Nếu đây là lỗi undefined method maker_nm, đây chính là bug chúng ta cần
    if e.message.include?("undefined method `maker_nm'")
      puts "\n*** FOUND THE BUG! ***"
      puts "This is the exact error we're looking for."
      
      # Thử debug thêm
      if e.message.include?("TAggregateRankingPoint")
        puts "\nDebugging the problematic object:"
        # Extract object info from error message if possible
        if match = e.message.match(/#<TAggregateRankingPoint id: (\d+)/)
          record_id = match[1]
          puts "Problematic record ID: #{record_id}"
          
          begin
            record = TAggregateRankingPoint.find(record_id)
            puts "Record found: #{record.attributes.keys.inspect}"
            puts "Record class: #{record.class}"
            puts "Record respond_to maker_nm: #{record.respond_to?(:maker_nm)}"
          rescue => debug_error
            puts "Error finding record: #{debug_error.message}"
          end
        end
      end
    end
  end
end

# Test template rendering nếu có thể
puts "\n#{test_cases.length + 1}. Testing template rendering"
puts "-" * 50

begin
  # Test render một partial có sử dụng maker_nm
  popular_model = TAggregateRankingPoint.first
  if popular_model
    puts "Testing template with record: #{popular_model.id}"
    # Simulate template access
    puts "maker_nm: #{popular_model.maker_nm}"
    puts "model_nm: #{popular_model.model_nm}"
  end
rescue => e
  puts "ERROR in template test: #{e.message}"
  puts "This might be the source of the ActionView::Template::Error"
end

puts "\n=== REPRODUCE TEST COMPLETE ==="
