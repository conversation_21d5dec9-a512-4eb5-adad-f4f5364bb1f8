name: PR Review with <PERSON>

on:
  issue_comment:
    types: [created]

jobs:
  review:
    runs-on: ubuntu-latest
    if: github.event.issue.pull_request && contains(github.event.comment.body, 'devin-review')
    permissions:
      contents: read
      pull-requests: write
      actions: read
    steps:
      - name: Check review limit
        id: limit-check
        run: |
          PR_NUMBER="${{ github.event.issue.number }}"
          REPO_OWNER="${{ github.repository_owner }}"
          REPO_NAME="${{ github.event.repository.name }}"
          MAX_REVIEWS=2
          
          echo "Checking review limit for PR #$PR_NUMBER..."
          echo "Maximum allowed reviews: $MAX_REVIEWS"
          
          # Get all comments for this PR
          COMMENTS_RESPONSE=$(curl -s \
            -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
            -H "Accept: application/vnd.github.v3+json" \
            "https://api.github.com/repos/$REPO_OWNER/$REPO_NAME/issues/$PR_NUMBER/comments?per_page=100")
          
          if [ $? -ne 0 ]; then
            echo "Failed to fetch PR comments"
            exit 1
          fi
          
          # Count comments that match the same trigger condition (excluding the current comment)
          # Only count comments that contain "devin-review" exactly like the workflow trigger condition
          CURRENT_COMMENT_ID="${{ github.event.comment.id }}"
          COUNT=0
          
          # Parse comments and count those that would trigger this workflow
          # Match the exact same condition: contains(github.event.comment.body, 'devin-review')
          DEVIN_REVIEW_COMMENTS=$(echo "$COMMENTS_RESPONSE" | jq -r --arg current_id "$CURRENT_COMMENT_ID" '
            [.[] | select(.id != ($current_id | tonumber) and (.body | contains("devin-review")))] | length
          ')
          
          if [ $? -eq 0 ] && [ -n "$DEVIN_REVIEW_COMMENTS" ]; then
            COUNT=$DEVIN_REVIEW_COMMENTS
          else
            echo "Failed to parse comments or no devin-review comments found"
            COUNT=0
          fi
          
          echo "Current 'devin-review' comment count for PR #$PR_NUMBER: $COUNT"
          echo "Current comment ID: $CURRENT_COMMENT_ID (excluded from count)"
          
          if [ $COUNT -ge $MAX_REVIEWS ]; then
            echo "❌ Review limit exceeded! This PR has already received $COUNT 'devin-review' requests."
            echo "Maximum allowed reviews: $MAX_REVIEWS"
            echo "should_skip=true" >> $GITHUB_OUTPUT
            echo "skip_reason=limit_exceeded" >> $GITHUB_OUTPUT
          else
            REMAINING=$((MAX_REVIEWS - COUNT))
            echo "✅ Review allowed. Remaining reviews: $REMAINING"
            echo "should_skip=false" >> $GITHUB_OUTPUT
            echo "skip_reason=" >> $GITHUB_OUTPUT
          fi

      - name: Post limit exceeded message
        if: steps.limit-check.outputs.should_skip == 'true'
        run: |
          PR_NUMBER="${{ github.event.issue.number }}"
          REPO_OWNER="${{ github.repository_owner }}"
          REPO_NAME="${{ github.event.repository.name }}"
          
          COMMENT_BODY="🚫 **Devin Review Limit Exceeded**
          
          This PR has already received the maximum number of 'devin-review' requests (2 requests).
          
          To get additional reviews, please:
          - Make significant changes to the code
          - Ask a team member to manually trigger if needed
          
          _This limit helps control API costs. Contact your team lead if you need an exception._"
          
          curl -s -X POST \
            -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
            -H "Accept: application/vnd.github.v3+json" \
            "https://api.github.com/repos/$REPO_OWNER/$REPO_NAME/issues/$PR_NUMBER/comments" \
            -d "{\"body\": $(echo "$COMMENT_BODY" | jq -Rs .)}"
          
          echo "Posted limit exceeded message to PR #$PR_NUMBER"

      - name: Get PR info
        id: pr-info
        if: steps.limit-check.outputs.should_skip != 'true'
        run: |
          # Get PR details from the issue API since we're triggered by comment
          PR_NUMBER="${{ github.event.issue.number }}"
          REPO_NAME="${{ github.repository }}"
          
          # Get the actual PR URL from the pull request API
          PR_URL="https://github.com/$REPO_NAME/pull/$PR_NUMBER"
          
          echo "pr_url=$PR_URL" >> $GITHUB_OUTPUT
          echo "pr_number=$PR_NUMBER" >> $GITHUB_OUTPUT
          echo "repo_name=$REPO_NAME" >> $GITHUB_OUTPUT
          echo "comment_author=${{ github.event.comment.user.login }}" >> $GITHUB_OUTPUT

      - name: Trigger Devin Review
        id: devin-session
        if: steps.limit-check.outputs.should_skip != 'true'
        env:
          DEVIN_API_KEY: ${{ secrets.DEVIN_API_KEY }}
          PROMPT: |
            Review PR ${{ steps.pr-info.outputs.pr_url }} !kuruma_review_pr
            Requested by: ${{ steps.pr-info.outputs.comment_author }}
        run: |
          # Check if jq is available
          if ! command -v jq &> /dev/null; then
            echo "Error: jq is not available"
            exit 1
          fi
          
          # Escape the prompt for JSON with error handling
          if ! ESCAPED_PROMPT=$(echo "$PROMPT" | jq -Rs .); then
            echo "Error: Failed to escape prompt for JSON"
            exit 1
          fi

          # Send request to Devin API with retry logic
          echo "Sending request to Devin API..."
          echo "Triggered by comment from: ${{ steps.pr-info.outputs.comment_author }}"
          
          MAX_RETRIES=3
          RETRY_COUNT=0
          SUCCESS=false
          SESSION_ID=""
          
          while [ $RETRY_COUNT -lt $MAX_RETRIES ] && [ "$SUCCESS" = false ]; do
            RETRY_COUNT=$((RETRY_COUNT + 1))
            echo "Attempt $RETRY_COUNT of $MAX_RETRIES..."
            
            # Calculate exponential backoff delay (1s, 2s, 4s)
            if [ $RETRY_COUNT -gt 1 ]; then
              DELAY=$((2 ** ($RETRY_COUNT - 2)))
              echo "Waiting ${DELAY}s before retry..."
              sleep $DELAY
            fi
            
            HTTP_CODE=$(curl -w "%{http_code}" -s -o response.json -X POST \
              --max-time 30 \
              -H "Authorization: Bearer $DEVIN_API_KEY" \
              -H "Content-Type: application/json" \
              -d '{"prompt": '"$ESCAPED_PROMPT"'}' \
              "https://api.devin.ai/v1/sessions")
            
            CURL_EXIT_CODE=$?
            
            # Check curl exit status
            if [ $CURL_EXIT_CODE -eq 0 ]; then
              # Validate HTTP response code
              if [ "$HTTP_CODE" -lt 400 ]; then
                SUCCESS=true
                echo "Request successful on attempt $RETRY_COUNT"
              else
                echo "HTTP error $HTTP_CODE on attempt $RETRY_COUNT"
                if [ -f response.json ]; then
                  # Parse error message safely without exposing sensitive data
                  ERROR_MSG=$(jq -r '.error.message // .message // "Unknown error"' response.json 2>/dev/null || echo "Failed to parse error")
                  echo "Error message: $ERROR_MSG"
                fi
              fi
            else
              echo "Network error (curl exit code: $CURL_EXIT_CODE) on attempt $RETRY_COUNT"
            fi
            
            # If this was the last attempt and still failed, exit
            if [ $RETRY_COUNT -eq $MAX_RETRIES ] && [ "$SUCCESS" = false ]; then
              echo "Error: Failed to send request to Devin API after $MAX_RETRIES attempts"
              rm -f response.json
              exit 1
            fi
          done
          
          # Parse and validate successful response
          if [ -f response.json ]; then
            if jq -e . response.json > /dev/null 2>&1; then
              SESSION_ID=$(jq -r '.session_id // "unknown"' response.json)
              echo "Devin session started successfully. Session ID: $SESSION_ID"
              echo "Review requested by: ${{ steps.pr-info.outputs.comment_author }}"
              
              # Export session ID for monitoring step
              echo "session_id=$SESSION_ID" >> $GITHUB_OUTPUT
            else
              echo "Error: Invalid JSON response from Devin API"
              rm -f response.json
              exit 1
            fi
            # Clean up response file
            rm -f response.json
          else
            echo "Error: No response received from Devin API"
            exit 1
          fi
