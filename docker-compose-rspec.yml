version: '3.5'
services:
  db:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: webapp_test_db
      MYSQL_ROOT_USER: root
      MYSQL_ROOT_PASSWORD: 123456
    ports:
      - '3306:3306'
    volumes:
      - mysql-data:/run/mysqld
      - './docker/db/conf.d:/etc/mysql/conf.d'

  rspec:
    image: gcr.io/tradecarview-dev/myapp:testing
    depends_on:
      - db
      - redis
    environment:
      REDIS_CACHE_URL: 'redis://redis:6379/0'
      REDIS_SESSION_URL: 'redis://redis:6379/1'
      PRIMARY_DB_PORT: 3306
    command: './run-tests.sh'

  redis:
    image: redis:6.0
    ports:
      - '6379:6379'

volumes:
  mysql-data:
networks:
  default:
    external:
      name: cloudbuild
