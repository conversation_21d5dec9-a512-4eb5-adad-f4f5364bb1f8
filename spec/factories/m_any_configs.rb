FactoryBot.define do
  factory :m_any_config do
    category { Faker::Number.number(digits: 2) }
    sub_category { Faker::Number.number(digits: 2) }
    sequence(:sequence) { |n| n }
    any_data { Faker::Lorem.sentence }
    data_type { Faker::Lorem.word }
    is_valid { [true, false].sample }
    key_name { Faker::Lorem.word }
    remark { Faker::Lorem.sentence }
    create_date { Faker::Time.backward(days: 365) }
    update_date { Faker::Time.backward(days: 365) }
  end
end
