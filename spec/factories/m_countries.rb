FactoryBot.define do
  factory :m_country do
    sequence(:country) { |n| "Country #{n}" }
    sequence(:a2) { |n| "A#{n}" }
    sequence(:a3) { |n| "A#{n}C" }
    sequence(:number)
    sequence(:country_jp) { |n| "Country JP #{n}" }
    sequence(:country_e) { |n| "Country E #{n}" }
    sequence(:ar) { |n| "AR #{n}" }
    sequence(:de) { |n| "DE #{n}" }
    sequence(:en) { |n| "EN #{n}" }
    sequence(:es) { |n| "ES #{n}" }
    sequence(:fr) { |n| "FR #{n}" }
    sequence(:it) { |n| "IT #{n}" }
    sequence(:ja) { |n| "JA #{n}" }
    sequence(:ko) { |n| "KO #{n}" }
    sequence(:pt) { |n| "PT #{n}" }
    sequence(:ro) { |n| "RO #{n}" }
    sequence(:ru) { |n| "RU #{n}" }
    sequence(:zh_cn) { |n| "ZH_CN #{n}" }
    sequence(:country_code) { |n| "CC-#{n}" }
    sequence(:short_country) { |n| "SC-#{n}" }
  end
end
