FactoryBot.define do
  factory :t_trim do
    model_id { Faker::Number.unique.between(from: 1, to: 1000) }
    trim_id { Faker::Number.unique.between(from: 1, to: 1000) }
    pattern_type { Faker::Lorem.characters(number: 40) }
    short_pattern_type { Faker::Lorem.characters(number: 50) }
    is_valid { true }
    created_date { Faker::Date.between(from: '2022-01-01', to: '2022-12-31') }
    updated_date { Faker::Date.between(from: '2022-01-01', to: '2022-12-31') }
    vehicle_length { Faker::Number.between(from: 1, to: 100) }
    vehicle_width { Faker::Number.between(from: 1, to: 100) }
    vehicle_height { Faker::Number.between(from: 1, to: 100) }
  end
end
