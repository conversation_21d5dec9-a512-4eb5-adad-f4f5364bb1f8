FactoryBot.define do
  factory :m_import_regulation do
    country_number { Faker::Number.unique.number(digits: 1) }
    regulations_id { Faker::Number.unique.number(digits: 1) }
    create_date { Faker::Time.backward(days: 365) }
    update_date { Faker::Time.backward(days: 365) }
    is_valid { true }
    is_import_regulations { true }
    model_year_from { Faker::Number.between(from: 2000, to: 2022) }
    model_year_to { Faker::Number.between(from: 2018, to: 2022) }
    is_model_year_from_update { Faker::Boolean.boolean }
    is_model_year_to_update { Faker::Boolean.boolean }
    steering_id { Faker::Number.unique.number(digits: 1) }
  end
end
