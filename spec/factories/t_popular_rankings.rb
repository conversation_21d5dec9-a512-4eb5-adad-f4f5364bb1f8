FactoryBot.define do
  factory :t_popular_ranking do
    country_number { Faker::Number.between(from: 1, to: 100) }
    make_id { Faker::Number.between(from: 1, to: 100) }
    model_id { Faker::Number.between(from: 1, to: 100) }
    ranking_point { Faker::Number.between(from: 1, to: 100) }
    make_brand_id { Faker::Number.between(from: 1, to: 100) }
    model_master_id { Faker::Number.between(from: 1, to: 100) }
    model_year { Faker::Number.between(from: 2020, to: 2022) }
    media_template_path { Faker::File.file_name }
    media_template_prefix { Faker::Lorem.characters(number: 10) }
    media_template_suffix { Faker::Lorem.characters(number: 50) }
    association :master_model
  end
end
