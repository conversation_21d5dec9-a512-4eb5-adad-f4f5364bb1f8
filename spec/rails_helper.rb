# This file is copied to spec/ when you run 'rails generate rspec:install'
require 'spec_helper'
require 'simplecov'

ENV['RAILS_ENV'] = 'test'
require_relative '../config/environment'

unless ENV['IGNORE_COVERAGE'] == 'true'
  SimpleCov.start 'rails' do
    add_filter '/spec/'
  end
end

# Prevent database truncation if the environment is production
abort('The Rails environment is running in production mode!') if Rails.env.production?
require 'rspec/rails'
require 'database_cleaner'
require 'capybara/rspec'

begin
  ActiveRecord::Migration.maintain_test_schema!
rescue ActiveRecord::PendingMigrationError => e
  abort e.to_s.strip
end

RSpec.configure do |config|
  # Remove this line if you're not using ActiveRecord or ActiveRecord fixtures
  config.fixture_path = "#{Rails.root}/spec/fixtures"

  config.use_transactional_fixtures = true
  config.infer_spec_type_from_file_location!
  config.filter_rails_from_backtrace!

  imports_data = [{ model_name: 'MasterMake', file_name: 'WorldCars.dbo.Master_Make.tsv', model_import: 'MasterMake' },
                  { model_name: 'MasterModel', file_name: 'WorldCars.dbo.Master_Model.tsv', model_import: 'MasterModel' },
                  { model_name: 'ExchangeRate', file_name: 'TradeItem.dbo.M_ExchangeRate.tsv', model_import: 'MExchangeRate' },
                  { model_name: 'PrimaryBodyStyle', file_name: 'TradeItem.dbo.M_PrimaryBodyStyle.tsv', model_import: 'MPrimaryBodyStyle' },
                  { model_name: 'PopularRanking', file_name: 'TradeItem.dbo.T_PopularRanking.tsv', model_import: 'TPopularRanking' },
                  { model_name: 'Article', file_name: 'TradeItem.dbo.M_Article.tsv', model_import: 'MArticle' },
                  { model_name: 'ModelImage', file_name: 'CvCarsMaster.dbo.ModelImages.tsv', model_import: 'ModelImage' },
                  { model_name: 'ModelSalesMaster', file_name: 'CvCarsMaster.dbo.ModelSalesMaster.tsv', model_import: 'ModelSalesMaster' },
                  { model_name: 'MCountry', file_name: 'TradeItem.dbo.M_Country.tsv', model_import: 'MCountry' },
                  { model_name: 'SecondaryBodyStyle', file_name: 'TradeItem.dbo.M_SecondaryBodyStyle.tsv', model_import: 'MSecondaryBodyStyle' },
                  { model_name: 'PortListing', file_name: 'TradeItem.dbo.T_PortListing.tsv', model_import: 'TPortListing' },
                  { model_name: 'Port', file_name: 'TradeItem.dbo.M_Port.tsv', model_import: 'MPort' }]
  imports_data.each do |import_data|
    next if import_data[:model_import].constantize.first.present?

    service_import = Object.const_get("Import::MasterData::Models::#{import_data[:model_name]}")
    service_import.new(Settings.import_data.configs.to_h.merge(file_name: import_data[:file_name])).call
  end

  # ignore crawler detection
  config.before(:each) do
    allow_any_instance_of(ActionDispatch::Request).to receive(:is_crawler?).and_return(false)
  end

  config.before(:suite) do
    DatabaseCleaner.clean_with :transaction
    DatabaseCleaner.strategy = :truncation
  end

  config.include FactoryBot::Syntax::Methods
end

Shoulda::Matchers.configure do |config|
  config.integrate do |with|
    # Choose a test framework:
    with.test_framework :rspec

    # Choose one or more libraries:
    with.library :active_record
    with.library :active_model
    with.library :action_controller
    # Or, choose the following (which implies all of the above):
    with.library :rails
  end
end
