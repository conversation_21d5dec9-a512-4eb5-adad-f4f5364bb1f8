require 'rails_helper'

RSpec.describe SchemaCacheRefreshMiddleware do
  let(:app) { double('app') }
  let(:middleware) { described_class.new(app) }
  let(:env) { {} }

  describe '#call' do
    context 'when no error occurs' do
      it 'passes through the request normally' do
        response = [200, {}, ['OK']]
        expect(app).to receive(:call).with(env).and_return(response)
        
        expect(middleware.call(env)).to eq(response)
      end
    end

    context 'when a schema-related error occurs' do
      let(:schema_error) do
        NoMethodError.new("undefined method `maker_nm' for #<TAggregateRankingPoint id: 1000074>")
      end

      before do
        allow(app).to receive(:call).with(env).and_raise(schema_error)
        allow(Rails.logger).to receive(:warn)
        allow(Rails.logger).to receive(:info)
        allow(Rails.logger).to receive(:error)
      end

      it 'detects schema-related errors' do
        expect(middleware.send(:schema_related_error?, schema_error)).to be true
      end

      it 'logs the error' do
        expect(Rails.logger).to receive(:warn).with(/Schema-related error detected/)
        
        expect { middleware.call(env) }.to raise_error(schema_error)
      end

      context 'when error count reaches threshold' do
        before do
          # Simulate multiple errors to reach threshold
          3.times do
            begin
              middleware.call(env)
            rescue
              # Ignore errors, just increment counter
            end
          end
        end

        it 'refreshes schema cache and retries' do
          # Mock schema cache refresh
          schema_cache = double('schema_cache')
          allow(ActiveRecord::Base.connection).to receive(:schema_cache).and_return(schema_cache)
          allow(schema_cache).to receive(:clear!)
          
          # Mock model reset
          allow(TAggregateRankingPoint).to receive(:reset_column_information)
          allow(TAggregateOffer).to receive(:reset_column_information)
          allow(TPopularRanking).to receive(:reset_column_information)
          
          # Mock columns preload
          allow(ActiveRecord::Base.connection).to receive(:columns)
          
          # Mock successful retry
          success_response = [200, {}, ['OK']]
          allow(app).to receive(:call).with(env).and_return(success_response)
          
          expect(Rails.logger).to receive(:info).with(/Refreshing schema cache/)
          expect(Rails.logger).to receive(:info).with(/Schema cache refresh completed/)
          expect(Rails.logger).to receive(:info).with(/Retrying request/)
          
          expect(middleware.call(env)).to eq(success_response)
        end
      end
    end

    context 'when a non-schema-related error occurs' do
      let(:other_error) { StandardError.new("Some other error") }

      before do
        allow(app).to receive(:call).with(env).and_raise(other_error)
      end

      it 'does not detect as schema-related error' do
        expect(middleware.send(:schema_related_error?, other_error)).to be false
      end

      it 'does not refresh schema cache' do
        expect(ActiveRecord::Base.connection).not_to receive(:schema_cache)
        
        expect { middleware.call(env) }.to raise_error(other_error)
      end
    end
  end

  describe '#schema_related_error?' do
    it 'detects undefined method errors for database attributes' do
      error = NoMethodError.new("undefined method `maker_nm' for #<TAggregateRankingPoint>")
      expect(middleware.send(:schema_related_error?, error)).to be true
    end

    it 'detects unknown attribute errors' do
      error = ActiveRecord::UnknownAttributeError.new("unknown attribute 'maker_nm'")
      expect(middleware.send(:schema_related_error?, error)).to be true
    end

    it 'detects column not found errors' do
      error = ActiveRecord::StatementInvalid.new("column 'maker_nm' not found")
      expect(middleware.send(:schema_related_error?, error)).to be true
    end

    it 'does not detect unrelated errors' do
      error = StandardError.new("some random error")
      expect(middleware.send(:schema_related_error?, error)).to be false
    end

    it 'does not detect undefined method errors for non-database attributes' do
      error = NoMethodError.new("undefined method `some_random_method'")
      expect(middleware.send(:schema_related_error?, error)).to be false
    end
  end

  describe '#should_refresh_schema_cache?' do
    it 'returns true when error count reaches threshold' do
      # Simulate errors to reach threshold
      middleware.instance_variable_set(:@error_count, 3)
      expect(middleware.send(:should_refresh_schema_cache?)).to be true
    end

    it 'returns true when refresh interval has passed' do
      # Set last refresh to more than 1 hour ago
      middleware.instance_variable_set(:@last_refresh, 2.hours.ago)
      expect(middleware.send(:should_refresh_schema_cache?)).to be true
    end

    it 'returns false when conditions are not met' do
      # Reset to initial state
      middleware.instance_variable_set(:@error_count, 0)
      middleware.instance_variable_set(:@last_refresh, Time.current)
      expect(middleware.send(:should_refresh_schema_cache?)).to be false
    end
  end

  describe '#refresh_schema_cache' do
    let(:schema_cache) { double('schema_cache') }

    before do
      allow(ActiveRecord::Base.connection).to receive(:schema_cache).and_return(schema_cache)
      allow(schema_cache).to receive(:clear!)
      allow(ActiveRecord::Base.connection).to receive(:columns)
      allow(Rails.logger).to receive(:info)
      allow(Rails.logger).to receive(:debug)
    end

    it 'clears schema cache' do
      expect(schema_cache).to receive(:clear!)
      middleware.send(:refresh_schema_cache)
    end

    it 'resets column information for affected models' do
      expect(TAggregateRankingPoint).to receive(:reset_column_information)
      expect(TAggregateOffer).to receive(:reset_column_information)
      expect(TPopularRanking).to receive(:reset_column_information)
      
      middleware.send(:refresh_schema_cache)
    end

    it 'preloads schema for important tables' do
      expect(ActiveRecord::Base.connection).to receive(:columns).with('t_aggregate_ranking_points')
      expect(ActiveRecord::Base.connection).to receive(:columns).with('t_aggregate_offers')
      expect(ActiveRecord::Base.connection).to receive(:columns).with('t_popular_rankings')
      
      middleware.send(:refresh_schema_cache)
    end

    it 'logs the refresh process' do
      expect(Rails.logger).to receive(:info).with(/Refreshing schema cache/)
      expect(Rails.logger).to receive(:info).with(/Schema cache refresh completed/)
      
      middleware.send(:refresh_schema_cache)
    end

    it 'handles errors gracefully' do
      allow(schema_cache).to receive(:clear!).and_raise(StandardError.new("Cache error"))
      expect(Rails.logger).to receive(:error).with(/Failed to refresh schema cache/)
      
      expect { middleware.send(:refresh_schema_cache) }.not_to raise_error
    end
  end
end
