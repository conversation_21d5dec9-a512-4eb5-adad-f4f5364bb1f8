require 'rails_helper'
require 'rake'

RSpec.describe 'import_master_data' do
  let(:task) { Rake::Task['import_master_data:carsensor'] }

  before(:all) do
    Rails.application.load_tasks
  end

  describe 'import_master_data:carsensor task' do
    context 'incorrect case' do
      it 'aborts, choose incorrect input type, only y/n' do
        allow($stdin).to receive(:gets).and_return("z\n")
        expect { task.execute }.to raise_error(SystemExit)
      end

      it 'aborts, enter incorrect group name' do
        allow($stdin).to receive(:gets).and_return("n\n", "incorrect_name\n")
        expect { task.execute }.to raise_error(SystemExit)
      end
    end

    context 'correct case' do
      it 'imports the every_20m groups' do
        allow($stdin).to receive(:gets).and_return("n\n", "every_20m\n")
        expect { task.execute }.not_to raise_error
      end

      # not run on CI/CD
      # it 'imports all group' do
      #   allow($stdin).to receive(:gets).and_return("y\n")
      #   expect { task.execute }.not_to raise_error
      # end
    end
  end
end
