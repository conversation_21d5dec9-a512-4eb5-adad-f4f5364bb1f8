require 'rails_helper'

RSpec.describe Metas::Search, type: :controller do
  controller(ApplicationController) do
    include Metas::Search

    def index
      # Mock method for testing
    end

    private

    def tdk_search_object
      @tdk_search_object ||= begin
        search_service = Search::SearchConditionFilter.new(params, 0)
        Tdk::Searcher.new(search_service, 0)
      end
    end

    def car_count
      @car_count || 150
    end

    def page_num
      @page_num || '(Page 2)'
    end
  end

  describe '#generate_combination_meta' do
    context 'when combination meta is applicable' do
      let(:params) do
        {
          'controller' => 'search',
          'action' => 'index',
          'bsty' => '1',
          'prcf' => '500',
          'prct' => '1000'
        }
      end

      before do
        allow(controller).to receive(:params).and_return(params)
        controller.instance_variable_set(:@car_count, 150)
        controller.instance_variable_set(:@page_num, '(Page 2)')
      end

      it 'returns meta info from MetaGenerator' do
        result = controller.send(:generate_combination_meta)

        expect(result).to be_present
        expect(result).to include(:title, :description, :h1)
        expect(result[:title]).to include('Bus')
        expect(result[:title]).to include('US$500-US$1,000')
        expect(result[:title]).to include('(Page 2)')
      end

      it 'caches the result' do
        expect(Tdk::MetaGenerator).to receive(:new).once.and_call_original

        # Call twice to test caching
        controller.send(:generate_combination_meta)
        controller.send(:generate_combination_meta)
      end
    end

    context 'when no combination is applicable' do
      let(:params) do
        {
          'controller' => 'search',
          'action' => 'index',
          'make' => 'toyota',
          'model' => 'yaris'
        }
      end

      before do
        allow(controller).to receive(:params).and_return(params)
        controller.instance_variable_set(:@car_count, 150)
        controller.instance_variable_set(:@page_num, '(Page 2)')
      end

      it 'returns nil' do
        result = controller.send(:generate_combination_meta)
        expect(result).to be_nil
      end
    end
  end

  describe '#immutable_title?' do
    context 'when generate_combination_meta is present' do
      before do
        allow(controller).to receive(:generate_combination_meta).and_return(
          {
            title: 'Some title',
            description: 'Some description',
            h1: 'Some h1'
          },
        )
        allow(controller).to receive_message_chain(:tdk_search_object, :condition_size).and_return(2)
      end

      it 'returns true' do
        expect(controller.send(:immutable_title?)).to be true
      end
    end

    context 'when generate_combination_meta is nil and condition_size is zero' do
      before do
        allow(controller).to receive(:generate_combination_meta).and_return(nil)
        allow(controller).to receive_message_chain(:tdk_search_object, :condition_size).and_return(0)
      end

      it 'returns true' do
        expect(controller.send(:immutable_title?)).to be true
      end
    end

    context 'when generate_combination_meta is nil and condition_size is not zero' do
      before do
        allow(controller).to receive(:generate_combination_meta).and_return(nil)
        allow(controller).to receive_message_chain(:tdk_search_object, :condition_size).and_return(2)
      end

      it 'returns false' do
        expect(controller.send(:immutable_title?)).to be false
      end
    end
  end
end
