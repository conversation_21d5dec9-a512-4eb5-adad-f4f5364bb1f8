require 'rails_helper'

RSpec.describe UThankyouOffersCondition, type: :model do
  it 'validates presence of attributes' do
    should validate_presence_of(:max_count_pc)
    should validate_presence_of(:max_count_sp)
    should validate_presence_of(:priority)
    should validate_presence_of(:start_date)
    should validate_presence_of(:update_date)
    should validate_presence_of(:create_date)
  end

  it 'validates numericality of attributes' do
    should validate_numericality_of(:max_count_pc).only_integer
    should validate_numericality_of(:max_count_sp).only_integer
    should validate_numericality_of(:priority).only_integer
  end

  it 'validates inclusion of is_valid and is_check_on attributes' do
    should validate_exclusion_of(:is_valid).in_array([nil])
    should validate_exclusion_of(:is_check_on).in_array([nil])
  end
end
