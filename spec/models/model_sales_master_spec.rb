require 'rails_helper'

RSpec.describe ModelSalesMaster, type: :model do
  describe 'associations' do
    it { should belong_to(:model_master).with_foreign_key(:model_master_id) }
  end

  describe 'validations' do
    it { should validate_presence_of(:model_master_id) }
    it { should validate_numericality_of(:model_master_id).only_integer }
    it { should validate_presence_of(:country_id) }
    it { should validate_numericality_of(:country_id).only_integer }
    it { should validate_numericality_of(:year).only_integer.allow_nil }
    it { should validate_numericality_of(:month).only_integer.allow_nil }
    it { should validate_numericality_of(:model_change_type_id).only_integer.allow_nil }
  end
end
