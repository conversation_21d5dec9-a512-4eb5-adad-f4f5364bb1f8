require 'rails_helper'

RSpec.describe MServicePlan, type: :model do
  describe 'Validations' do
    it { should validate_presence_of(:id_service) }
    it { should validate_presence_of(:count) }
    it { should validate_presence_of(:price) }
    it { should validate_exclusion_of(:is_display).in_array([nil]) }
    it { should validate_numericality_of(:price).is_greater_than_or_equal_to(0) }
    it { should validate_numericality_of(:price_jpy).is_greater_than_or_equal_to(0) }
  end
end
