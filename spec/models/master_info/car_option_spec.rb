require 'rails_helper'

RSpec.describe MasterInfo::CarOption do
  describe '.car_options_by_ids' do
    it 'returns car option by ids' do
      car_options = MasterInfo::CarOption.car_options_by_ids([1, 2, 3, 4])
      expect(car_options.map(&:name)).to eq(['Anti-Lock Brakes', 'Driver Airbag', 'Passenger Airbag', 'Side Airbag'])
    end
  end

  describe 'check data' do
    data = [{ id: 1, name: 'Anti-Lock Brakes' },
            { id: 2, name: 'Driver Airbag' },
            { id: 3, name: 'Passenger Airbag' },
            { id: 4, name: 'Side Airbag' },
            { id: 5, name: 'Alarm' },
            { id: 6, name: 'Power Windows' },
            { id: 7, name: 'Rear Window Defroster' },
            { id: 8, name: 'Rear Window Wiper' },
            { id: 9, name: 'Tinted Glass' },
            { id: 10, name: 'A/C:front' },
            { id: 11, name: 'A/C:rear' },
            { id: 12, name: 'Cruise Control' },
            { id: 13, name: 'Navigation System' },
            { id: 14, name: 'Power Steering' },
            { id: 15, name: 'Remote Keyless Entry' },
            { id: 16, name: 'Tilt Wheel  Tilt Wheel' },
            { id: 17, name: 'Digital Meter' },
            { id: 18, name: 'AM/FM Radio' },
            { id: 19, name: 'AM/FM Stereo' },
            { id: 20, name: 'CD Changer' },
            { id: 21, name: 'CD Player' },
            { id: 22, name: 'Premium Sound' },
            { id: 23, name: 'Satellite Radio' },
            { id: 24, name: 'DVD' },
            { id: 25, name: 'Hard Disc' },
            { id: 26, name: 'Child Seat' },
            { id: 27, name: 'Leather Seats' },
            { id: 28, name: 'Power Seats' },
            { id: 29, name: 'Bucket Seat' },
            { id: 30, name: 'Alloy Wheels' },
            { id: 31, name: 'Power Door Locks' },
            { id: 32, name: 'Power Mirrors' },
            { id: 33, name: 'Sunroof', query_value: '33' },
            { id: 34, name: 'Third Row Seats' },
            { id: 35, name: 'Power Slide Door' },
            { id: 36, name: 'Custom Wheels' },
            { id: 37, name: 'Fully Loaded' },
            { id: 38, name: 'Maintenance Records Available' },
            { id: 39, name: 'Repainted Car' },
            { id: 40, name: 'New Tires' },
            { id: 41, name: 'No Accident History' },
            { id: 42, name: 'One Owner' },
            { id: 43, name: 'Performance Tires' },
            { id: 44, name: 'Upgraded Sound System' },
            { id: 45, name: 'Non-Smoker' },
            { id: 70, name: 'Turbo' }]

    data.each do |car_option|
      it "returns data for #{car_option[:id]}" do
        expect(described_class.find(car_option[:id]).attributes).to eq(car_option)
      end
    end
  end
end
