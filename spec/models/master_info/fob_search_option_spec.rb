require 'rails_helper'

RSpec.describe MasterInfo::FobSearchOption do
  search_options = [{ name: 'US$500', value: 500 },
                    { name: 'US$1,000', value: 1000 },
                    { name: 'US$1,500', value: 1500 },
                    { name: 'US$2,000', value: 2000 },
                    { name: 'US$2,500', value: 2500 },
                    { name: 'US$5,000', value: 5000 },
                    { name: 'US$10,000', value: 10_000 },
                    { name: 'US$20,000', value: 20_000 }]

  describe 'check data' do
    search_options.each do |option|
      it "returns value for #{option[:name]}" do
        expect(described_class.find_by(name: option[:name]).attributes.except(:id)).to eq(option)
      end
    end
  end
end
