require 'rails_helper'

RSpec.describe MasterInfo::MileageSearchOption, type: :model do
  it 'should have five mileage options' do
    expect(described_class.count).to eq(5)
  end

  describe 'option 50,000km ' do
    it 'should have correct name' do
      option = described_class.find_by(value: 50_000)

      expect(option[:name]).to eq('50,000km')
    end
  end

  describe 'option 80,000km ' do
    it 'should have correct name' do
      option = described_class.find_by(value: 80_000)

      expect(option[:name]).to eq('80,000km')
    end
  end

  describe 'option 100,000km ' do
    it 'should have correct name' do
      option = described_class.find_by(value: 100_000)

      expect(option[:name]).to eq('100,000km')
    end
  end

  describe 'option 150,000km ' do
    it 'should have correct name' do
      option = described_class.find_by(value: 150_000)

      expect(option[:name]).to eq('150,000km')
    end
  end

  describe 'option 200,000km ' do
    it 'should have correct name' do
      option = described_class.find_by(value: 200_000)

      expect(option[:name]).to eq('200,000km')
    end
  end
end
