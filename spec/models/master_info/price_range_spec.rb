require 'rails_helper'

RSpec.describe MasterInfo::PriceRange, type: :model do
  let(:fields) { %i[from to] }

  it 'should have eight price ranges' do
    expect(described_class.count).to eq(8)
  end

  describe 'Price range 1' do
    it 'should have correct from & to' do
      range = described_class.fetch_price_range(1)

      expect(range_data(range)).to eq([nil, 500])
    end
  end

  describe 'Price range 2' do
    it 'should have correct from & to' do
      range = described_class.fetch_price_range(2)

      expect(range_data(range)).to eq([500, 1000])
    end
  end

  describe 'Price range 3' do
    it 'should have correct from & to' do
      range = described_class.fetch_price_range(3)

      expect(range_data(range)).to eq([1000, 1500])
    end
  end

  describe 'Price range 4' do
    it 'should have correct from & to' do
      range = described_class.fetch_price_range(4)

      expect(range_data(range)).to eq([1500, 2000])
    end
  end

  describe 'Price range 5' do
    it 'should have correct from & to' do
      range = described_class.fetch_price_range(5)

      expect(range_data(range)).to eq([2000, 2500])
    end
  end

  describe 'Price range 6' do
    it 'should have correct from & to' do
      range = described_class.fetch_price_range(6)

      expect(range_data(range)).to eq([2500, 5000])
    end
  end

  describe 'Price range 7' do
    it 'should have correct from & to' do
      range = described_class.fetch_price_range(7)

      expect(range_data(range)).to eq([5000, nil])
    end
  end

  describe 'Price range 8' do
    it 'should have correct from & to' do
      range = described_class.fetch_price_range(8)

      expect(range_data(range)).to eq([0, 99_999_999])
    end
  end

  describe 'Price range with rescue' do
    it 'handles the exception' do
      allow(described_class).to receive(:reload).and_raise(StandardError)
      range = described_class.fetch_price_range(1)
      expect(range_data(range)).to eq([nil, 500])
    end
  end

  private

  def range_data(range)
    fields.map { |field| range[field] }
  end
end
