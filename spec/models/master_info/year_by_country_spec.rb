require 'rails_helper'

RSpec.describe MasterInfo::YearByCountry, type: :model do
  it 'should have 17 country-year mappings' do
    expect(described_class.count).to eq(17)
  end

  describe 'country 50' do
    it 'should have year 5' do
      year = described_class.find_by(country_code: 50)[:year]

      expect(year).to eq(5)
    end
  end

  describe 'country 44' do
    it 'should have year 10' do
      year = described_class.find_by(country_code: 44)[:year]

      expect(year).to eq(10)
    end
  end

  describe 'country 388' do
    it 'should have year 5' do
      year = described_class.find_by(country_code: 388)[:year]

      expect(year).to eq(5)
    end
  end

  describe 'country 144' do
    it 'should have year 2' do
      year = described_class.find_by(country_code: 144)[:year]

      expect(year).to eq(2)
    end
  end

  describe 'country 480' do
    it 'should have year 4' do
      year = described_class.find_by(country_code: 480)[:year]

      expect(year).to eq(4)
    end
  end

  describe 'country 566' do
    it 'should have year 8' do
      year = described_class.find_by(country_code: 566)[:year]

      expect(year).to eq(8)
    end
  end

  describe 'country 591' do
    it 'should have year 4' do
      year = described_class.find_by(country_code: 591)[:year]

      expect(year).to eq(4)
    end
  end

  describe 'country 604' do
    it 'should have year 5' do
      year = described_class.find_by(country_code: 604)[:year]

      expect(year).to eq(5)
    end
  end

  describe 'country 598' do
    it 'should have year 5' do
      year = described_class.find_by(country_code: 598)[:year]

      expect(year).to eq(5)
    end
  end

  describe 'country 586' do
    it 'should have year 3' do
      year = described_class.find_by(country_code: 586)[:year]

      expect(year).to eq(3)
    end
  end

  describe 'country 702' do
    it 'should have year 3' do
      year = described_class.find_by(country_code: 702)[:year]

      expect(year).to eq(3)
    end
  end

  describe 'country 740' do
    it 'should have year 8' do
      year = described_class.find_by(country_code: 740)[:year]

      expect(year).to eq(8)
    end
  end

  describe 'country 800' do
    it 'should have year 15' do
      year = described_class.find_by(country_code: 800)[:year]

      expect(year).to eq(15)
    end
  end

  describe 'country 704' do
    it 'should have year 5' do
      year = described_class.find_by(country_code: 704)[:year]

      expect(year).to eq(5)
    end
  end

  describe 'country 882' do
    it 'should have year 8' do
      year = described_class.find_by(country_code: 882)[:year]

      expect(year).to eq(8)
    end
  end

  describe 'country 124' do
    it 'should have year 15' do
      year = described_class.find_by(country_code: 124)[:year]

      expect(year).to eq(15)
    end
  end

  describe 'country 840' do
    it 'should have year 25' do
      year = described_class.find_by(country_code: 840)[:year]

      expect(year).to eq(25)
    end
  end
end
