require 'rails_helper'

RSpec.describe MasterInfo::SortCondition, type: :model do
  let(:fields) { %i[name group] }

  it 'should have 11 sorting options' do
    expect(described_class.count).to eq(11)
  end

  describe 'sorting condition with value 62' do
    it 'should have correct name and group' do
      condition = described_class.find_by(value: 62)

      expect(sort_condition_data(condition)).to eq(%w[Featured Featured])
    end
  end

  describe 'sorting condition with value 1' do
    it 'should have correct name and group' do
      condition = described_class.find_by(value: 1)

      expect(sort_condition_data(condition)).to eq(['Low to High', 'FOB Price'])
    end
  end

  describe 'sorting condition with value 2' do
    it 'should have correct name and group' do
      condition = described_class.find_by(value: 2)

      expect(sort_condition_data(condition)).to eq(['High to Low', 'FOB Price'])
    end
  end

  describe 'sorting condition with value 60' do
    it 'should have correct name and group' do
      condition = described_class.find_by(value: 60)

      expect(sort_condition_data(condition)).to eq(['Low to High', 'Total Price'])
    end
  end

  describe 'sorting condition with value 61' do
    it 'should have correct name and group' do
      condition = described_class.find_by(value: 61)

      expect(sort_condition_data(condition)).to eq(['High to Low', 'Total Price'])
    end
  end

  describe 'sorting condition with value 8' do
    it 'should have correct name and group' do
      condition = described_class.find_by(value: 8)

      expect(sort_condition_data(condition)).to eq(['New to Old', 'Year'])
    end
  end

  describe 'sorting condition with value 7' do
    it 'should have correct name and group' do
      condition = described_class.find_by(value: 7)

      expect(sort_condition_data(condition)).to eq(['Old to New', 'Year'])
    end
  end

  describe 'sorting condition with value 22' do
    it 'should have correct name and group' do
      condition = described_class.find_by(value: 22)

      expect(sort_condition_data(condition)).to eq(['New to Old', 'Update'])
    end
  end

  describe 'sorting condition with value 21' do
    it 'should have correct name and group' do
      condition = described_class.find_by(value: 21)

      expect(sort_condition_data(condition)).to eq(['Old to New', 'Update'])
    end
  end

  describe 'sorting condition with value 36' do
    it 'should have correct name and group' do
      condition = described_class.find_by(value: 36)

      expect(sort_condition_data(condition)).to eq(['Low to High', 'Mileage'])
    end
  end

  describe 'sorting condition with value 37' do
    it 'should have correct name and group' do
      condition = described_class.find_by(value: 37)

      expect(sort_condition_data(condition)).to eq(['High to Low', 'Mileage'])
    end
  end

  private

  def sort_condition_data(condition)
    fields.map { |field| condition[field] }
  end
end
