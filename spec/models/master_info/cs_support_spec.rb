require 'rails_helper'

RSpec.describe MasterInfo::CsSupport do
  describe '.pc_url' do
    it 'returns pc url' do
      cs_support_pc_urls = MasterInfo::CsSupport.all.map(&:pc_url)
      pc_url = proc { |path| "/customersupportcenter/#{path}/" }
      expect(cs_support_pc_urls).not_to be_nil
      expect(cs_support_pc_urls).to eq(%w[kenya mozambique tanzania zambia uganda].map(&pc_url))
    end
  end

  describe 'check data' do
    cs_supports = [{ name: 'Kenya', path: 'kenya/', number: 404 }, { name: 'Mozambique', path: 'mozambique/', number: 508 },
                   { name: 'Tanzania', path: 'tanzania/', number: 834 }, { name: 'Zambia', path: 'zambia/', number: 894 },
                   { name: 'Uganda', path: 'uganda/', number: 800 }]

    cs_supports.each do |cs|
      it "returns value for #{cs[:name]}" do
        expect(described_class.find_by(name: cs[:name]).attributes.except(:id)).to eq(cs)
      end
    end
  end
end
