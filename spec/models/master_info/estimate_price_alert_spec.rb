require 'rails_helper'
PATTERN_SHOW_ASTERISK = [1, 2].freeze

RSpec.describe MasterInfo::EstimatePriceAlert do
  describe '.show_asterisk' do
    it 'returns pc url' do
      alert_show_asterisk = MasterInfo::EstimatePriceAlert.where(pattern: PATTERN_SHOW_ASTERISK).map(&:show_asterisk)
      expect(alert_show_asterisk.all?(true)).to eq(true)
    end
  end

  describe 'check data' do
    estimate_prices = [{ country_number: 242, name: 'Fiji', kind: 'Registration', year: 5, pattern: 1 },
                       { country_number: 404, name: 'Kenya', kind: 'Manufacture', year: 7, pattern: 1 },
                       { country_number: 598, name: 'Papua New Guinea', kind: nil, year: nil, pattern: 2 },
                       { country_number: 662, name: 'Saint Lucia', kind: nil, year: nil, pattern: 2 },
                       { country_number: 716, name: 'Zimbabwe', kind: nil, year: nil, pattern: 2 },
                       { country_number: 834, name: 'Tanzania', kind: nil, year: nil, pattern: 2 },
                       { country_number: 28, name: 'Antigua and Barbuda', kind: 'Manufacture', year: 5, pattern: 3 },
                       { country_number: 50, name: 'Bangladesh', kind: 'Registration', year: 5, pattern: 3 },
                       { country_number: 52, name: 'Barbados', kind: 'Registration', year: 4, pattern: 3 },
                       { country_number: 144, name: 'Sri Lanka', kind: 'Registration', year: 3, pattern: 3 },
                       { country_number: 196, name: 'Cyprus', kind: 'Registration', year: 3, pattern: 3 },
                       { country_number: 308, name: 'Grenada', kind: 'Manufacture', year: 10, pattern: 3 },
                       { country_number: 462, name: 'Maldives', kind: 'Registration', year: 5, pattern: 3 },
                       { country_number: 643, name: 'Russian Federation', kind: 'Registration', year: 5, pattern: 3 },
                       { country_number: 670, name: 'Saint Vincent and the Grenadines', kind: 'Manufacture', year: 12, pattern: 3 },
                       { country_number: 740, name: 'Suriname', kind: 'Manufacture', year: 8, pattern: 3 },
                       { country_number: 748, name: 'Swaziland', kind: 'Manufacture', year: 15, pattern: 3 },
                       { country_number: 800, name: 'Uganda', kind: 'Manufacture', year: 15, pattern: 3 },
                       { country_number: 124, name: 'Canada', kind: 'Manufacture', year: 15, pattern: 4 },
                       { country_number: 840, name: 'United States', kind: 'Manufacture', year: 25, pattern: 4 },
                       { country_number: 388, name: 'Jamaica', kind: 'Manufacture', year: nil, before_year: 2015, pattern: 5 }]

    estimate_prices.each do |estimate|
      it "returns value for #{estimate[:country_number]}" do
        expect(described_class.find_by(country_number: estimate[:country_number]).attributes.except(:id)).to eq(estimate)
      end
    end
  end
end
