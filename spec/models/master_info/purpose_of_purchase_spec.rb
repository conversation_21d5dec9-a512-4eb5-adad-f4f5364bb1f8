require 'rails_helper'

RSpec.describe MasterInfo::PurposeOfPurchase, type: :model do
  it 'should have three purpose options' do
    expect(described_class.count).to eq(3)
  end

  describe 'purpose with value 0' do
    it 'should have correct text' do
      purpose = described_class.find_by(value: 0)

      expect(purpose[:text]).to eq('Please Select')
    end
  end

  describe 'purpose with value 3' do
    it 'should have correct text' do
      purpose = described_class.find_by(value: 3)

      expect(purpose[:text]).to eq('For Personal Use')
    end
  end

  describe 'purpose with value 1' do
    it 'should have correct text' do
      purpose = described_class.find_by(value: 1)

      expect(purpose[:text]).to eq('For Customers')
    end
  end
end
