require 'rails_helper'

RSpec.describe MasterInfo::InternalLinkCondition, type: :model do
  let(:fields) { %i[text query] }

  it 'should have 12 conditions' do
    expect(described_class.count).to eq(9)
  end

  describe 'DriveTypeID:10' do
    it 'should have correct text, query' do
      condition = described_class.find_by(field: 'DriveTypeID', value: 10)

      expect(condition_data(condition)).to eq(['4WD', 'dr=10'])
    end
  end

  describe 'SteeringID:13' do
    it 'should have correct text, query' do
      condition = described_class.find_by(field: 'SteeringID', value: 13)

      expect(condition_data(condition)).to eq(['LeftHandDrive', 'st=13'])
    end
  end

  describe 'TransmissionID:6' do
    it 'should have correct text, query' do
      condition = described_class.find_by(field: 'TransmissionID', value: 6)

      expect(condition_data(condition)).to eq(['Manual', 'tmns=6'])
    end
  end

  describe 'FuelTypeID:17' do
    it 'should have correct text, query' do
      condition = described_class.find_by(field: 'FuelTypeID', value: 17)

      expect(condition_data(condition)).to eq(['Diesel', 'fues=17'])
    end
  end

  describe 'No Accidents' do
    it 'should have correct text, query' do
      condition = described_class.find_by(field: 'IsAccident', value: 0)

      expect(condition_data(condition)).to eq(['No Accidents', 'ac=2'])
      expect(condition.additional_condition).to eq(' AND IsNoAccidentsHistory:True')
    end
  end

  describe 'Not Repaired' do
    it 'should have correct text, query' do
      condition = described_class.find_by(field: 'IsAccident', value: 1)

      expect(condition_data(condition)).to eq(['Not Repaired', 'ac=1'])
    end
  end

  describe 'FuelTypeID:20' do
    it 'should have correct text, query' do
      condition = described_class.find_by(field: 'FuelTypeID', value: 20)

      expect(condition_data(condition)).to eq(['Gasoline/Petrol', 'fues=20'])
    end
  end

  describe 'FuelTypeID:21' do
    it 'should have correct text, query' do
      condition = described_class.find_by(field: 'FuelTypeID', value: 21)

      expect(condition_data(condition)).to eq(['Hybrid', 'fues=21'])
    end
  end

  describe 'Sunroof' do
    it 'should have correct text, query' do
      condition = described_class.find_by(field: 'Options', value: 33)

      expect(condition_data(condition)).to eq(['Sunroof', 'op=33'])
    end
  end

  private

  def condition_data(condition)
    fields.map { |field| condition[field] }
  end
end
