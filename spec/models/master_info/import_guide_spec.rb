require 'rails_helper'

RSpec.describe MasterInfo::ImportGuide, type: :model do
  describe 'data structure' do
    let(:import_guide_data) { described_class.data }

    it 'loads data from YAML file' do
      expect(import_guide_data).to be_a(Hash)
    end

    it 'has benefits_content section' do
      expect(import_guide_data[:benefits_content]).to be_present
      expect(import_guide_data[:benefits_content]).to be_an(Array)
    end

    it 'has imports_content section' do
      expect(import_guide_data[:imports_content]).to be_present
      expect(import_guide_data[:imports_content]).to be_an(Array)
    end

    it 'has documents_content section' do
      expect(import_guide_data[:documents_content]).to be_present
      expect(import_guide_data[:documents_content]).to be_an(Array)
    end

    it 'has strategies_content section' do
      expect(import_guide_data[:strategies_content]).to be_present
      expect(import_guide_data[:strategies_content]).to be_an(Array)
    end

    it 'has checklist section' do
      expect(import_guide_data[:checklist]).to be_present
      expect(import_guide_data[:checklist]).to be_an(Array)
    end

    it 'has cost_payment section' do
      expect(import_guide_data[:cost_payment]).to be_present
      expect(import_guide_data[:cost_payment]).to be_an(Array)
    end

    it 'has logistics section' do
      expect(import_guide_data[:logistics]).to be_present
      expect(import_guide_data[:logistics]).to be_an(Array)
    end
  end

  describe 'benefits_content structure' do
    let(:benefits_content) { described_class.data[:benefits_content] }

    it 'contains expected number of benefits' do
      expect(benefits_content.length).to eq(3)
    end

    it 'has proper structure for each benefit' do
      benefits_content.each do |benefit|
        expect(benefit).to have_key(:title)
        expect(benefit).to have_key(:content)
        expect(benefit[:title]).to be_a(String)
        expect(benefit[:content]).to be_a(String)
      end
    end

    it 'includes extensive inventory benefit' do
      extensive_inventory = benefits_content.find { |b| b[:title].include?('Extensive Inventory') }
      expect(extensive_inventory).to be_present
      expect(extensive_inventory[:content]).to include('TCV offers one of the largest selections')
    end

    it 'includes safe transactions benefit' do
      safe_transactions = benefits_content.find { |b| b[:title].include?('Safe and Secure Transactions') }
      expect(safe_transactions).to be_present
      expect(safe_transactions[:content]).to include('All purchases are processed through the TCV system')
    end

    it 'includes flexible payment benefit' do
      flexible_payment = benefits_content.find { |b| b[:title].include?('Flexible Payment Options') }
      expect(flexible_payment).to be_present
      expect(flexible_payment[:content]).to include('TCV supports multiple payment methods')
    end
  end

  describe 'imports_content structure' do
    let(:imports_content) { described_class.data[:imports_content] }

    it 'contains expected number of import steps' do
      expect(imports_content.length).to eq(4)
    end

    it 'has proper structure for each import step' do
      imports_content.each do |step|
        expect(step).to have_key(:title)
        expect(step).to have_key(:content)
        expect(step[:title]).to be_a(String)
        expect(step[:content]).to be_an(Array)
      end
    end

    it 'includes vehicle pickup step' do
      pickup_step = imports_content.find { |s| s[:title].include?('Vehicle Pickup at Port') }
      expect(pickup_step).to be_present
      expect(pickup_step[:content]).to be_an(Array)
      expect(pickup_step[:content].first[:sub_title]).to eq('What to do:')
    end

    it 'includes vehicle registration step' do
      registration_step = imports_content.find { |s| s[:title].include?('Vehicle Registration') }
      expect(registration_step).to be_present
      expect(registration_step[:content]).to be_an(Array)
    end
  end

  describe 'documents_content structure' do
    let(:documents_content) { described_class.data[:documents_content] }

    it 'contains expected number of documents' do
      expect(documents_content.length).to eq(6)
    end

    it 'has proper structure for each document' do
      documents_content.each do |document|
        expect(document).to have_key(:title)
        expect(document).to have_key(:content)
        expect(document[:title]).to be_a(String)
        expect(document[:content]).to be_an(Array)
      end
    end

    it 'includes Japan Export Certificate' do
      export_cert = documents_content.find { |d| d[:title].include?('Japan Export Certificate') }
      expect(export_cert).to be_present
      expect(export_cert[:content]).to be_an(Array)
      expect(export_cert[:content].first[:sub_content]).to include('Proves vehicle export permission')
    end

    it 'includes Bill of Lading' do
      bill_of_lading = documents_content.find { |d| d[:title].include?('Bill of Lading') }
      expect(bill_of_lading).to be_present
      expect(bill_of_lading[:content]).to be_an(Array)
    end
  end

  describe 'strategies_content structure' do
    let(:strategies_content) { described_class.data[:strategies_content] }

    it 'contains expected number of cost categories' do
      expect(strategies_content.length).to eq(3)
    end

    it 'has proper structure for each cost category' do
      strategies_content.each do |category|
        expect(category).to have_key(:title)
        expect(category).to have_key(:content)
        expect(category[:title]).to be_a(String)
        expect(category[:content]).to be_an(Array)
      end
    end

    it 'includes vehicle purchase costs' do
      purchase_costs = strategies_content.find { |s| s[:title].include?('Vehicle Purchase Costs') }
      expect(purchase_costs).to be_present
      expect(purchase_costs[:content]).to be_an(Array)
    end

    it 'includes import & customs costs' do
      import_costs = strategies_content.find { |s| s[:title].include?('Import & Customs Costs') }
      expect(import_costs).to be_present
      expect(import_costs[:content]).to be_an(Array)
    end
  end

  describe 'checklist structure' do
    let(:checklist) { described_class.data[:checklist] }

    it 'contains expected number of checklist sections' do
      expect(checklist.length).to eq(2)
    end

    it 'has proper structure for each checklist section' do
      checklist.each do |section|
        expect(section).to have_key(:column_name)
        expect(section).to have_key(:row_name)
        expect(section[:column_name]).to be_an(Array)
        expect(section[:row_name]).to be_an(Array)
      end
    end

    it 'includes safety standards in first section' do
      first_section = checklist.first
      safety_row = first_section[:row_name].find { |r| r[:block_1] == 'Safety Standards' }
      expect(safety_row).to be_present
      expect(safety_row[:block_2]).to include('Check the latest safety standards')
    end
  end

  describe 'cost_payment structure' do
    let(:cost_payment) { described_class.data[:cost_payment] }

    it 'contains expected number of cost payment items' do
      expect(cost_payment.length).to eq(6)
    end

    it 'has proper structure for cost payment items' do
      cost_payment.each do |item|
        expect(item).to be_a(String)
      end
    end

    it 'includes key cost information' do
      expect(cost_payment.first).to include('The purchase price of a vehicle is only a fraction')
      expect(cost_payment.join(' ')).to include('marine insurance')
    end
  end

  describe 'logistics structure' do
    let(:logistics) { described_class.data[:logistics] }

    it 'contains expected number of logistics sections' do
      expect(logistics.length).to eq(3)
    end

    it 'has proper structure for each logistics section' do
      logistics.each do |section|
        expect(section).to have_key(:title)
        expect(section).to have_key(:content)
        expect(section[:title]).to be_a(String)
        expect(section[:content]).to be_an(Array)
      end
    end

    it 'includes vehicle transportation section' do
      transportation = logistics.find { |l| l[:title].include?('Vehicle Transportation') }
      expect(transportation).to be_present
      expect(transportation[:content]).to be_an(Array)
    end

    it 'includes RoRo shipping method' do
      roro = logistics.find { |l| l[:title].include?('RoRo') }
      expect(roro).to be_present
      expect(roro[:content]).to be_an(Array)
    end

    it 'includes container shipping method' do
      container = logistics.find { |l| l[:title].include?('Container Shipping') }
      expect(container).to be_present
      expect(container[:content]).to be_an(Array)
    end
  end
end
