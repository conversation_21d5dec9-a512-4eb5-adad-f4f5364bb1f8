require 'rails_helper'

RSpec.describe MasterInfo::DefaultPortForCountry do
  describe 'check data' do
    default_ports = [{ country_id: 28, port_id: 125 },
                     { country_id: 533, port_id: 18_631 },
                     { country_id: 36, port_id: 718 },
                     { country_id: 44, port_id: 1417 },
                     { country_id: 50, port_id: 1448 },
                     { country_id: 72, port_id: 21_958 },
                     { country_id: 108, port_id: 27_728 },
                     { country_id: 124, port_id: 3371 },
                     { country_id: 136, port_id: 4178 },
                     { country_id: 152, port_id: 4304 },
                     { country_id: 531, port_id: 36_456 },
                     { country_id: 214, port_id: 5759 },
                     { country_id: 180, port_id: 27_728 },
                     { country_id: 268, port_id: 8815 },
                     { country_id: 288, port_id: 11_281 },
                     { country_id: 320, port_id: 11_709 },
                     { country_id: 328, port_id: 11_744 },
                     { country_id: 388, port_id: 14_936 },
                     { country_id: 404, port_id: 16_640 },
                     { country_id: 296, port_id: 11_310 },
                     { country_id: 426, port_id: 21_958 },
                     { country_id: 454, port_id: 27_728 },
                     { country_id: 496, port_id: 21_521 },
                     { country_id: 508, port_id: 17_665 },
                     { country_id: 104, port_id: 2634 },
                     { country_id: 516, port_id: 21_958 },
                     { country_id: 520, port_id: 17_738 },
                     { country_id: 554, port_id: 18_696 },
                     { country_id: 566, port_id: 18_879 },
                     { country_id: 586, port_id: 19_599 },
                     { country_id: 598, port_id: 20_005 },
                     { country_id: 643, port_id: 21_376 },
                     { country_id: 646, port_id: 27_728 },
                     { country_id: 659, port_id: 21_550 },
                     { country_id: 670, port_id: 21_570 },
                     { country_id: 882, port_id: 36_313 },
                     { country_id: 702, port_id: 21_706 },
                     { country_id: 90, port_id: 2435 },
                     { country_id: 728, port_id: 16_640 },
                     { country_id: 144, port_id: 4229 },
                     { country_id: 740, port_id: 23_233 },
                     { country_id: 748, port_id: 21_958 },
                     { country_id: 834, port_id: 27_728 },
                     { country_id: 764, port_id: 24_242 },
                     { country_id: 780, port_id: 24_325 },
                     { country_id: 800, port_id: 16_640 },
                     { country_id: 826, port_id: 26_766 },
                     { country_id: 840, port_id: 31_545 },
                     { country_id: 894, port_id: 27_728 },
                     { country_id: 716, port_id: 27_728 },
                     { country_id: 372, port_id: 13_174 }]

    default_ports.each do |port|
      it "returns value for #{port[:country_id]}" do
        expect(described_class.find_by(country_id: port[:country_id]).attributes.except(:id)).to eq(port)
      end
    end
  end
end
