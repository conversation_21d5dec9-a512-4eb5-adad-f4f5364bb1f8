require 'rails_helper'

RSpec.describe MasterInfo::AnyDoorSearchOption do
  describe '.default_value' do
    it 'returns the default value' do
      default_option = MasterInfo::AnyDoorSearchOption.default_value
      expect(default_option.value).to eq(MasterInfo::AnyDoorSearchOption::DEFAULT_VALUE)
    end
  end

  describe 'check data' do
    any_door_options = [{ name: '0', value: 88 }, { name: '1', value: 87 }, { name: '2', value: 1 },
                        { name: '3', value: 2 }, { name: '4', value: 3 }, { name: '5', value: 4 }]

    any_door_options.each do |option|
      it "returns value for #{option[:name]}" do
        expect(described_class.find_by(name: option[:name]).attributes.except(:id)).to eq(option)
      end
    end
  end
end
