require 'rails_helper'

RSpec.describe MasterInfo::Fob do
  describe 'check data' do
    fobs = [{ name: 'Under $500', pc_path: 'used_car/all/all/?prct=500', sp_path: 'used_car/all/all/?prct=500' },
            { name: '$500 - $1,000', pc_path: 'used_car/all/all/?prcf=500&prct=1000', sp_path: 'used_car/all/all/?prcf=500&prct=1000' },
            { name: '$1,000 - $1,500', pc_path: 'used_car/all/all/?prcf=1000&prct=1500', sp_path: 'used_car/all/all/?prcf=1000&prct=1500' },
            { name: '$1,500 - $2,000', pc_path: 'used_car/all/all/?prcf=1500&prct=2000', sp_path: 'used_car/all/all/?prcf=1500&prct=2000' },
            { name: '$2,000 - $2,500', pc_path: 'used_car/all/all/?prcf=2000&prct=2500', sp_path: 'used_car/all/all/?prcf=2000&prct=2500' },
            { name: '$2,500 - $5,000', pc_path: 'used_car/all/all/?prcf=2500&prct=5000', sp_path: 'used_car/all/all/?prcf=2500&prct=5000' },
            { name: '$5,000 - $10,000', pc_path: 'used_car/all/all/?prcf=5000&prct=10000', sp_path: 'used_car/all/all/?prcf=5000&prct=10000' },
            { name: '$10,000 - $20,000', pc_path: 'used_car/all/all/?prcf=10000&prct=20000', sp_path: 'used_car/all/all/?prcf=10000&prct=20000' },
            { name: 'Over $20,000', pc_path: 'used_car/all/all/?prcf=20000', sp_path: 'used_car/all/all/?prcf=20000' }]

    fobs.each do |fob|
      it "returns value for #{fob[:name]}" do
        expect(described_class.find_by(name: fob[:name]).attributes.except(:id)).to eq(fob)
      end
    end
  end
end
