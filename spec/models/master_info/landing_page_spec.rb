require 'rails_helper'

RSpec.describe MasterInfo::LandingPage, type: :model do
  describe 'country_name' do
    it 'should have kenya and zambia' do
      expect(described_class.pluck(:country_name)).to include('kenya', 'zambia')
    end
  end

  describe 'country_code' do
    it 'should have 404 and 894' do
      expect(described_class.pluck(:country_code)).to include(404, 894)
    end
  end

  describe 'pay_in_text' do
    it 'should be present for both countries' do
      expect(described_class.pluck(:pay_in_text).all?(&:present?)).to be_truthy
    end
  end

  describe 'intro_text' do
    it 'should be present for both countries' do
      expect(described_class.pluck(:intro_text).all?(&:present?)).to be_truthy
    end
  end

  describe 'contract_link' do
    it 'should be present for both countries' do
      expect(described_class.pluck(:contract_link).all?(&:present?)).to be_truthy
    end
  end

  describe 'contact_info' do
    it 'should have place, link, phone, and phone_link for both countries' do
      described_class.pluck(:contact_info).each do |data|
        expect(data['place']).to be_present
        expect(data['link']).to be_present
        expect(data['phone']).to be_present
        expect(data['phone_link']).to be_present
      end
    end
  end

  describe '.fetch_by_country' do
    context 'when a landing page exists for the given country' do
      let(:country_code) { 404 }

      it 'returns the landing page' do
        result = described_class.fetch_by_country(country_code)
        expect(result&.country_code).to eq(404)
      end
    end

    context 'when no landing page is found for the given country' do
      let(:country_code) { 999 }

      it 'returns nil' do
        result = described_class.fetch_by_country(country_code)
        expect(result).to be_nil
      end
    end

    context 'when an exception is raised during reloading' do
      let(:country_code) { 404 }

      it 'handles the exception' do
        allow(described_class).to receive(:reload).and_raise(StandardError)
        result = described_class.fetch_by_country(country_code)
        expect(result&.country_code).to eq(404)
      end
    end
  end
end
