require 'rails_helper'

RSpec.describe MasterInfo::DriverTypeSearchOption do
  describe 'check data' do
    driver_types = [{ name: '2wheel drive', value: 9 }, { name: '4wheel drive', value: 10 }, { name: 'All wheel drive', value: 11 }]

    driver_types.each do |driver|
      it "returns value for #{driver[:name]}" do
        expect(described_class.find_by(name: driver[:name]).attributes.except(:id)).to eq(driver)
      end
    end
  end
end
