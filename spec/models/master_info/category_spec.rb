require 'rails_helper'

RSpec.describe MasterInfo::Category do
  describe 'check data' do
    data = [{ name: 'Left Hand Drive',
              svg_name: 'icon-handle',
              key_get_stock_count: 'SteeringID:13',
              stock: 0,
              svg_width: 32,
              svg_height: 32,
              pc_path: 'used_car/all/all/?st=13' },
            { name: 'Manual',
              svg_name: 'icon-manual',
              key_get_stock_count: 'TransmissionID:6',
              stock: 0,
              svg_width: 32,
              svg_height: 32,
              pc_path: 'used_car/all/all/?tmns=6' },
            { name: 'Diesel',
              svg_name: 'icon-oilpot',
              key_get_stock_count: 'FuelTypeID:17',
              stock: 0,
              svg_width: 32,
              svg_height: 32,
              pc_path: 'used_car/all/all/?fues=17' },
            { name: '4WD',
              svg_name: 'icon-tire',
              key_get_stock_count: 'DriveTypeID:10',
              stock: 0,
              svg_width: 28,
              svg_height: 28,
              pc_path: 'used_car/all/all/?dr=10' },
            { name: 'No Accidents',
              svg_name: 'icon-no-accidentscar',
              key_get_stock_count: 'IsAccident:0 AND IsNoAccidentsHistory:True',
              stock: 0,
              svg_width: 28,
              svg_height: 28,
              pc_path: 'used_car/all/all/?ac=2' },
            { name: 'Not Repaired',
              svg_name: 'icon-not-repaired',
              key_get_stock_count: 'IsAccident:1',
              stock: 0,
              svg_width: 28,
              svg_height: 28,
              pc_path: 'used_car/all/all/?ac=1' }]

    data.each do |category|
      it "returns value for #{category[:name]}" do
        expect(described_class.find_by(name: category[:name]).attributes.except(:id)).to eq(category)
      end
    end
  end
end
