require 'rails_helper'

DEFAULT_VALUE = 2
RSpec.describe MasterInfo::AccidentSearchOption do
  describe '.default_value' do
    it 'returns the default value' do
      default_option = MasterInfo::AccidentSearchOption.default_value
      expect(default_option.value).to eq(DEFAULT_VALUE)
    end
  end

  describe 'check data' do
    search_options = [{ name: 'Accident Not Repaired', value: 1 }, { name: 'No Accident Only', value: 2 }]

    search_options.each do |option|
      it "returns value for #{option[:name]}" do
        expect(described_class.find_by(name: option[:name]).attributes.except(:id)).to eq(option)
      end
    end
  end
end
