require 'rails_helper'

RSpec.describe MasterInfo::CountryLpTarget do
  describe 'check data' do
    country_lp_targets = [{ id: 1, a3: 'KEN', code: 404, name: 'Kenya', setting_key: 'kenya' },
                          { id: 2, a3: 'ZMB', code: 894, name: 'Zambia', setting_key: 'zambia' },
                          { id: 3, a3: 'USA', code: 840, name: 'United States', setting_key: 'united_states' }]
    country_lp_targets.each do |country|
      it "returns value for #{country[:id]}" do
        expect(described_class.find(country[:id]).attributes).to eq(country)
      end
    end
  end
end
