require 'rails_helper'

RSpec.describe MasterInfo::PopupAssistData, type: :model do
  describe 'data for Kenya' do
    let(:kenya_support_data) { described_class.fetch_by_country(404) }

    it 'should have content and WhatsApp numbers' do
      expect(kenya_support_data[:content]).to be_present
      expect(kenya_support_data[:whats_app]).to be_present
    end
  end

  describe 'data for Zambia' do
    let(:zambia_support_data) { described_class.fetch_by_country(894) }

    it 'should have content and WhatsApp numbers' do
      expect(zambia_support_data[:content]).to be_present
      expect(zambia_support_data[:whats_app]).to be_present
    end
  end

  describe 'data for other countries' do
    let(:other_countries_support_data) { described_class.fetch_by_country(792) }

    it 'should have the correct country code' do
      expect(other_countries_support_data[:country]).to eq('OTHER')
    end

    it 'should have content and text' do
      expect(other_countries_support_data[:content]).to be_present
      expect(other_countries_support_data[:text]).to be_present
    end

    it 'should have backorder link for both signed-in and non-signed-in users' do
      expect(other_countries_support_data[:backorder_link]['signed_in']).to be_present
      expect(other_countries_support_data[:backorder_link]['not_signed_in']).to be_present
    end
  end
end
