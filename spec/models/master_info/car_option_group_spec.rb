require 'rails_helper'

RSpec.describe MasterInfo::CarOptionGroup do
  describe '.option_list' do
    it 'returns car option by ids' do
      car_options_by_group = MasterInfo::CarOptionGroup.first.option_list
      expect(car_options_by_group.map(&:id)).to eq([2, 1, 3, 5, 4])
    end
  end

  describe 'check data' do
    car_option_groups = [
      {
        name: 'Safety',
        data: [
          { 'ids' => [2, 1, 3, 5, 4], 'class_col' => 'col-xs-4' },
        ]
      },
      {
        name: 'Comfort',
        data: [
          { 'ids' => [14, 10, 15, 13, 11, 12, 17, 16, 21, 18, 19, 24, 20, 22, 25, 23], 'class_col' => 'col-xs-4' },
        ]
      },
      {
        name: 'Interia',
        data: [
          { 'ids' => [6, 7, 9, 8, 31, 32, 34, 27, 28, 26, 29], 'class_col' => 'col-xs-4' },
        ]
      },
      {
        name: 'Exterior',
        data: [
          { 'ids' => [30, 33, 35], 'class_col' => 'col-xs-4' },
        ]
      },
      {
        name: 'Selling Points',
        data: [
          { 'ids' => [41, 38, 42], 'class_col' => 'col-xs-3' },
          { 'ids' => [40, 45, 37, 43, 70, 44, 36, 39], 'class_col' => 'col-xs-3af4' },
        ]
      },
    ]

    car_option_groups.each do |category|
      it "returns data for #{category[:name]}" do
        expect(described_class.find_by(name: category[:name]).attributes.except(:id)).to eq(category)
      end
    end
  end
end
