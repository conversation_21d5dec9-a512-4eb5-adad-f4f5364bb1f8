require 'rails_helper'

RSpec.describe MasterInfo::CapacitySearchOption do
  describe 'check data' do
    data = [
      { name: '550', value: 550 },
      { name: '660', value: 660 },
      { name: '800', value: 800 },
      { name: '1000', value: 1000 },
      { name: '1100(1.1L)', value: 1100 },
      { name: '1200(1.2L)', value: 1200 },
      { name: '1300(1.3L)', value: 1300 },
      { name: '1400(1.4L)', value: 1400 },
      { name: '1500(1.5L)', value: 1500 },
      { name: '1600(1.6L)', value: 1600 },
      { name: '1700(1.7L)', value: 1700 },
      { name: '1800(1.8L)', value: 1800 },
      { name: '1900(1.9L)', value: 1900 },
      { name: '2000(2.0L)', value: 2000 },
      { name: '2100(2.1L)', value: 2100 },
      { name: '2200(2.2L)', value: 2200 },
      { name: '2300(2.3L)', value: 2300 },
      { name: '2400(2.4L)', value: 2400 },
      { name: '2500(2.5L)', value: 2500 },
      { name: '2600(2.6L)', value: 2600 },
      { name: '2700(2.7L)', value: 2700 },
      { name: '2800(2.8L)', value: 2800 },
      { name: '2900(2.9L)', value: 2900 },
      { name: '3000(3.0L)', value: 3000 },
      { name: '3500(3.5L)', value: 3500 },
      { name: '4000(4.0L)', value: 4000 },
      { name: '4500(4.5L)', value: 4500 },
      { name: '5000(5.0L)', value: 5000 },
      { name: '5500(5.5L)', value: 5500 },
      { name: '6000(6.0L)', value: 6000 },
    ]

    data.each do |item|
      it "returns value for #{item[:name]}" do
        expect(described_class.find_by(name: item[:name]).attributes.except(:id)).to eq(item)
      end
    end
  end
end
