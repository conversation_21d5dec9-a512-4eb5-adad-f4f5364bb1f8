require 'rails_helper'

RSpec.describe MasterInfo::SteeringSearchOption, type: :model do
  it 'should have 2 drive type options' do
    expect(described_class.count).to eq(2)
  end

  describe 'options value 13' do
    it 'should have correct name' do
      option = described_class.find_by(value: 13)

      expect(option.name).to eq('Left Handle Only')
    end
  end

  describe 'options value 12' do
    it 'should have correct name' do
      option = described_class.find_by(value: 12)

      expect(option.name).to eq('Right Handle Only')
    end
  end

  describe 'check default_value' do
    it 'should have correct name' do
      option = described_class.default_value

      expect(option.value).to eq(described_class::DEFAULT_VALUE)
    end
  end
end
