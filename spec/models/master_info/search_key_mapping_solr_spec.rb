require 'rails_helper'

RSpec.describe MasterInfo::SearchKeyMappingSolr, type: :model do
  let(:fields) { %i[value model get_current_text available_text text group] }

  it 'should have 30 search options' do
    expect(described_class.count).to eq(30)
  end

  describe 'prcf' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'prcf')

      expect(key_data(key)).to eq(['Price', 'MasterInfo::FobSearchOption', nil, nil, nil, nil])
    end
  end

  describe 'prct' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'prct')

      expect(key_data(key)).to eq(['Price', 'MasterInfo::FobSearchOption', nil, nil, nil, nil])
    end
  end

  describe 'aid' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'aid')

      expect(key_data(key)).to eq(['MakeID', 'MasterMake', nil, nil, nil, nil])
    end
  end

  describe 'oid' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'oid')

      expect(key_data(key)).to eq(['ModelID', 'MasterModel', nil, nil, nil, nil])
    end
  end

  describe 'fd' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'fd')

      expect(key_data(key)).to eq(['BodyM', nil, nil, nil, nil, nil])
    end
  end

  describe 'fid' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'fid')

      expect(key_data(key)).to eq(['ModelYear', nil, true, nil, nil, nil])
    end
  end

  describe 'jid' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'jid')

      expect(key_data(key)).to eq(['ModelYear', nil, true, nil, nil, nil])
    end
  end

  describe 'smo' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'smo')

      expect(key_data(key)).to eq(['ModelYearMonth', nil, true, nil, nil, nil])
    end
  end

  describe 'emo' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'emo')

      expect(key_data(key)).to eq(['ModelYearMonth', nil, true, nil, nil, nil])
    end
  end

  describe 'mimn' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'mimn')

      expect(key_data(key)).to eq(['Odometer', 'MasterInfo::MileageSearchOption', nil, nil, nil, nil])
    end
  end

  describe 'mimx' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'mimx')

      expect(key_data(key)).to eq(['Odometer', 'MasterInfo::MileageSearchOption', nil, nil, nil, nil])
    end
  end

  describe 'sds' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'sds')

      expect(key_data(key)).to eq(['Displacement', 'MasterInfo::CapacitySearchOption', nil, nil, nil, nil])
    end
  end

  describe 'eds' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'eds')

      expect(key_data(key)).to eq(['Displacement', 'MasterInfo::CapacitySearchOption', nil, nil, nil, nil])
    end
  end

  describe 'tmns' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'tmns')

      expect(key_data(key)).to eq(['TransmissionID', 'MArticle', nil, nil, nil, 'Transmission'])
    end
  end

  describe 'ac' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'ac')

      expect(key_data(key)).to eq(['IsAccident', 'MasterInfo::AccidentSearchOption', nil, nil, nil, nil])
    end
  end

  describe 'st' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'st')

      expect(key_data(key)).to eq(['SteeringID', 'MasterInfo::SteeringSearchOption', nil, nil, nil, nil])
    end
  end

  describe 'nw' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'nw')

      expect(key_data(key)).to eq(['IsNew', nil, nil, true, 'New Cars Only', nil])
    end
  end

  describe 'spp' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'spp')

      expect(key_data(key)).to eq(['SpecialPriceStatus', nil, nil, true, 'Special Price Only', nil])
    end
  end

  describe 'fues' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'fues')

      expect(key_data(key)).to eq(['FuelTypeID', 'MArticle', nil, nil, nil, 'FuelType'])
    end
  end

  describe 'bsty' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'bsty')

      expect(key_data(key)).to eq(['BodyStyle1', 'MPrimaryBodyStyle', nil, nil, nil, nil])
    end
  end

  describe 'dr' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'dr')

      expect(key_data(key)).to eq(['DriveTypeID', 'MasterInfo::DriverTypeSearchOption', nil, nil, nil, nil])
    end
  end

  describe 'ecls' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'ecls')

      expect(key_data(key)).to eq(['ColorID', 'MArticle', nil, nil, nil, 'ColorType'])
    end
  end

  describe 'do' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'do')

      expect(key_data(key)).to eq(['Door', 'MasterInfo::AnyDoorSearchOption', nil, nil, nil, nil])
    end
  end

  describe 'uid' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'uid')

      expect(key_data(key)).to eq(['UserID', nil, nil, nil, nil, nil])
    end
  end

  describe 'co' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'co')

      expect(key_data(key)).to eq(['CountryID', 'MCountry', nil, nil, nil, nil])
    end
  end

  describe 'rfc' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'rfc')

      expect(key_data(key)).to eq(['ModelYearAndMonth', nil, nil, nil, nil, nil])
    end
  end

  describe 'ctid' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'ctid')

      expect(key_data(key)).to eq(['CategoryID', nil, nil, nil, nil, nil])
    end
  end

  describe 'pbdsid' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'pbdsid')

      expect(key_data(key)).to eq(['BodyStyle1', nil, nil, nil, nil, nil])
    end
  end

  describe 'op' do
    it 'should have correct value, model, get_current_text, available_text, text, group' do
      key = described_class.find_by(key: 'op')

      expect(key_data(key)).to eq(['Options', 'MasterInfo::CarOption', nil, nil, nil, nil])
    end
  end

  private

  def key_data(key)
    fields.map { |field| key[field] }
  end
end
