require 'rails_helper'

RSpec.describe MasterInfo::SuggestData, type: :model do
  it 'should have 36 car models' do
    expect(described_class.count).to eq(36)
  end

  it 'should have toyota land cruiser prado' do
    model = described_class.find_by(name: 'toyota land cruiser prado')

    expect(model).to be_present
  end

  it 'should have toyota markX' do
    model = described_class.find_by(name: 'toyota markX')

    expect(model).to be_present
  end

  it 'should have toyota prius' do
    model = described_class.find_by(name: 'toyota prius')

    expect(model).to be_present
  end

  it 'should have toyota chaser' do
    model = described_class.find_by(name: 'toyota chaser')

    expect(model).to be_present
  end

  it 'should have toyota alphard' do
    model = described_class.find_by(name: 'toyota alphard')

    expect(model).to be_present
  end

  it 'should have toyota alphard hybrid' do
    model = described_class.find_by(name: 'toyota alphard hybrid')

    expect(model).to be_present
  end

  it 'should have toyota crown' do
    model = described_class.find_by(name: 'toyota crown')

    expect(model).to be_present
  end

  it 'should have toyota harrier' do
    model = described_class.find_by(name: 'toyota harrier')

    expect(model).to be_present
  end

  it 'should have toyota hiace' do
    model = described_class.find_by(name: 'toyota hiace')

    expect(model).to be_present
  end

  it 'should have honda crossroad' do
    model = described_class.find_by(name: 'honda crossroad')

    expect(model).to be_present
  end

  it 'should have honda vezel' do
    model = described_class.find_by(name: 'honda vezel')

    expect(model).to be_present
  end

  it 'should have honda civic' do
    model = described_class.find_by(name: 'honda civic')

    expect(model).to be_present
  end

  it 'should have honda fit' do
    model = described_class.find_by(name: 'honda fit')

    expect(model).to be_present
  end

  it 'should have honda fit hybrid' do
    model = described_class.find_by(name: 'honda fit hybrid')

    expect(model).to be_present
  end

  it 'should have honda cr-z' do
    model = described_class.find_by(name: 'honda cr-z')

    expect(model).to be_present
  end

  it 'should have honda cr-v' do
    model = described_class.find_by(name: 'honda cr-v')

    expect(model).to be_present
  end

  it 'should have honda acty truck' do
    model = described_class.find_by(name: 'honda acty truck')

    expect(model).to be_present
  end

  it 'should have honda accord' do
    model = described_class.find_by(name: 'honda accord')

    expect(model).to be_present
  end

  it 'should have nissan silvia' do
    model = described_class.find_by(name: 'nissan silvia')

    expect(model).to be_present
  end

  it 'should have nissan skyline' do
    model = described_class.find_by(name: 'nissan skyline')

    expect(model).to be_present
  end

  it 'should have nissan 180sx' do
    model = described_class.find_by(name: 'nissan 180sx')

    expect(model).to be_present
  end

  it 'should have nissan juku' do
    model = described_class.find_by(name: 'nissan juku')

    expect(model).to be_present
  end

  it 'should have nissan caravan' do
    model = described_class.find_by(name: 'nissan caravan')

    expect(model).to be_present
  end

  it 'should have nissan serena' do
    model = described_class.find_by(name: 'nissan serena')

    expect(model).to be_present
  end

  it 'should have nissan sunny truck' do
    model = described_class.find_by(name: 'nissan sunny truck')

    expect(model).to be_present
  end

  it 'should have nissan sunny' do
    model = described_class.find_by(name: 'nissan sunny')

    expect(model).to be_present
  end

  it 'should have nissan faredy z' do
    model = described_class.find_by(name: 'nissan faredy z')

    expect(model).to be_present
  end

  it 'should have suzuki carry truck' do
    model = described_class.find_by(name: 'suzuki carry truck')

    expect(model).to be_present
  end

  it 'should have suzuki carry van' do
    model = described_class.find_by(name: 'suzuki carry van')

    expect(model).to be_present
  end

  it 'should have suzuki swift' do
    model = described_class.find_by(name: 'suzuki swift')

    expect(model).to be_present
  end

  it 'should have suzuki jimny' do
    model = described_class.find_by(name: 'suzuki jimny')

    expect(model).to be_present
  end

  it 'should have suzuki every' do
    model = described_class.find_by(name: 'suzuki every')

    expect(model).to be_present
  end

  it 'should have suzuki wagonR' do
    model = described_class.find_by(name: 'suzuki wagonR')

    expect(model).to be_present
  end

  it 'should have suzuki alto' do
    model = described_class.find_by(name: 'suzuki alto')

    expect(model).to be_present
  end

  it 'should have suzuki cappuccino' do
    model = described_class.find_by(name: 'suzuki cappuccino')

    expect(model).to be_present
  end

  it 'should have suzuki escudo' do
    model = described_class.find_by(name: 'suzuki escudo')

    expect(model).to be_present
  end
end
