require 'rails_helper'

RSpec.describe MasterInfo::SpecificPortInCountry, type: :model do
  it 'should have 28 country-port mappings' do
    expect(described_class.count).to eq(28)
  end

  describe 'country 834' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 834)[:ports]

      expect(ports).to include(
        { 'port_id' => 16_640, 'port_name' => 'MOMBASA' },
        { 'port_id' => 27_728, 'port_name' => 'DAR ES SALAAM' },
      )
    end
  end

  describe 'country 800' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 800)[:ports]

      expect(ports).to include(
        { 'port_id' => 16_640, 'port_name' => 'MOMBASA' },
        { 'port_id' => 27_728, 'port_name' => 'DAR ES SALAAM' },
      )
    end
  end

  describe 'country 894' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 894)[:ports]

      expect(ports).to include(
        { 'port_id' => 27_728, 'port_name' => 'DAR ES SALAAM' },
        { 'port_id' => 17_665, 'port_name' => 'MAPUTO' },
        { 'port_id' => 21_958, 'port_name' => 'DURBAN' },
      )
    end
  end

  describe 'country 404' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 404)[:ports]

      expect(ports).to include(
        { 'port_id' => 16_640, 'port_name' => 'MOMBASA' },
      )
    end
  end

  describe 'country 716' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 716)[:ports]

      expect(ports).to include(
        { 'port_id' => 16_640, 'port_name' => 'MOMBASA' },
        { 'port_id' => 27_728, 'port_name' => 'DAR ES SALAAM' },
        { 'port_id' => 17_665, 'port_name' => 'MAPUTO' },
        { 'port_id' => 21_958, 'port_name' => 'DURBAN' },
      )
    end
  end

  describe 'country 454' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 454)[:ports]

      expect(ports).to include(
        { 'port_id' => 21_958, 'port_name' => 'DURBAN' },
        { 'port_id' => 27_728, 'port_name' => 'DAR ES SALAAM' },
      )
    end
  end

  describe 'country 180' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 180)[:ports]

      expect(ports).to include(
        { 'port_id' => 16_640, 'port_name' => 'MOMBASA' },
        { 'port_id' => 27_728, 'port_name' => 'DAR ES SALAAM' },
        { 'port_id' => 21_958, 'port_name' => 'DURBAN' },
      )
    end
  end

  describe 'country 108' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 108)[:ports]

      expect(ports).to include(
        { 'port_id' => 16_640, 'port_name' => 'MOMBASA' },
        { 'port_id' => 27_728, 'port_name' => 'DAR ES SALAAM' },
      )
    end
  end

  describe 'country 508' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 508)[:ports]

      expect(ports).to include(
        { 'port_id' => 17_665, 'port_name' => 'MAPUTO' },
      )
    end
  end

  describe 'country 710' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 710)[:ports]

      expect(ports).to include(
        { 'port_id' => 21_958, 'port_name' => 'DURBAN' },
      )
    end
  end

  describe 'country 728' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 728)[:ports]

      expect(ports).to include(
        { 'port_id' => 16_640, 'port_name' => 'MOMBASA' },
      )
    end
  end

  describe 'country 72' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 72)[:ports]

      expect(ports).to include(
        { 'port_id' => 21_958, 'port_name' => 'DURBAN' },
      )
    end
  end

  describe 'country 288' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 288)[:ports]

      expect(ports).to include(
        { 'port_id' => 11_281, 'port_name' => 'TEMA' },
      )
    end
  end

  describe 'country 426' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 426)[:ports]

      expect(ports).to include(
        { 'port_id' => 21_958, 'port_name' => 'DURBAN' },
      )
    end
  end

  describe 'country 450' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 450)[:ports]

      expect(ports).to include(
        { 'port_id' => 17_023, 'port_name' => 'TAMATAVE (TOAMASINA)' },
      )
    end
  end

  describe 'country 516' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 516)[:ports]

      expect(ports).to include(
        { 'port_id' => 21_958, 'port_name' => 'DURBAN' },
      )
    end
  end

  describe 'country 566' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 566)[:ports]

      expect(ports).to include(
        { 'port_id' => 18_879, 'port_name' => 'LAGOS' },
      )
    end
  end

  describe 'country 646' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 646)[:ports]

      expect(ports).to include(
        { 'port_id' => 16_640, 'port_name' => 'MOMBASA' },
        { 'port_id' => 27_728, 'port_name' => 'DAR ES SALAAM' },
      )
    end
  end

  describe 'country 28' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 28)[:ports]

      expect(ports).to include(
        { 'port_id' => 125, 'port_name' => "ST JOHN'S" },
      )
    end
  end

  describe 'country 40' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 40)[:ports]

      expect(ports).to include(
        { 'port_id' => 9123, 'port_name' => 'BREMERHAVEN' },
      )
    end
  end

  describe 'country 203' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 203)[:ports]

      expect(ports).to include(
        { 'port_id' => 9123, 'port_name' => 'BREMERHAVEN' },
      )
    end
  end

  describe 'country 398' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 398)[:ports]

      expect(ports).to include(
        { 'port_id' => 8815, 'port_name' => 'POTI' },
        { 'port_id' => 21_521, 'port_name' => 'VLADIVOSTOK' },
      )
    end
  end

  describe 'country 417' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 417)[:ports]

      expect(ports).to include(
        { 'port_id' => 8815, 'port_name' => 'POTI' },
        { 'port_id' => 21_521, 'port_name' => 'VLADIVOSTOK' },
      )
    end
  end

  describe 'country 496' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 496)[:ports]

      expect(ports).to include(
        { 'port_id' => 21_521, 'port_name' => 'VLADIVOSTOK' },
      )
    end
  end

  describe 'country 703' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 703)[:ports]

      expect(ports).to include(
        { 'port_id' => 20_637, 'port_name' => 'GDYNIA' },
      )
    end
  end

  describe 'country 756' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 756)[:ports]

      expect(ports).to include(
        { 'port_id' => 9123, 'port_name' => 'BREMERHAVEN' },
      )
    end
  end

  describe 'country 531' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 531)[:ports]

      expect(ports).to include(
        { 'port_id' => 36_456, 'port_name' => 'CURACAO' },
      )
    end
  end

  describe 'country 748' do
    it 'should have correct ports' do
      ports = described_class.find_by(country_number: 748)[:ports]

      expect(ports).to include(
        { 'port_id' => 21_958, 'port_name' => 'DURBAN' },
      )
    end
  end
end
