require 'rails_helper'

RSpec.describe TListingCount, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:service_id) }
    it { should validate_presence_of(:create_date) }
    it { should validate_presence_of(:update_date) }
    it { should validate_presence_of(:total_count) }
    it { should validate_numericality_of(:total_count).only_integer }
    it { should validate_presence_of(:country_number) }
    it { should validate_numericality_of(:country_number).only_integer }
    it { should validate_presence_of(:make_id) }
    it { should validate_numericality_of(:make_id).only_integer }
    it { should validate_presence_of(:model_id) }
    it { should validate_numericality_of(:model_id).only_integer }
    it { should validate_presence_of(:parts_category_id) }
    it { should validate_numericality_of(:parts_category_id).only_integer }
    it { should validate_presence_of(:make_brand_id) }
    it { should validate_numericality_of(:make_brand_id).only_integer }
    it { should validate_presence_of(:model_master_id) }
    it { should validate_numericality_of(:model_master_id).only_integer }
    it { should validate_presence_of(:buy_now_only_count) }
    it { should validate_numericality_of(:buy_now_only_count).only_integer }
  end
end
