require 'rails_helper'

RSpec.describe MPort, type: :model do
  describe 'associations' do
    it { should belong_to(:m_country).class_name(MCountry.name) }
  end

  describe 'validations' do
    it { should validate_presence_of(:country_number) }
    it { should validate_presence_of(:port_cd) }
    it { should validate_length_of(:port_cd).is_at_most(3) }
    it { should validate_presence_of(:port_name) }
    it { should validate_length_of(:port_name).is_at_most(100) }
    it { should validate_presence_of(:port_initial) }
    it { should validate_length_of(:port_initial).is_equal_to(1) }
    it { should validate_exclusion_of(:is_valid).in_array([nil]) }
    it { should validate_presence_of(:create_date) }
    it { should validate_presence_of(:update_date) }
  end
end
