require 'rails_helper'

RSpec.describe MAreaGroup, type: :model do
  describe 'Associations' do
    it { should belong_to(:m_country).with_foreign_key(:country_id).with_primary_key(:number) }
    it { should belong_to(:m_area).with_foreign_key(:area_id).with_primary_key(:area_id) }
  end

  describe 'Validations' do
    it { should validate_presence_of(:area_id) }
    it { should validate_presence_of(:country_id) }
    it { should validate_exclusion_of(:is_delete).in_array([nil]) }
  end
end
