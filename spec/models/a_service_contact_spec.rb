require 'rails_helper'

RSpec.describe AServiceContact, type: :model do
  describe 'Validations' do
    it { should validate_presence_of(:service_contact_id) }
    it { should validate_length_of(:referrer_type).is_at_most(100) }
    it { should validate_length_of(:src).is_at_most(100) }
    it { should validate_length_of(:src_2).is_at_most(100) }
    it { should validate_length_of(:src_3).is_at_most(100) }
    it { should validate_presence_of(:registered_date) }
  end
end
