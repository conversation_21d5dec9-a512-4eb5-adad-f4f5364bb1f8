require 'rails_helper'

RSpec.describe TAggregateRankingPoint, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:maker_id) }
    it { should validate_numericality_of(:maker_id).only_integer }
    it { should validate_presence_of(:model_id) }
    it { should validate_numericality_of(:model_id).only_integer }
    it { should validate_presence_of(:maker_nm) }
    it { should validate_length_of(:maker_nm).is_at_most(50) }
    it { should validate_presence_of(:model_nm) }
    it { should validate_length_of(:model_nm).is_at_most(50) }
    it { should validate_presence_of(:ranking_point) }
    it { should validate_numericality_of(:ranking_point).only_integer }
    it { should validate_presence_of(:create_date) }
    it { should validate_presence_of(:update_date) }
  end

  describe 'class methods' do
    describe '.query_by_make_and_model' do
      it 'returns matching records for given maker and model IDs' do
        create(:t_aggregate_ranking_point, maker_id: 2, model_id: 2)
        create(:t_aggregate_ranking_point, maker_id: 1, model_id: 3)

        couple_maker_model = [[2, 2]]
        limit = 2

        results = TAggregateRankingPoint.query_by_make_and_model(couple_maker_model, limit)
        expect(results.size).to eq(1)
        expect(results.map { |r| [r.maker_id, r.model_id] }).to eq(couple_maker_model)
      end

      it 'returns empty array if no matching records found' do
        create(:t_aggregate_ranking_point, maker_id: 1, model_id: 1)
        create(:t_aggregate_ranking_point, maker_id: 2, model_id: 2)

        couple_maker_model = [[3, 3], [4, 4]]
        limit = 2

        results = TAggregateRankingPoint.query_by_make_and_model(couple_maker_model, limit)

        expect(results).to be_empty
      end
    end
  end
end
