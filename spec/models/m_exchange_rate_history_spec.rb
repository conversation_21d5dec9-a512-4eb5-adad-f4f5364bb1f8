require 'rails_helper'

RSpec.describe MExchangeRateHistory, type: :model do
  describe 'Validations' do
    it { should validate_presence_of(:target_date) }
    it { should validate_presence_of(:exchange_id) }
    it { should validate_presence_of(:exchange_name) }
    it { should validate_length_of(:exchange_name).is_at_most(30) }
    it { should validate_presence_of(:exchange_name_ascii) }
    it { should validate_length_of(:exchange_name_ascii).is_at_most(10) }
    it { should validate_presence_of(:exchange_symbol) }
    it { should validate_length_of(:exchange_symbol).is_at_most(30) }
    it { should validate_presence_of(:rate) }
    it { should validate_numericality_of(:rate) }
  end
end
