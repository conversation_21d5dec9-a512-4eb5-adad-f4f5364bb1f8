require 'rails_helper'

RSpec.describe MOption, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:entry_date) }
    it { should validate_presence_of(:edit_date) }
    it { should validate_exclusion_of(:is_delete).in_array([nil]) }
    it { should validate_presence_of(:name) }
    it { should validate_length_of(:name).is_at_most(100) }
    it { should validate_presence_of(:group_id) }
    it { should validate_presence_of(:sort) }
  end
end
