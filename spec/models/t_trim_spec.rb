require 'rails_helper'

RSpec.describe TTrim, type: :model do
  let!(:existing_record) { FactoryBot.create(:t_trim, trim_id: '12323', master_model: FactoryBot.create(:master_model, id: '12322')) }
  subject { FactoryBot.build(:t_trim) }

  describe 'validations' do
    it { should validate_presence_of(:model_id) }
    it { should validate_numericality_of(:model_id).only_integer }
    it { should validate_presence_of(:trim_id) }
    it { should validate_numericality_of(:trim_id).only_integer }
    it { should validate_presence_of(:pattern_type) }
    it { should validate_presence_of(:created_date) }
    it { should validate_presence_of(:updated_date) }
    it { should validate_length_of(:pattern_type).is_at_most(40) }
    it { should validate_length_of(:short_pattern_type).is_at_most(50) }
    it { should validate_numericality_of(:vehicle_length).only_integer.allow_nil }
    it { should validate_numericality_of(:vehicle_width).only_integer.allow_nil }
    it { should validate_numericality_of(:vehicle_height).only_integer.allow_nil }
    it { should validate_exclusion_of(:is_valid).in_array([nil]) }
    it { should validate_uniqueness_of(:model_id).scoped_to(:trim_id) }
  end
end
