require 'rails_helper'

RSpec.describe TCountryIpAddress, type: :model do
  describe 'associations' do
    it { should belong_to(:m_country).with_foreign_key(:country_id).with_primary_key(:number) }
  end

  describe 'validations' do
    it { should validate_presence_of(:ip_number_start) }
    it { should validate_presence_of(:ip_number_end) }
    it { should validate_presence_of(:created_date) }
    it { should validate_presence_of(:updated_date) }
    it { should validate_presence_of(:ip_address_start) }
    it { should validate_length_of(:ip_address_start).is_at_most(50) }
    it { should validate_presence_of(:ip_address_end) }
    it { should validate_length_of(:ip_address_end).is_at_most(50) }
    it { should validate_presence_of(:country_id) }
    it { should validate_length_of(:country_name).is_at_most(50) }
  end
end
