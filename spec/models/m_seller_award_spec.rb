require 'rails_helper'

RSpec.describe MSellerAward, type: :model do
  describe 'scopes' do
    describe '.valid' do
      it 'should return only valid seller awards' do
        valid_seller_award = create(:m_seller_award, is_valid: true)
        invalid_seller_award = create(:m_seller_award, is_valid: false)

        expect(described_class.valid).to include(valid_seller_award)
        expect(described_class.valid).not_to include(invalid_seller_award)
      end
    end
  end
end
