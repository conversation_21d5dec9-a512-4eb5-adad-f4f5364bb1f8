require 'rails_helper'

RSpec.describe TListingNumber, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:create_date) }
    it { should validate_presence_of(:update_date) }
    it { should validate_presence_of(:stock_date) }
    it { should validate_presence_of(:make_id) }
    it { should validate_numericality_of(:make_id).only_integer }
    it { should validate_presence_of(:model_id) }
    it { should validate_numericality_of(:model_id).only_integer }
    it { should validate_presence_of(:make_model_stock) }
    it { should validate_numericality_of(:make_model_stock).only_integer }
  end
end
