require 'rails_helper'

RSpec.describe MakeBrand, type: :model do
  describe 'Validations' do
    it { should validate_presence_of(:make_id) }
    it { should validate_presence_of(:brand_id) }
    it { should validate_presence_of(:country_id) }
    it { should validate_exclusion_of(:is_active).in_array([nil]) }
    it { should validate_exclusion_of(:is_delete).in_array([nil]) }
  end

  describe 'Associations' do
    it { should belong_to(:make) }
  end
end
