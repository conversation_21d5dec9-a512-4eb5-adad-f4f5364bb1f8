require 'rails_helper'

RSpec.describe Trim, type: :model do
  describe 'validations' do
    it { should validate_length_of(:pattern_type).is_at_most(50) }
    it { should validate_length_of(:short_pattern_type).is_at_most(50) }
    it { should validate_length_of(:trim_name).is_at_most(200) }
    it { should validate_length_of(:trim_ascii_name).is_at_most(100) }
    it { should validate_length_of(:remarks).is_at_most(2000) }
    it { should validate_numericality_of(:doors).only_integer.allow_nil }
    it { should validate_numericality_of(:body_style_id).only_integer.allow_nil }
    it { should validate_numericality_of(:transmission_id).only_integer.allow_nil }
    it { should validate_numericality_of(:curb_weight).only_integer.allow_nil }
    it { should validate_numericality_of(:wheelbase).only_integer.allow_nil }
    it { should validate_numericality_of(:tread_front).only_integer.allow_nil }
    it { should validate_numericality_of(:tread_rear).only_integer.allow_nil }
    it { should validate_numericality_of(:length).only_integer.allow_nil }
    it { should validate_numericality_of(:width).only_integer.allow_nil }
    it { should validate_numericality_of(:height).only_integer.allow_nil }
    it { should validate_numericality_of(:fuel_capacity).only_integer.allow_nil }
    it { should validate_numericality_of(:ground_clearance).only_integer.allow_nil }
    it { should validate_numericality_of(:suspension_type_front_id).only_integer.allow_nil }
    it { should validate_numericality_of(:suspension_type_rear_id).only_integer.allow_nil }
    it { should validate_numericality_of(:brake_type_front_id).only_integer.allow_nil }
    it { should validate_numericality_of(:brake_type_rear_id).only_integer.allow_nil }
    it { should validate_numericality_of(:engine_location_id).only_integer.allow_nil }
    it { should validate_numericality_of(:tire_front_id).only_integer.allow_nil }
    it { should validate_numericality_of(:tire_rear_id).only_integer.allow_nil }
    it { should validate_numericality_of(:spring_front_id).only_integer.allow_nil }
    it { should validate_numericality_of(:spring_rear_id).only_integer.allow_nil }
    it { should validate_numericality_of(:steering_type_id).only_integer.allow_nil }
    it { should validate_numericality_of(:steering_location_id).only_integer.allow_nil }
    it { should validate_numericality_of(:seat).only_integer.allow_nil }
    it { should validate_numericality_of(:drive_wheel_id).only_integer.allow_nil }
    it { should validate_numericality_of(:engine_id).only_integer.allow_nil }
    it { should validate_numericality_of(:power_source_id).only_integer.allow_nil }
    it { should validate_numericality_of(:special_edition_id).only_integer.allow_nil }
    it { should validate_numericality_of(:luggage_capacity).only_integer.allow_nil }
    it { should validate_numericality_of(:total_power).only_integer.allow_nil }
    it { should validate_numericality_of(:total_power_kw).only_integer.allow_nil }
    it { should validate_numericality_of(:total_torque_nm).only_integer.allow_nil }
    it { should validate_numericality_of(:limited_sales_count).only_integer.allow_nil }
    it { should validate_numericality_of(:sales_end_year).only_integer.allow_nil }
    it { should validate_numericality_of(:sales_end_month).only_integer.allow_nil }
  end
end
