require 'rails_helper'

RSpec.describe MasterMake, type: :model do
  describe 'validations' do
    it { should validate_length_of(:vc_name).is_at_most(100) }
    it { should validate_length_of(:vc_name_e).is_at_most(100) }
  end

  describe 'associations' do
    it { should have_many(:master_models).with_foreign_key(:id_make).with_primary_key(:id_make) }
  end

  describe 'class methods' do
    describe '.get_by_vc_name_e' do
      it 'returns the first record with matching vc_name_e' do
        make = create(:master_make, vc_name_e: 'example_make')

        expect(MasterMake.get_by_vc_name_e('example_make')).to eq(make)
      end

      it 'returns nil if no record matches vc_name_e' do
        expect(MasterMake.get_by_vc_name_e('non_existent_make')).to be_nil
      end
    end
  end
end
