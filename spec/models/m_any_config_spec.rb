require 'rails_helper'

RSpec.describe MAnyConfig, type: :model do
  describe 'Validations' do
    it { should validate_presence_of(:category) }
    it { should validate_numericality_of(:category).only_integer }
    it { should validate_presence_of(:sub_category) }
    it { should validate_numericality_of(:sub_category).only_integer }
    it { should validate_inclusion_of(:sub_category).in_range(MIN_INT..MAX_INT) }
    it { should validate_presence_of(:sequence) }
    it { should validate_numericality_of(:sequence).only_integer }
    it { should validate_inclusion_of(:sequence).in_range(MIN_INT..MAX_INT) }
    it { should validate_presence_of(:any_data) }
    it { should validate_presence_of(:data_type) }
    it { should validate_length_of(:data_type).is_at_most(16) }
    it { should validate_exclusion_of(:is_valid).in_array([nil]) }
    it { should validate_presence_of(:key_name) }
    it { should validate_length_of(:key_name).is_at_most(128) }
    it { should validate_presence_of(:remark) }
    it { should validate_length_of(:remark).is_at_most(300) }
  end

  describe 'scopes' do
    describe '.country_code_has_fob' do
      it 'returns records with category 4 and sub_category 5' do
        record1 = create(:m_any_config, category: 4, sub_category: 5)
        record2 = create(:m_any_config, category: 4, sub_category: 5)
        record3 = create(:m_any_config, category: 4, sub_category: 6)
        result = MAnyConfig.country_code_has_fob

        expect(result).to include(record1, record2)
        expect(result).not_to include(record3)
      end
    end
  end
end
