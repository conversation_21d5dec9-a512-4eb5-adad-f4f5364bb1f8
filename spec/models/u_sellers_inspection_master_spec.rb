require 'rails_helper'

RSpec.describe USellersInspectionMaster, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:user_id) }
    it { should validate_presence_of(:inspection_id) }
    it { should validate_presence_of(:exchange_rate_id) }
    it { should validate_presence_of(:create_date) }
    it { should validate_presence_of(:update_date) }
    it { should validate_presence_of(:price) }
    it { should validate_presence_of(:displacement_min) }
    it { should validate_presence_of(:displacement_max) }
    it { should validate_presence_of(:displacement_name) }
    it { should validate_exclusion_of(:is_valid).in_array([nil]) }
    it { should validate_numericality_of(:price) }
    it { should validate_numericality_of(:displacement_min).only_integer }
    it { should validate_numericality_of(:displacement_max).only_integer }
    it { should validate_length_of(:displacement_name).is_at_most(50) }
  end
end
