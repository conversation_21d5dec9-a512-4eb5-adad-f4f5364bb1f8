require 'rails_helper'

RSpec.describe TFeedbackSummary, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:create_date) }
    it { should validate_presence_of(:update_date) }
    it { should validate_numericality_of(:feedback_count1_year).only_integer.allow_nil }
    it { should validate_numericality_of(:yes_count1_year).only_integer.allow_nil }
    it { should validate_numericality_of(:no_count1_year).only_integer.allow_nil }
    it { should validate_numericality_of(:feedback_count_half_year).only_integer.allow_nil }
    it { should validate_numericality_of(:yes_count_half_year).only_integer.allow_nil }
    it { should validate_numericality_of(:no_count_half_year).only_integer.allow_nil }
    it { should validate_numericality_of(:feedback_count_total).only_integer.allow_nil }
    it { should validate_numericality_of(:yes_count_total).only_integer.allow_nil }
    it { should validate_numericality_of(:no_count_total).only_integer.allow_nil }
  end
end
