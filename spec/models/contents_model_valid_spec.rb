require 'rails_helper'

RSpec.describe ContentsModelValid, type: :model do
  let!(:existing_record) { FactoryBot.create(:contents_model_valid, contents_id: '12322', model_master_id: '12323') }
  subject { FactoryBot.build(:contents_model_valid) }
  describe 'Validations' do
    it { should validate_presence_of(:contents_id) }
    it { should validate_presence_of(:model_master_id) }
    it { should validate_numericality_of(:contents_id).only_integer }
    it { should validate_numericality_of(:model_master_id).only_integer }
    it { should validate_uniqueness_of(:contents_id).scoped_to(:model_master_id) }
  end
end
