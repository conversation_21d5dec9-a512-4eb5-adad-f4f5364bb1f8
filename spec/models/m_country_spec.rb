require 'rails_helper'

RSpec.describe MCountry, type: :model do
  let(:m_country) { { usa: 840, kenya: 404, korea: 408, yugo: 891 } }

  describe 'Associations' do
    it { should have_one(:m_localize).with_foreign_key(:country_id).with_primary_key(:id) }
    it { should have_one(:m_area_group).with_foreign_key(:country_id).with_primary_key(:number) }
    it { should have_many(:t_country_ip_address).with_foreign_key(:country_id).with_primary_key(:number) }
    it { should have_many(:t_port_listings).with_foreign_key(:country_number).with_primary_key(:number) }
    it { should have_many(:m_ports).with_foreign_key(:country_number).with_primary_key(:number) }
  end

  describe 'Validations' do
    it { should validate_presence_of(:country) }
    it { should validate_presence_of(:a2) }
    it { should validate_presence_of(:a3) }
    it { should validate_presence_of(:number) }
    it { should validate_length_of(:country).is_at_most(100) }
    it { should validate_length_of(:a2).is_equal_to(2) }
    it { should validate_length_of(:a3).is_equal_to(3) }
  end

  describe 'Constants' do
    it 'correctly defines LP_REGION_MAPPING' do
      expect(described_class::LP_REGION_MAPPING).to eq(
        {
          'kenya' => 404,
          'zambia' => 894,
          'unitedstates' => 840
        },
      )
    end

    it 'correctly defines UNITED_STATES_CODE' do
      expect(described_class::UNITED_STATES_CODE).to eq(840)
    end

    it 'correctly defines CONGGO_COUNTRY_CODE' do
      expect(described_class::CONGGO_COUNTRY_CODE).to eq(180)
    end

    it 'has the correct value for KENYA_COUNTRY_CODE' do
      expect(described_class::KENYA_COUNTRY_CODE).to eq(404)
    end

    it 'has the correct value for ZAMBIA_COUNTRY_CODE' do
      expect(described_class::ZAMBIA_COUNTRY_CODE).to eq(894)
    end

    it 'has the correct value for NORTH_KOREA_COUNTRY_CODE' do
      expect(described_class::NORTH_KOREA_COUNTRY_CODE).to eq(408)
    end

    it 'has the correct value for YUGOSLAVIA_COUNTRY_CODE' do
      expect(described_class::YUGOSLAVIA_COUNTRY_CODE).to eq(891)
    end

    it 'has the correct value for SPECIFIC_COUNTRIES' do
      expect(described_class::SPECIFIC_COUNTRIES).to eq([408, 891, 336])
    end

    it 'has the correct value for COUNTRY_REQUIRE_PRESHIP' do
      expect(described_class::COUNTRY_REQUIRE_PRESHIP).to eq(
        [242, 328, 404, 508, 598, 659, 662, 716, 728, 800, 834],
      )
    end

    it 'has the correct value for COUNTRY_DEFAULT_SHIPPING' do
      expect(described_class::COUNTRY_DEFAULT_SHIPPING).to eq(
        [372, 826],
      )
    end

    it 'has the correct value for COUNTRY_LIST_SUPPORT_INSPECTION' do
      expect(described_class::COUNTRY_LIST_SUPPORT_INSPECTION).to eq(
        [36, 44, 50, 144, 242, 328, 388, 404, 480, 554, 508, 598, 659, 662, 716, 728, 800, 834, 894],
      )
    end

    it 'has the correct value for COUNTRY_LIST_SPECIFIC_PAYMENT' do
      expect(described_class::COUNTRY_LIST_SPECIFIC_PAYMENT).to eq([44, 894])
    end

    it 'has the correct value for COUNTRY_FIXED_SP_PHONE_NUMBER' do
      expect(described_class::COUNTRY_FIXED_SP_PHONE_NUMBER).to eq([50, 144, 566, 288])
    end

    it 'has the correct value for COUNTRY_CODE_HAS_REGULAR_CAR' do
      expect(described_class::COUNTRY_CODE_HAS_REGULAR_CAR).to eq(
        [404, 840, 800, 44, 124, 388, 50, 704, 740, 591, 480, 566, 882, 586, 598, 604, 144, 702, 496],
      )
    end

    it 'has the correct value for COUNTRY_CODE_NEED_SHOW_GDPR' do
      expect(described_class::COUNTRY_CODE_NEED_SHOW_GDPR).to eq(
        [56, 100, 203, 208, 276, 233, 372, 300, 724, 250, 191, 380, 196, 428, 440, 442,
         348, 470, 528, 40, 616, 620, 642, 705, 703, 246, 752, 826, 352, 483, 578],
      )
    end

    it 'has the correct value for COUNTRY_LIST_NOT_INCLUDE_INSURANCE' do
      expect(described_class::COUNTRY_LIST_NOT_INCLUDE_INSURANCE).to eq([404, 834, 800])
    end
  end

  describe '.get_country_by_ip' do
    it 'returns the country based on the given IP' do
      TCountryIpAddress.create(ip_number_start: 1, ip_number_end: 10, created_date: Time.now, updated_date: Time.now,
                               ip_address_start: '0.0.0.0', ip_address_end: '0.0.0.0', country_id: m_country[:usa], is_valid: true)

      # Call the method being tested
      result = described_class.get_country_by_ip(5)

      # Verification
      expect(result.country).to eq('United States')
    end

    it 'returns nil if no country matches the given IP' do
      # Call the method being tested
      result = described_class.get_country_by_ip(15)

      # Verification
      expect(result).to be_nil
    end
  end

  describe '.country_has_regular_car' do
    it 'returns the country with regular car for the provided code' do
      # Call the method being tested
      result = described_class.country_has_regular_car(m_country[:kenya])

      # Verification
      expect(result.number).to eq(m_country[:kenya])
    end

    it 'returns nil if the country does not have regular car for the provided code' do
      code = 999

      # Call the method being tested
      result = described_class.country_has_regular_car(code)

      # Verification
      expect(result).to be_nil
    end
  end

  describe '.display_gdpr?' do
    it 'returns true if the country code needs to display GDPR' do
      code = 56

      # Call the method being tested
      result = described_class.display_gdpr?(code)

      # Verification
      expect(result).to eq(true)
    end

    it 'returns false if the country code does not need to display GDPR' do
      code = 123

      # Call the method being tested
      result = described_class.display_gdpr?(code)

      # Verification
      expect(result).to eq(false)
    end

    it 'returns false if the country code is nil' do
      code = nil

      # Call the method being tested
      result = described_class.display_gdpr?(code)

      # Verification
      expect(result).to eq(false)
    end
  end

  describe '.country_for_select_options' do
    it 'returns the list of countries for select options' do
      results = described_class.country_for_select_options
      # Verification
      expect(results.map(&:number)).to include(m_country[:kenya], m_country[:usa])
    end
  end
end
