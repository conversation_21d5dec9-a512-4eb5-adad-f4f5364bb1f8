require 'rails_helper'

RSpec.describe MakeBrandsToWorldMake, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:make_brand_id) }
    it { should validate_presence_of(:world_make_id) }
    it { should validate_presence_of(:create_date) }
    it { should validate_presence_of(:update_date) }
    it { should validate_exclusion_of(:is_valid).in_array([nil]) }
  end

  describe 'associations' do
    it { should belong_to(:model_master).with_primary_key(:make_brand_id).with_foreign_key(:make_brand_id) }
  end
end
