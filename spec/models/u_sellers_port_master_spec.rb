require 'rails_helper'

RSpec.describe USellersPortMaster, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:user_id) }
    it { should validate_presence_of(:create_date) }
    it { should validate_presence_of(:update_date) }
    it { should validate_exclusion_of(:is_valid).in_array([nil]) }
    it { should validate_presence_of(:port_id) }
    it { should validate_numericality_of(:sort).only_integer.allow_nil }
  end
end
