require 'rails_helper'

RSpec.describe UThankyouOffersConditionDetail, type: :model do
  describe 'validations' do
    it { should validate_numericality_of(:thankyou_offers_condition_detail_id).only_integer.allow_nil }
    it { should validate_presence_of(:thankyou_offers_condition_id) }
    it { should validate_numericality_of(:thankyou_offers_condition_id).only_integer }
    it { should validate_presence_of(:max_count) }
    it { should validate_presence_of(:device) }
    it { should validate_presence_of(:priority) }
    it { should validate_presence_of(:search_type) }
    it { should validate_presence_of(:sort_order) }
    it { should validate_presence_of(:create_date) }
  end
end
