require 'rails_helper'

RSpec.describe MLocalize, type: :model do
  describe 'associations' do
    it { should belong_to(:m_country).with_foreign_key(:country_id).with_primary_key(:id) }
  end

  describe 'validations' do
    it { should validate_presence_of(:create_date) }
    it { should validate_presence_of(:update_date) }
    it { should validate_exclusion_of(:is_valid).in_array([nil]) }
    it { should validate_exclusion_of(:is_logistics).in_array([nil]) }
  end
end
