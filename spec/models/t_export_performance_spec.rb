require 'rails_helper'

RSpec.describe TExportPerformance, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:create_date) }
    it { should validate_presence_of(:update_date) }
    it { should validate_presence_of(:stock_date) }
    it { should validate_presence_of(:make_id) }
    it { should validate_numericality_of(:make_id).only_integer }
    it { should validate_presence_of(:model_id) }
    it { should validate_numericality_of(:model_id).only_integer }
    it { should validate_presence_of(:all_money_receive_count) }
    it { should validate_numericality_of(:all_money_receive_count).only_integer }
    it { should validate_presence_of(:make_money_receive_count) }
    it { should validate_numericality_of(:make_money_receive_count).only_integer }
    it { should validate_presence_of(:model_money_receive_count) }
    it { should validate_numericality_of(:model_money_receive_count).only_integer }
  end
end
