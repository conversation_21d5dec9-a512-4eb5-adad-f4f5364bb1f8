require 'rails_helper'

RSpec.describe MakesTranslate, type: :model do
  describe 'Validations' do
    it { should validate_presence_of(:lang_id) }
    it { should validate_presence_of(:make_id) }
    it { should validate_presence_of(:make_name) }
    it { should validate_exclusion_of(:is_valid).in_array([nil]) }
    it { should validate_presence_of(:entry_date) }
    it { should validate_presence_of(:update_date) }
    it { should validate_length_of(:make_name).is_at_most(50) }
  end
end
