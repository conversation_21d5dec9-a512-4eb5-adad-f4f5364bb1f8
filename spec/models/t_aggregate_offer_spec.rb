require 'rails_helper'

RSpec.describe TAggregateOffer, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:maker_id) }
    it { should validate_numericality_of(:maker_id).only_integer }
    it { should validate_presence_of(:model_id) }
    it { should validate_numericality_of(:model_id).only_integer }
    it { should validate_presence_of(:maker_nm) }
    it { should validate_length_of(:maker_nm).is_at_most(50) }
    it { should validate_presence_of(:model_nm) }
    it { should validate_length_of(:model_nm).is_at_most(50) }
    it { should validate_presence_of(:body_type_id) }
    it { should validate_numericality_of(:body_type_id).only_integer }
    it { should validate_presence_of(:price_range) }
    it { should validate_numericality_of(:price_range).only_integer }
    it { should validate_presence_of(:offer_count) }
    it { should validate_numericality_of(:offer_count).only_integer }
    it { should validate_presence_of(:create_date) }
    it { should validate_presence_of(:update_date) }
  end

  describe 'constants' do
    it 'has INVALID_BODY_TYPE_ID constant' do
      expect(TAggregateOffer::INVALID_BODY_TYPE_ID).to eq([0, 12])
    end

    it 'has DEFAULT_BODY_TYPE_ID constant' do
      expect(TAggregateOffer::DEFAULT_BODY_TYPE_ID).to eq(0)
    end
  end

  describe 'class methods' do
    let(:make_id) { 1 }
    let(:model_id) { 2 }
    let(:body_type_id) { 3 }
    let(:price_range) { 4 }
    let(:count) { 5 }

    describe '.most_body_type_for_model' do
      it 'returns the body type ID for the model with the most offer count' do
        # Create test data
        create(:t_aggregate_offer, maker_id: make_id, model_id: model_id, body_type_id: body_type_id, offer_count: 10)
        create(:t_aggregate_offer, maker_id: make_id, model_id: model_id, body_type_id: body_type_id + 1, offer_count: 5)
        create(:t_aggregate_offer, maker_id: make_id, model_id: model_id, body_type_id: body_type_id + 2, offer_count: 8)

        body_type = TAggregateOffer.most_body_type_for_model(make_id, model_id)

        expect(body_type).to eq(body_type_id)
      end
    end

    describe '.price_range_for_body_type' do
      it 'returns the price range for the specified body type' do
        # Create test data
        create(:t_aggregate_offer, maker_id: make_id, model_id: model_id,
                                   body_type_id: body_type_id, price_range: price_range)
        create(:t_aggregate_offer, maker_id: make_id, model_id: model_id,
                                   body_type_id: body_type_id, price_range: price_range + 1)
        create(:t_aggregate_offer, maker_id: make_id, model_id: model_id,
                                   body_type_id: body_type_id, price_range: price_range + 2)

        result = TAggregateOffer.price_range_for_body_type(make_id, model_id, body_type_id)

        expect(result).to eq(price_range)
      end
    end

    describe '.same_model_cars_list' do
      it 'returns a list of cars with the same body type and price range, excluding the specified model' do
        # Create test data
        model = create(:t_aggregate_offer, maker_id: make_id, model_id: model_id, body_type_id: body_type_id, price_range: price_range)
        create(:t_aggregate_offer, maker_id: make_id, body_type_id: body_type_id, price_range: price_range)
        create(:t_aggregate_offer, maker_id: make_id, model_id: model_id + 1, body_type_id: body_type_id, price_range: price_range)
        create(:t_aggregate_offer, maker_id: make_id, model_id: model_id + 2, body_type_id: body_type_id, price_range: price_range)

        cars_list = TAggregateOffer.same_model_cars_list(model_id, body_type_id, price_range)

        expect(cars_list.size).to eq(3)
        expect(cars_list).not_to include(model)
      end
    end

    describe '.get_price_range_for_make_model' do
      it 'returns the price range for the specified make and model with the default body type' do
        # Create test data
        create(:t_aggregate_offer, maker_id: make_id, model_id: model_id, body_type_id: TAggregateOffer::DEFAULT_BODY_TYPE_ID,
                                   price_range: price_range)
        create(:t_aggregate_offer, maker_id: make_id, model_id: model_id, body_type_id: TAggregateOffer::DEFAULT_BODY_TYPE_ID,
                                   price_range: price_range + 1)
        create(:t_aggregate_offer, maker_id: make_id, model_id: model_id, body_type_id: TAggregateOffer::DEFAULT_BODY_TYPE_ID,
                                   price_range: price_range + 2)

        result = TAggregateOffer.get_price_range_for_make_model(make_id, model_id)

        expect(result).to eq(price_range)
      end
    end

    describe '.top_model_by_offer_count' do
      it 'returns the top models by offer count within the specified price range excluding model_id' do
        # Create test data
        create(:t_aggregate_offer, maker_id: make_id, model_id: model_id, body_type_id: TAggregateOffer::DEFAULT_BODY_TYPE_ID,
                                   price_range: price_range, offer_count: 10)
        offer_2 = create(:t_aggregate_offer, maker_id: make_id, model_id: model_id + 1, body_type_id: TAggregateOffer::DEFAULT_BODY_TYPE_ID,
                                             price_range: price_range, offer_count: 8)
        offer_3 = create(:t_aggregate_offer, maker_id: make_id, model_id: model_id + 2, body_type_id: TAggregateOffer::DEFAULT_BODY_TYPE_ID,
                                             price_range: price_range, offer_count: 6)

        count = 10
        top_models = TAggregateOffer.top_model_by_offer_count(price_range, model_id, count)

        expect(top_models.size).to eq(2)
        expect(top_models.first.model_id).to eq(offer_2.model_id)
        expect(top_models.last.model_id).to eq(offer_3.model_id)
      end
    end

    describe '.top_model_in_price_range' do
      it 'returns the top models by offer count within the specified price range' do
        high_count_model = create(:t_aggregate_offer, maker_id: make_id, model_id: model_id, body_type_id: TAggregateOffer::DEFAULT_BODY_TYPE_ID,
                                                      price_range: price_range, offer_count: 10)
        create(:t_aggregate_offer, maker_id: make_id, model_id: model_id + 1, body_type_id: TAggregateOffer::DEFAULT_BODY_TYPE_ID,
                                   price_range: price_range, offer_count: 8)
        low_count_model = create(:t_aggregate_offer, maker_id: make_id, model_id: model_id + 2, body_type_id: TAggregateOffer::DEFAULT_BODY_TYPE_ID,
                                                     price_range: price_range, offer_count: 6)
        excluded_model = create(:t_aggregate_offer, maker_id: make_id, model_id: model_id + 3, body_type_id: TAggregateOffer::DEFAULT_BODY_TYPE_ID,
                                                    price_range: price_range + 1, offer_count: 8)
        create(:t_aggregate_offer, maker_id: make_id, model_id: model_id + 4, body_type_id: TAggregateOffer::DEFAULT_BODY_TYPE_ID,
                                   price_range: price_range + 1, offer_count: 1)

        limit = 4
        result = TAggregateOffer.top_all_model_in_price_range(price_range, limit)

        expect(result.size).to eq(3)
        expect(result.first.model_id).to eq(high_count_model.model_id)
        expect(result.last.model_id).to eq(low_count_model.model_id)
        expect(result.map(&:model_id)).not_to include(excluded_model.model_id)
      end
    end
  end
end
