require 'rails_helper'

RSpec.describe TOfferResponseRateSummary, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:create_date) }
    it { should validate_presence_of(:update_date) }
    it { should validate_numericality_of(:offer_count).allow_nil }
    it { should validate_numericality_of(:response_count).allow_nil }
    it { should validate_numericality_of(:response_time).allow_nil }
  end
end
