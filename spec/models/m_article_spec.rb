require 'rails_helper'

RSpec.describe MArticle, type: :model do
  describe 'Validations' do
    it { should validate_presence_of(:entry_date) }
    it { should validate_presence_of(:edit_date) }
    it { should validate_exclusion_of(:is_delete).in_array([nil]) }
    it { should validate_presence_of(:group_name) }
    it { should validate_length_of(:group_name).is_at_most(50) }
    it { should validate_presence_of(:sort) }
    it { should validate_numericality_of(:sort).only_integer }
    it { should validate_presence_of(:en) }
    it { should validate_length_of(:en).is_at_most(50) }
  end

  describe 'Constants' do
    it 'TRANSMISSION_ABBREVIATION is frozen' do
      expect(MArticle::TRANSMISSION_ABBREVIATION).to be_frozen
    end

    it 'TRANSMISSION_ABBREVIATION keys have valid values' do
      valid_values = %w[AMT AT MT Others]
      expect(MArticle::TRANSMISSION_ABBREVIATION.values.uniq).to match_array(valid_values)
    end
  end
end
