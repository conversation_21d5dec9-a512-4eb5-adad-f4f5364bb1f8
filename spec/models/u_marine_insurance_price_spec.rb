require 'rails_helper'

RSpec.describe UMarineInsurancePrice, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:user_id) }
    it { should validate_presence_of(:invoice_price_min) }
    it { should validate_presence_of(:create_date) }
    it { should validate_presence_of(:update_date) }
    it { should validate_exclusion_of(:is_valid).in_array([nil]) }
    it { should validate_numericality_of(:invoice_price_max).allow_nil }
    it { should validate_numericality_of(:invoice_price_min) }
    it { should validate_numericality_of(:icca_price).allow_nil }
    it { should validate_numericality_of(:iccb_price).allow_nil }
    it { should validate_numericality_of(:iccc_price).allow_nil }
    it { should validate_numericality_of(:iccd_price).allow_nil }
    it { should validate_numericality_of(:icce_price).allow_nil }
  end
end
