require 'rails_helper'

RSpec.describe ModelMasterTranslate, type: :model do
  describe 'associations' do
    it { should belong_to(:model_master).with_foreign_key(:model_master_id) }
  end

  describe 'validations' do
    it { should validate_presence_of(:lang_id) }
    it { should validate_numericality_of(:lang_id).only_integer }
    it { should validate_presence_of(:model_master_id) }
    it { should validate_numericality_of(:model_master_id).only_integer }
    it { should validate_presence_of(:entry_date) }
    it { should validate_presence_of(:update_date) }
    it { should validate_presence_of(:custom_model_name) }
    it { should validate_length_of(:custom_model_name).is_at_most(50) }
  end
end
