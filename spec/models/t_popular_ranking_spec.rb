require 'rails_helper'

RSpec.describe TPopularRanking, type: :model do
  describe 'associations' do
    it { should belong_to(:master_model).with_foreign_key('model_id') }
  end

  describe 'validations' do
    it { should validate_presence_of(:country_number) }
    it { should validate_presence_of(:make_id) }
    it { should validate_presence_of(:model_id) }
    it { should validate_presence_of(:ranking_point) }
    it { should validate_presence_of(:make_brand_id) }
    it { should validate_presence_of(:model_master_id) }
    it { should validate_presence_of(:model_year) }
    it { should validate_length_of(:media_template_path).is_at_most(400) }
    it { should validate_length_of(:media_template_prefix).is_at_most(10) }
    it { should validate_length_of(:media_template_suffix).is_at_most(50) }
    it { should validate_numericality_of(:country_number).only_integer }
    it { should validate_numericality_of(:make_id).only_integer }
    it { should validate_numericality_of(:model_id).only_integer }
    it { should validate_numericality_of(:ranking_point).only_integer }
    it { should validate_numericality_of(:make_brand_id).only_integer }
    it { should validate_numericality_of(:model_master_id).only_integer }
    it { should validate_numericality_of(:model_year).only_integer }
    it { should validate_uniqueness_of(:country_number).scoped_to(:make_id, :model_id).ignoring_case_sensitivity }
  end

  describe '.fetch_popular_ranking' do
    let!(:popular_ranking1) { create(:t_popular_ranking, country_number: 1, make_id: 1, ranking_point: 10) }
    let!(:popular_ranking2) { create(:t_popular_ranking, country_number: 1, make_id: 2, ranking_point: 20) }
    let!(:popular_ranking3) { create(:t_popular_ranking, country_number: 2, make_id: 1, ranking_point: 30) }

    it 'returns popular rankings for a given country code and limit' do
      result = described_class.fetch_popular_ranking(1, 2)
      expect(result).to contain_exactly(popular_ranking2, popular_ranking1)
    end

    it 'returns popular rankings for a given country code, limit, and make_id' do
      result = described_class.fetch_popular_ranking(1, 1, 1)
      expect(result).to contain_exactly(popular_ranking1)
    end
  end
end
