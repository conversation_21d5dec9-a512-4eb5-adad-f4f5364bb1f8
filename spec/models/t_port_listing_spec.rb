require 'rails_helper'

RSpec.describe TPortListing, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:country_number) }
    it { should validate_presence_of(:port_cd) }
    it { should validate_presence_of(:create_date) }
    it { should validate_presence_of(:update_date) }
    it { should validate_presence_of(:category_id) }
    it { should validate_length_of(:port_cd).is_at_most(3) }
    it { should validate_exclusion_of(:is_valid).in_array([nil]) }
  end

  describe 'associations' do
    it { should belong_to(:m_country) }
  end

  describe 'constants' do
    it 'has a specific category constant' do
      expect(described_class::SPECIFIC_CATEGORY).equal?(1)
    end
  end
end
