require 'rails_helper'

RSpec.describe MtCountry, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:lang_id) }
    it { should validate_numericality_of(:lang_id).only_integer }
    it { should validate_presence_of(:country_id) }
    it { should validate_numericality_of(:country_id).only_integer }
    it { should validate_length_of(:name).is_at_most(50) }
    it { should validate_presence_of(:entry_date) }
    it { should validate_presence_of(:edit_date) }
  end
end
