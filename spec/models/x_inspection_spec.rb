require 'rails_helper'

RSpec.describe XInspection, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:inspection_id_by_country) }
    it { is_expected.to validate_numericality_of(:inspection_id_by_country).only_integer }
    it { should validate_presence_of(:inspection_id_by_seller) }
    it { is_expected.to validate_numericality_of(:inspection_id_by_seller).only_integer }
    it { should validate_presence_of(:create_date) }
  end
end
