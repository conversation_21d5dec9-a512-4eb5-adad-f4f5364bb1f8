require 'rails_helper'

RSpec.describe Make, type: :model do
  describe 'validations' do
    it { should validate_exclusion_of(:is_valid).in_array([nil]) }
    it { should validate_length_of(:make_name).is_at_most(50) }
    it { should validate_length_of(:make_ascii_name).is_at_most(50) }
    it { should validate_length_of(:description).is_at_most(2000) }
  end

  describe 'associations' do
    it { should have_many(:make_brands).with_foreign_key(:make_id) }
  end
end
