require 'rails_helper'

RSpec.describe MSecondaryBodyStyle, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:entry_date) }
    it { should validate_presence_of(:edit_date) }
    it { should validate_exclusion_of(:is_delete).in_array([nil]) }
    it { should validate_presence_of(:primary_body_style_id) }
    it { should validate_inclusion_of(:primary_body_style_id).in_range(-128..127) }
    it { should validate_presence_of(:name) }
    it { should validate_length_of(:name).is_at_most(100) }
    it { should validate_presence_of(:sort) }
    it { should validate_inclusion_of(:sort).in_range(-128..127) }
    it { should validate_presence_of(:category_id) }
    it { should validate_inclusion_of(:category_id).in_range(-128..127) }
  end
end
