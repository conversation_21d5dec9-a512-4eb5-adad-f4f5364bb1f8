require 'rails_helper'

RSpec.describe MImportRegulation, type: :model do
  let(:regulation) { FactoryBot.create(:m_import_regulation, country_number: 24, regulations_id: 2) }
  describe 'validations' do
    it { should validate_presence_of(:country_number) }
    it { should validate_presence_of(:regulations_id) }
    it { should validate_presence_of(:create_date) }
    it { should validate_presence_of(:update_date) }
    it { should validate_exclusion_of(:is_valid).in_array([nil]) }
    it { should validate_exclusion_of(:is_import_regulations).in_array([nil]) }
    it { should validate_numericality_of(:model_year_from).only_integer }
    it { should validate_numericality_of(:model_year_to).only_integer }
    it { should validate_numericality_of(:steering_id).only_integer }
    it 'validates uniqueness of country_number scoped to regulations_id' do
      regulation
      should validate_uniqueness_of(:country_number).scoped_to(:regulations_id)
    end
  end
end
