require 'rails_helper'

RSpec.describe UThankyouOffersSellerCondition, type: :model do
  context 'validations' do
    it { should validate_presence_of(:thankyou_offers_condition_detail_id) }
    it { should validate_numericality_of(:thankyou_offers_condition_detail_id).only_integer }
    it { should validate_presence_of(:with_type) }
    it { should validate_numericality_of(:with_type).only_integer }
    it { should validate_presence_of(:seller_id) }
    it { should validate_numericality_of(:seller_id).only_integer }
    it { should validate_presence_of(:create_date) }
  end
end
