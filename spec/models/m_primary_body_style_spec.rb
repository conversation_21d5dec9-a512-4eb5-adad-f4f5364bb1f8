require 'rails_helper'

RSpec.describe MPrimaryBodyStyle, type: :model do
  describe 'constants' do
    it 'should have METAS_BODY_STYLE constant' do
      expect(described_class::METAS_BODY_STYLE).to eq([2, 3, 6, 7, 8, 9, 11])
    end
  end

  describe 'validations' do
    it { should validate_presence_of(:entry_date) }
    it { should validate_presence_of(:edit_date) }
    it { should validate_exclusion_of(:is_delete).in_array([nil]) }
    it { should validate_presence_of(:name) }
    it { should validate_length_of(:name).is_at_most(100) }
    it { should validate_presence_of(:sort) }
    it { should validate_inclusion_of(:sort).in_range(-128..127) }
    it { should validate_presence_of(:category_id) }
    it { should validate_inclusion_of(:category_id).in_range(-128..127) }
  end

  describe 'bsty_non_counter_cache' do
    it 'should correctly return the expected records' do
      bsty_ids = [7, 8, 9, 6, 1, 3, 11, 2, 10, 4, 5]
      bsty_records = described_class.bsty_non_counter_cache

      expect(bsty_records.size).to eq(bsty_ids.size)

      bsty_records.each_with_index do |bsty, index|
        expect(bsty.primary_body_style_id).to eq(bsty_ids[index])
      end
    end
  end
end
