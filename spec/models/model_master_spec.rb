require 'rails_helper'

RSpec.describe ModelMaster, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:make_brand_id) }
    it { should validate_presence_of(:custom_model_name) }
    it { should validate_presence_of(:model_ascii_name) }
    it { should validate_exclusion_of(:is_delete).in_array([nil]) }
    it { should validate_length_of(:custom_model_name).is_at_most(50) }
    it { should validate_length_of(:model_ascii_name).is_at_most(300) }
  end

  describe 'associations' do
    it { should have_many(:model_master_translates) }
    it { should have_many(:model_sales_masters) }
    it { should have_many(:model_master_to_world_models) }
    it { should have_many(:make_brands_to_world_makes).with_foreign_key(:make_brand_id) }
    it { should belong_to(:make_brand).with_foreign_key(:make_brand_id).class_name(MakeBrand.name) }
  end
end
