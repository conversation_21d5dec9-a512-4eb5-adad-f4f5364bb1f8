require 'rails_helper'

RSpec.describe ModelMasterToWorldModel, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:model_master_id) }
    it { should validate_numericality_of(:model_master_id).only_integer }
    it { should validate_presence_of(:world_model_id) }
    it { should validate_numericality_of(:world_model_id).only_integer }
    it { should validate_presence_of(:create_date) }
    it { should validate_presence_of(:update_date) }
  end

  describe 'associations' do
    it { should belong_to(:model_master) }
  end
end
