require 'rails_helper'

RSpec.describe MName, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:group_key) }
    it { should validate_length_of(:group_key).is_at_most(50) }
    it { should validate_presence_of(:id_of_master) }
    it { should validate_presence_of(:lang_id) }
    it { should validate_presence_of(:name) }
    it { should validate_length_of(:name).is_at_most(50) }
    it { should validate_exclusion_of(:is_valid).in_array([nil]) }
  end

  describe 'scopes' do
    describe '.valid' do
      it 'returns records with is_valid set to true' do
        valid_record = create(:m_name, is_valid: true)
        invalid_record = create(:m_name, is_valid: false)

        expect(MName.valid).to include(valid_record)
        expect(MName.valid).not_to include(invalid_record)
      end
    end
  end
end
