require 'rails_helper'

RSpec.describe MArea, type: :model do
  describe 'Associations' do
    it { should have_many(:m_area_groups).with_foreign_key(:area_id).with_primary_key(:area_id) }
  end

  describe 'Validations' do
    it { should validate_presence_of(:entry_date) }
    it { should validate_presence_of(:edit_date) }
    it { should validate_presence_of(:area) }
    it { should validate_length_of(:area).is_at_most(50) }
    it { should validate_presence_of(:en) }
    it { should validate_length_of(:en).is_at_most(50) }
    it { should validate_presence_of(:sort) }
    it { should validate_numericality_of(:sort).only_integer }
    it { should validate_numericality_of(:sort_localize).only_integer }
  end
end
