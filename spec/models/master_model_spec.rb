require 'rails_helper'

RSpec.describe MasterModel, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:id_make) }
    it { should validate_presence_of(:vc_name) }
    it { should validate_presence_of(:id_category) }
    it { should validate_exclusion_of(:is_valid).in_array([nil]) }
    it { should validate_presence_of(:id_model_old) }
    it { should validate_presence_of(:dt_created) }
    it { should validate_presence_of(:dt_updated) }
    it { should validate_presence_of(:temp_model_id) }
    it { should validate_presence_of(:temp_make_id) }
    it { should validate_length_of(:vc_name).is_at_most(100) }
    it { should validate_length_of(:vc_name_e).is_at_most(100) }
  end

  describe 'associations' do
    it { should belong_to(:master_make).with_primary_key(:id_make).with_foreign_key(:id_make) }
  end

  describe 'scopes' do
    describe '.valid' do
      it 'returns valid MasterModels' do
        # create valid and invalid MasterModels using factories or fixtures
        valid_model = create(:master_model, is_valid: true)
        invalid_model = create(:master_model, is_valid: false)

        # call the scope
        result = described_class.valid

        # assert the result
        expect(result).to include(valid_model)
        expect(result).not_to include(invalid_model)
      end
    end
  end

  describe 'class methods' do
    describe '.get_by_vc_name_e' do
      it 'returns the MasterModel with matching name' do
        # create a MasterModel with vc_name_e matching "test_name"
        create(:master_model, vc_name_e: 'test_name')

        # call the class method
        result = described_class.get_by_vc_name_e('test_name')

        # assert the result
        expect(result).to be_a(MasterModel)
        expect(result.vc_name_e).to eq('test_name')
      end
    end

    describe '.fetch_vc_name_e' do
      it 'returns an array of vc_name_e for given model ids' do
        # create multiple MasterModels with different vc_name_e values
        models = create_list(:master_model, 3)

        # call the class method
        result = described_class.fetch_vc_name_e(models.pluck(:id_model))

        # assert the result
        expect(result).to match_array(models.pluck(:vc_name_e))
      end
    end
  end
end
