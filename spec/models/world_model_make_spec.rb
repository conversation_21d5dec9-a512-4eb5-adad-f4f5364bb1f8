require 'rails_helper'

RSpec.describe WorldModelMake, type: :model do
  describe 'validations' do
    it { should validate_numericality_of(:world_make_id).only_integer.allow_nil }
    it { should validate_numericality_of(:world_make_old_id).only_integer.allow_nil }
    it { should validate_exclusion_of(:is_valid).in_array([nil]) }
    it { should validate_presence_of(:entry_date) }
    it { should validate_presence_of(:update_date) }
  end
end
