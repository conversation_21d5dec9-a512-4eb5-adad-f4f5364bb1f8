require 'rails_helper'

RSpec.describe MCsPhoneNumber, type: :model do
  describe 'Validations' do
    it { should validate_presence_of(:phone_numer) }
    it { should validate_length_of(:phone_numer).is_at_most(32) }
    it { should validate_presence_of(:country_number) }
    it { should validate_numericality_of(:country_number).only_integer }
    it { should validate_exclusion_of(:is_whats_up).in_array([nil]) }
    it { should validate_presence_of(:priority) }
    it { should validate_numericality_of(:priority).only_integer }
    it { should validate_exclusion_of(:is_sp_disp).in_array([nil]) }
    it { should validate_exclusion_of(:is_valid).in_array([nil]) }
  end

  describe 'Constants' do
    it 'COUNTRY_HAS_ADDITIONAL_PHONE_NUMBER is frozen' do
      expect(MCsPhoneNumber::COUNTRY_HAS_ADDITIONAL_PHONE_NUMBER).to be_frozen
    end

    it 'COUNTRY_FIXED_PHONE_NUMBER is frozen' do
      expect(MCsPhoneNumber::COUNTRY_FIXED_PHONE_NUMBER).to be_frozen
    end

    it 'COUNTRY_HAS_PHONE_NUMBER_ON_DB is frozen' do
      expect(MCsPhoneNumber::COUNTRY_HAS_PHONE_NUMBER_ON_DB).to be_frozen
    end

    it 'FIXED_PHONE_NUMBER_LIST is frozen' do
      expect(MCsPhoneNumber::FIXED_PHONE_NUMBER_LIST).to be_frozen
    end

    it 'FIXED_SP_PHONE_NUMBER_LIST is frozen' do
      expect(MCsPhoneNumber::FIXED_SP_PHONE_NUMBER_LIST).to be_frozen
    end
  end

  describe '.find_cs_phone_number_by_country' do
    context 'when providing an unsupported country code' do
      it 'returns nil' do
        expect(MCsPhoneNumber.find_cs_phone_number_by_country(999, 123)).to be_nil
      end
    end

    context 'when providing a supported country code' do
      it 'returns phone numbers if available in the database' do
        expect(MCsPhoneNumber.find_cs_phone_number_by_country(834, 123)[:phone_number]).to include('+254 715 720 325')
      end

      it 'returns pre-defined numbers if additional numbers are ignored' do
        expect(MCsPhoneNumber).to receive(:ignore_additional_numbers).with(834, 123).and_return(true)
        expect(MCsPhoneNumber.find_cs_phone_number_by_country(834, 123)[:phone_number]).to eq(
                                                                                             '+254 748 635 414 / +254 748 635 445 / +254 715 720 325',
                                                                                           )
      end

      it 'returns a merged list of fixed and database numbers if additional numbers are not ignored' do
        expect(MCsPhoneNumber.find_cs_phone_number_by_country(800, 0)[:title])
          .to include('For follow up and updates TCV Uganda Customer Center staff will get back')
      end
    end
  end
end

describe '.data_cs_sp_number_phone' do
  it 'returns valid numbers for CS SP' do
    allow(MCsPhoneNumber).to receive(:where).and_return([])
    result = MCsPhoneNumber.data_cs_sp_number_phone(834)
    expect(result).to eq([])
  end
end

describe '.ignore_additional_numbers' do
  it 'returns false when conditions for ignoring additional numbers are not met' do
    country_code = 834
    country_access = 'sample_access'

    result = MCsPhoneNumber.ignore_additional_numbers(country_code, country_access)
    expect(result).to be_falsy
  end
end
