require 'rails_helper'

RSpec.describe Transactions::OffersController, type: :request do
  describe 'Transactions' do
    let(:offer_response) do
      {
        itemId: 561_279,
        serviceContactId: 71_091,
        memberContactId: 70_302,
        makeId: 207,
        modelId: 50_892,
        isOrder: false,
        seller_id: 850_102,
        authentication:
        { cvpd: 'cvpd_cookies' }
      }.to_mash
    end

    let(:car_id) { 561_279 }

    context 'when not internal offer' do
      before do
        allow_any_instance_of(Transactions::Offers).to receive(:create).and_return([offer_response, true])
      end

      it 'return success /ajax_v2/transactions/offers' do
        post offers_path(item_id: car_id)
        expect(response).to have_http_status(:success)
        expect(response.content_type).to eq('application/json; charset=utf-8')
        expect(JSON.parse(response.body)['is_success']).to eq(true)
      end

      it 'return success /ajax_v2/transactions/offers/create_offer_with_ab_test_simple_signup' do
        post create_offer_with_ab_test_simple_signup_offers_path(item_id: car_id)
        expect(response).to have_http_status(:success)
        expect(response.content_type).to eq('application/json; charset=utf-8')
        expect(JSON.parse(response.body)['is_success']).to eq(true)
      end
    end

    context 'when internal offer' do
      before do
        allow_any_instance_of(Transactions::OffersController).to receive(:internal_offer?).and_return(true)
      end

      it 'return success /ajax_v2/transactions/offers' do
        post offers_path(item_id: car_id)
        expect(response).to have_http_status(:success)
        expect(response.content_type).to eq('application/json; charset=utf-8')
        expect(JSON.parse(response.body)['is_success']).to eq(false)
        expect(JSON.parse(response.body)['response']['errorMessage']).to eq('You are internal member.')
      end

      it 'return success /ajax_v2/transactions/offers/create_offer_with_ab_test_simple_signup' do
        post create_offer_with_ab_test_simple_signup_offers_path(item_id: car_id)
        expect(response).to have_http_status(:success)
        expect(response.content_type).to eq('application/json; charset=utf-8')
        expect(JSON.parse(response.body)['is_success']).to eq(false)
        expect(JSON.parse(response.body)['response']['errorMessage']).to eq('You are internal member.')
      end
    end

    context 'temp' do
      it 'create success' do
        post temp_offers_path(item_id: car_id)
        expect(response).to have_http_status(:success)
        expect(response.content_type).to eq('application/json; charset=utf-8')
      end

      it 'show success' do
        get temp_offers_path
        expect(response).to have_http_status(:success)
        expect(response.content_type).to eq('application/json; charset=utf-8')
      end
    end
  end
end
