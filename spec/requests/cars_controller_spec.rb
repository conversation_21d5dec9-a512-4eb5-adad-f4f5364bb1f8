require 'rails_helper'

RSpec.describe CarsController, type: :request do
  describe 'car controller' do
    let(:make) { FactoryBot.create(:master_make) }
    let(:model) { FactoryBot.create(:master_model) }
    let(:car_id) { 1 }
    let(:car_stock) do
      {
        'itemId' => 561_440,
        'name' => '2078',
        'detail' => '',
        'makeId' => make.id,
        'modelId' => model.id,
        'price' => 900.0,
        'priceDomestic' => 0.0,
        'priceExchangeRateId' => 4,
        'priceExchangeRateIdDomestic' => 0,
        'isAsk' => false,
        'firstRegistrationDate' => '2014-01-01T00:00:00',
        'odmeter' => 2078,
        'odometerOption' => 86,
        'displacement' => 2078,
        'steeringId' => 12,
        'transmissionId' => 5,
        'fuelId' => 15,
        'trimName' => '',
        'chassisNo' => '2078',
        'modelCode' => '2078',
        'bodystyle1Id' => 11,
        'bodystyle2Id' => 0,
        'passengers' => 1,
        'door' => 87,
        'vehicleLength' => 20_780.0,
        'vehicleWidth' => 20_780.0,
        'vehicleHeight' => 20_780.0,
        'vehicleOption' => [],
        'exteriorColorId' => 25,
        'interiorColorId' => 0,
        'userNumber' => '************',
        'modelYear' => 2014,
        'isFavorite' => false,
        'marketPriceStatus' => 0,
        'stockSalesStatus' => 0,
        'newStockUrl' => nil,
        'isAccident' => false,
        'terminateDate' => DateTime.now + 1.month,
        'mechanicalProblem' => '',
        'isAccountSuspended' => false,
        'driveTypeId' => 9,
        'manufactureYear' => 2014,
        'manufactureMonth' => 1,
        'isNew' => false,
        'isPegasusItem' => false,
        'pegasusUserCountryNumber' => 0,
        'imageCount' => 1,
        'sellerId' => 853_172,
        'sellerName' => 'Jung Corporation',
        'orderBuyerId' => 0,
        'isPegasusChildInvalid' => false,
        'isValid' => true,
        'authentication' => { 'cvpd' => '' }
      }
    end
    let(:seller_params) { { allow_inquiry: true, seller_id: 1 } }
    let(:seller_response) do
      {
        'seller_name' => 'seller_name',
        'seller_id' => 1,
        'feedback' => {
          'rating_avg_total' => 4.5,
          'accurate_avg_total' => 6,
          'communication_avg_total' => 6,
          'quickly_avg_total' => 7,
          'yes_count_total' => 10,
          'no_count_total' => 2,
          'rating_avg_half_year' => 10,
          'accurate_avg_half_year' => 7,
          'communication_avg_half_year' => 8,
          'quickly_avg_half_year' => 6
        },
        'reviews' => {
          'total_reviews' => [
            {
              'accurate' => 4,
              'communication' => 10,
              'quickly' => 7,
              'senderCountryNumber' => 392,
              'senderName' => 'TESTER'
            },
          ],
          'half_reviews' => [
            {
              'isRecommend' => true,
              'modelYear' => '2020',
              'makeName' => 'Toyota',
              'modelName' => 'Camry',
              'senderCountryNumber' => 392,
              'fbCreatedDate' => '2021-10-05',
              'accurate' => 4.2,
              'communication' => 4.4,
              'quickly' => 4.3,
              'comment' => 'Smooth transaction.',
              'replyComment' => 'We appreciate your business!',
              'senderName' => 'TESTER'
            },
          ]
        },
        'seller_award_icon' => 'favorite-star',
        'reply_rate' => 4.0,
        'reply_speed' => 5.0,
        'average_rate' => 4.5
      }
    end
    let(:recommend_cars_response) do
      [
        {
          'CountryID' => '392',
          'Make' => 'Toyota',
          'Model' => '86',
          'ModelYear' => 2010,
          'ItemID' => 561_435,
          'ModelID' => 54_743,
          'UserID' => 853_172,
          'FOBPrice' => 'US$1,800',
          'OptionsForSP' => [2078, 2078, '2WD', 'AT'],
          'path' => '/used_car/toyota/86/561435/'
        },
      ]
    end
    let(:total_count) { 319 }

    before do
      allow_any_instance_of(TcvCoreApi::Request).to receive(:send).and_return(double(success?: true, status: 200, body: car_stock))
      allow_any_instance_of(CarsController).to receive(:save_browshistry_into_cookies)
    end

    context 'when successful' do
      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
        allow(Search::CarDiscountService).to receive_message_chain(:new, :call).and_return({})
        allow_any_instance_of(Cars::InternalLinks).to receive(:call).and_return([])
        allow_any_instance_of(Solr::FacetService).to receive(:call_facet).and_return({ 'facet_counts' => { 'facet_queries' => {} } })
        get car_detail_path(make: make.vc_name_e, model: model.vc_name_e, car_id: car_id)
      end

      it 'return 200' do
        expect(response).to have_http_status(200)
      end

      it 'render show' do
        expect(response).to render_template('show')
      end
    end

    context 'when 410' do
      before do
        car_stock['terminateDate'] = DateTime.now - 1.month
        allow_any_instance_of(TcvCoreApi::Request).to receive(:send).and_return(double(success?: true, status: 200, body: car_stock))
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
        allow(Search::CarDiscountService).to receive_message_chain(:new, :call).and_return({})
        allow_any_instance_of(Cars::InternalLinks).to receive(:call).and_return([])
        allow_any_instance_of(Solr::FacetService).to receive(:call_facet).and_return({ 'facet_counts' => { 'facet_queries' => {} } })
        get car_detail_path(make: make.vc_name_e, model: model.vc_name_e, car_id: car_id)
      end

      it 'return 410' do
        expect(response).to have_http_status(410)
      end
    end

    context 'when SP successfully' do
      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
        allow(Search::CarDiscountService).to receive_message_chain(:new, :call).and_return({})
        allow_any_instance_of(Cars::InternalLinks).to receive(:call).and_return([])
        allow_any_instance_of(Solr::FacetService).to receive(:call_facet).and_return({ 'facet_counts' => { 'facet_queries' => {} } })
        allow_any_instance_of(CarsController).to receive(:smart_phone?).and_return(true)
        get car_detail_path(make: make.vc_name_e, model: model.vc_name_e, car_id: car_id)
      end

      it 'return 200' do
        expect(response).to have_http_status(200)
      end

      it 'render show' do
        expect(response).to render_template('show')
      end
    end

    context 'when unsuccessful' do
      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
        allow_any_instance_of(TcvCoreApi::Request).to receive(:send).and_return(double(success?: false, status: 404, body: car_stock))
      end

      it 'render 404' do
        get car_detail_path(make: make.vc_name_e, model: model.vc_name_e, car_id: car_id)
        expect(response).to have_http_status(404)
      end
    end

    context 'have http status gone when api return 410' do
      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
        allow_any_instance_of(TcvCoreApi::Request).to receive(:send).and_return(double(success?: false, status: 410, body: car_stock))
      end

      it 'render gone' do
        get car_detail_path(make: make.vc_name_e, model: model.vc_name_e, car_id: car_id)
        expect(response).to have_http_status(410)
      end
    end

    context 'when invalid data' do
      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
      end

      it 'moved permanently' do
        get car_detail_path(make: 'toyota', model: 'toyota', car_id: car_id)
        expect(response).to have_http_status(301)
      end
    end

    context 'get #Catalog' do
      let(:params) { { model_id: model.id, model_year: 2023, trim_name: 'catalog' } }

      before do
        get car_catalog_path(make: make.vc_name_e, model: model.vc_name_e, car_id: car_id), xhr: true, params: params
      end

      it 'return 200' do
        expect(response).to have_http_status(200)
      end

      it 'render catalog' do
        expect(response).to render_template('cars/_catalog')
      end
    end

    context 'get bad request #Catalog' do
      before do
        get car_catalog_path(make: make.vc_name_e, model: model.vc_name_e, car_id: car_id), xhr: true
      end

      it 'bad request' do
        expect(response).to have_http_status(:bad_request)
      end
    end

    context 'get #Seller_info' do
      before do
        allow_any_instance_of(SellerInfoPresenter).to receive(:compose).and_return(seller_response)
        get car_seller_info_path, xhr: true, params: seller_params
      end

      it 'return 200' do
        expect(response).to have_http_status(200)
      end

      it 'return json' do
        expect(response.content_type).to eq('application/json; charset=utf-8')
      end
    end

    context 'get #Seller_info SP' do
      before do
        allow_any_instance_of(SellerInfoPresenter).to receive(:compose).and_return(seller_response)
        allow_any_instance_of(CarsController).to receive(:smart_phone?).and_return(true)
        get car_seller_info_path, xhr: true, params: seller_params
      end

      it 'return 200' do
        expect(response).to have_http_status(200)
      end

      it 'return json' do
        expect(response.content_type).to eq('application/json; charset=utf-8')
      end
    end

    context 'get #inquiries_counter' do
      let(:inquiries_counter_response) { [1, 1] }
      before do
        allow_any_instance_of(Inquiries::OfferCount).to receive(:exec).and_return(inquiries_counter_response)
        get car_inquiries_counter_path, xhr: true, params: { car_id: car_id }
      end

      it 'return 200' do
        expect(response).to have_http_status(200)
      end

      it 'render inquiries_counter' do
        expect(response).to render_template('cars/_inquires_counter')
      end
    end

    context 'get #show_more with no having selling points' do
      let(:car_stock) { { 'vehicleOption' => [] } }
      before do
        allow_any_instance_of(TcvCoreApi::Request).to receive(:send).and_return(double(success?: true, status: 200, body: car_stock))
        get car_show_more_path(make: make, model: model, car_id: car_id), xhr: true, params: { dealer: 'test case', feed: '', award: '' }
      end

      it 'return 200' do
        expect(response).to have_http_status(200)
      end

      it 'renders the show_more partial' do
        expect(response).to render_template('sp/search/cars/_show_more')
      end

      it 'does not include selling points in the response' do
        expect(response.body).not_to include('Selling Points')
      end

      it 'include selling information in the response' do
        expect(response.body).to include('Selling Information')
      end
    end

    context 'get #show_more with do have selling points' do
      let(:car_stock) { { 'vehicleOption' => [45, 38, 39, 42] } }
      before do
        allow_any_instance_of(TcvCoreApi::Request).to receive(:send).and_return(double(success?: true, status: 200, body: car_stock))
        get car_show_more_path(make: make, model: model, car_id: car_id), xhr: true, params: { dealer: 'test case', feed: '', award: '' }
      end

      it 'return 200' do
        expect(response).to have_http_status(200)
      end

      it 'renders the show_more partial' do
        expect(response).to render_template('sp/search/cars/_show_more')
      end

      it 'does not include selling points in the response' do
        expect(response.body).to include('Selling Points')
      end

      it 'contains three li elements with selling points in the specified order' do
        html_doc = Nokogiri::HTML(response.body)
        li_elements = html_doc.css('li')
        expect(li_elements.size).to be >= 3
        expect(li_elements[0].text).to eq('Non-Smoker')
        expect(li_elements[1].text).to eq('One Owner')
        expect(li_elements[2].text).to eq('Maintenance Records Available')
      end

      it 'include selling information in the response' do
        expect(response.body).to include('Selling Information')
      end
    end

    context 'get #car_fetch_modals' do
      let(:country) { FactoryBot.create(:m_countries) }

      before do
        get car_fetch_modals_path, xhr: true
      end

      it 'return 200' do
        expect(response).to have_http_status(200)
      end

      it 'return json' do
        expect(response.content_type).to eq('application/json; charset=utf-8')
      end
    end

    context 'get #car_fetch_recommend_cars' do
      before do
        allow_any_instance_of(Cars::RecommendCars).to receive(:call).and_return(recommend_cars_response)
        get car_fetch_recommend_cars_path, xhr: true
      end

      it 'return 200' do
        expect(response).to have_http_status(200)
      end

      it 'render car_fetch_recommend_cars' do
        expect(response).to render_template('sp/cars/recommend_cars/_index')
      end
    end

    context 'check canonical tag' do
      let(:canonical_link) do
        car_detail_url = car_detail_path(make: make.vc_name_e, model: model.vc_name_e, car_id: car_id)
        get car_detail_url
        Nokogiri::HTML(response.body).at('link[rel="canonical"]')
      end

      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
      end

      it 'has a canonical tag with canonicalStockUrl' do
        car_stock['canonicalStockUrl'] = '/used_car/toyota/c-hr/562186/'
        expect(canonical_link).to be_present
        expect(canonical_link['href']).to eq('http://www.example.com/used_car/toyota/c-hr/562186/')
      end

      it 'has a canonical tag without canonicalStockUrl' do
        car_stock['canonicalStockUrl'] = ''
        expect(canonical_link).to be_present
        expect(canonical_link['href']).to eq("#{car_detail_url}/")
      end
    end
  end
end
