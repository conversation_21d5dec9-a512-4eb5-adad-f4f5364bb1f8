require 'rails_helper'

RSpec.describe EstimatedTotalPrices::CalculatesController, type: :controller do
  describe 'GET #index' do
    let(:params) do
      {
        car_ids: [1, 2, 3],
        country_number: 'US',
        port_id: 1,
        shipping: 1,
        insurance: 1,
        inspection: 1
      }
    end

    context 'with valid params' do
      it 'returns a successful response' do
        get :index, params: params, xhr: true

        expect(response).to have_http_status(:ok)
      end

      it 'renders the json response with formatted total_price_items' do
        get :index, params: params, xhr: true

        total_price_items = Price::EstimateTotal::Calculate.new(*estimate_total_price_params).exec

        expect(JSON.parse(response.body)['total_price_items']).to eq(total_price_items)
      end
    end

    context 'with invalid request' do
      it 'returns a unprocessable entity response' do
        get :index, params: {}

        expect(response).to have_http_status(422)
      end
    end

    private

    def estimate_total_price_params
      [
        params[:car_ids],
        {
          insurance: ActiveModel::Type::Boolean.new.cast(params[:insurance].to_i),
          inspection: ActiveModel::Type::Boolean.new.cast(params[:inspection].to_i),
          shipping: ActiveModel::Type::Boolean.new.cast(params[:shipping].to_i)
        },
        params[:country_number],
        params[:port_id],
        1,
      ]
    end

    def format_data(total_price_items)
      return {} if total_price_items.empty?

      total_price_items['totalPriceItems']
    end
  end
end
