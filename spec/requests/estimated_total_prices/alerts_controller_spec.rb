require 'rails_helper'

RSpec.describe EstimatedTotalPrices::AlertsController, type: :controller do
  describe 'GET #index' do
    context 'with valid country_number parameter' do
      it 'returns a successful response' do
        get :index, params: { country_number: 404 }, xhr: true

        expect(response).to have_http_status(:ok)
      end

      it 'renders the json response with pattern_alert data' do
        get :index, params: { country_number: 404 }, xhr: true
        pattern_alert = Price::EstimateTotal::Alerts.new(404).all
        pattern_alert.transform_keys!(&:to_s)

        expect(JSON.parse(response.body)['pattern_alert']).to eq(pattern_alert)
      end
    end

    context 'with invalid country_number parameter' do
      it 'returns empty {} ' do
        get :index, params: { country_number: 9999 }, xhr: true

        expect(JSON.parse(response.body)['pattern_alert']).to eq({})
      end
    end

    context 'with invalid request' do
      it 'returns a unprocessable entity response' do
        get :index, params: { country_number: 9999 }

        expect(response).to have_http_status(422)
      end
    end
  end
end
