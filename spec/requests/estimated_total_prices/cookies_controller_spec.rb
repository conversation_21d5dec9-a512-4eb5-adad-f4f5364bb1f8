require 'rails_helper'

RSpec.describe EstimatedTotalPrices::CookiesController, type: :controller do
  describe 'PUT #update' do
    include Cookies::Tradetpopts

    context 'with valid params' do
      it 'returns a successful response with updated trade options' do
        put :update, params: {
          country_number: 404,
          port_id: 1,
          shipping: 'true',
          insurance: 'true',
          inspection: 'true'
        }, xhr: true

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['incoterms']).to eq(display_incoterms)
      end

      context 'with just_update_itemdetailshipestimate param' do
        it 'returns a successful response and skips updating trade options' do
          put :update, params: {
            country_number: 'US',
            port_id: 1,
            shipping: 'true',
            insurance: 'true',
            inspection: 'true',
            just_update_itemdetailshipestimate: 'true'
          }, xhr: true

          expect(response).to have_http_status(:ok)
        end
      end
    end

    context 'invalid request' do
      it 'returns a unprocessable entity response' do
        put :update, params: {}

        expect(response).to have_http_status(422)
      end
    end
  end
end
