require 'rails_helper'

RSpec.describe EstimatedTotalPrices::NearestPortsController, type: :controller do
  describe 'GET #index' do
    context 'with valid params' do
      it 'returns a successful response' do
        get :index, params: { country_number: 404 }, xhr: true

        expect(response).to have_http_status(:ok)
      end

      it 'returns a list of ports' do
        get :index, params: { country_number: 404 }, xhr: true

        nearest_ports = Price::EstimateTotal::NearestPorts.new(404).all

        expect(JSON.parse(response.body)['ports']).to eq(nearest_ports)
      end
    end

    context 'with invalid params' do
      it 'returns an empty array' do
        get :index, params: { country_number: 999 }, xhr: true

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['ports']).to eq([])
      end
    end

    context 'invalid request' do
      it 'returns a unprocessable entity response' do
        get :index, params: { country_number: 404 }

        expect(response).to have_http_status(422)
      end
    end
  end
end
