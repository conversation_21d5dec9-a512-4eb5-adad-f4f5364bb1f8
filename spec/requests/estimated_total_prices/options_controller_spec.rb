require 'rails_helper'

RSpec.describe EstimatedTotalPrices::OptionsController, type: :controller do
  describe 'GET #index' do
    context 'with valid params' do
      it 'returns a successful response with cached options and show_asterisk' do
        get :index, params: { country_number: 404 }, xhr: true

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['options']).to eq({ 'shipping' => false, 'insurance' => false, 'pre_ship_inspection' => true })
        expect(JSON.parse(response.body)['show_asterisk']).to be_truthy
      end
    end

    context 'with invalid params' do
      it 'returns a bad request response' do
        get :index, params: { country_number: 'incorrect_number' }, xhr: true

        response_body = { 'options' => { 'shipping' => false, 'insurance' => false, 'pre_ship_inspection' => false }, 'show_asterisk' => nil }

        expect(JSON.parse(response.body)).to eq(response_body)
      end
    end

    context 'invalid request' do
      it 'returns a unprocessable entity response' do
        get :index, params: { country_number: 404 }

        expect(response).to have_http_status(422)
      end
    end
  end
end
