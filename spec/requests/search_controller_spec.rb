require 'rails_helper'

RSpec.describe Search, type: :request do
  let(:total_count) { 319 }
  let(:cars) do
    [[{ 'CountryID' => '702', 'IsBroken' => 0, 'dtUpdated' => '2024-01-30T17:32:00.717Z', 'ImagePath' => '', 'id' => '521638',
        'IsValid' => 1, 'Status' => 0, 'VehicleIdentificationNumber' => '321321', 'ServicePlan' => 1, 'StockID_MP' => 0, 'Make' => 'Chevrolet',
        'UserNumber' => '10000', 'Country' => 'SINGAPORE', 'IsPostedOption' => 0, 'VehicleWidth' => 0, 'MakeID' => 127, 'PriceExchangeRateID' => 1,
        'PriceText' => '0.00', 'DealerComment' => 'シンガポール共和国（シンガポールきょうわこく、英語: Republic of Singapore[4]、マレー語:
 Republik Singapura、簡体字中国語: 新加坡共和国、繁体字中国語: 新加坡共和國、タミル語: சிங்கப்பூர் குடியரசு）、通称シンガポールは、東南アジアに位置し、
シンガポール島及び60以上の小規模な島々からなる島国[5]、都市国家[6][7]で、政体は共和制[8]。 ',
        'URL' => '', 'ColorID' => 0, 'PriceOtherText' => '0.00', 'TrimName' => '', 'VehicleHeight' => 0, 'IsOffer' => true,
        'SortNum' => '2024-01-30T17:15:45.950Z', 'TerminateDate' => '2024-02-29T17:15:45.940Z', 'EndDate' => '2024-02-29T17:15:45.940Z',
        'SteeringID' => 12, 'dtCreated' => '2024-01-30T17:16:00.577Z', 'IsMoneyCollection' => true, 'Model' => 'Aveo', 'ModelYear' => 2019,
        'HangingCount' => 0, 'HangingUsers' => ':', 'MakeModelID' => 1_320_146, 'IsInternational' => 1, 'ManufactureDateM' => 1,
        'IsNoAccidentsHistory' => false, 'DriveTypeID' => 0, 'IsBuyItNow' => false, 'OdometerOption' => 0,
        'Name' => 'test_suzuki_car_pv_test_10000', 'PurchaseCharge' => 2, 'IsDomestic' => false, 'StartDate' => '2024-01-30T17:15:45.940Z',
        'FinalCountries' => ',28,72,90,104,108,180,212,214,308,320,328,388,404,454,508,516,598,643,659,702,716,780,800,826,834,840,894,',
        'CategoryID' => 1, 'ModelNumber' => 'testsuzuki', 'IsRecommendedItem' => true, 'BodyStyle1' => 3, 'BodyStyle2' => 0,
        'EditDate' => '2024-01-30T17:15:45.950Z', 'PortID' => 0, 'Odometer' => 11_111_111, 'ItemID' => 561_993, 'ServiceID' => 300,
        'SpecialPriceStatus' => 0, 'IsAccident' => 0, 'Price' => 0.0, 'IsHanging' => false, 'CreateDateMI' => 15, 'Detail' => '',
        'ModelID' => 50_146, 'TransmissionID' => 5, 'IsNew' => 0, 'FuelTypeID' => 0, 'ModelYearMonth' => 201_903, 'PriceRawText' => '10000.00',
        'ImageCount' => 1, 'CreateDateY' => 2024, 'UserID' => 850_579, 'CreateDateM' => 1, 'CreateDateHH' => 17, 'Body' => ' : Chevrolet
 testsuzuki : 321321 : Hatchback : Automatic : 561993 : SINGAPORE : Pegasus Singapore Parent Company : test_suzuki_car_pv_test_10000 -
PegasusSGP : 10000 : 850579-10000 : シンガポール共和国（シンガポールきょうわこく、英語: Republic of Singapore[4]、マレー語: Republik Singapura、
簡体字中国語: 新加坡共和国、繁体字中国語: 新加坡共和國、タミル語: சிங்கப்பூர் குடியரசு）、通称シンガポールは、東南アジアに位置し、シンガポール島及び60以上の小規
模な島々からなる島国[5]、都市国家[6][7]で、政体は共和制[8]。 ', 'BodyM' => [' : Chevrolet : Aveo : 2019 : testsuzuki : 321321 : Hatchback :
Automatic : 561993 : SINGAPORE : Pegasus Singapore Parent Company : test_suzuki_car_pv_test_10000 - PegasusSGP : 10000 : 850579-10000 :
シンガポール共和国（シンガポールきょうわこく、英語: Republic of Singapore[4]、マレー語: Republik Singapura、簡体字中国語: 新加坡共和国、繁体字中国語:
 新加坡共和國、タミル語: சிங்கப்பூர் குடியரசு）、通称シンガポールは、東南アジアに位置し、シンガポール島及び60以上の小規模な島々からなる島国[5]、都市国家[6][7]で、
政体は共和制[8]。 '], 'DealerName' => 'Pegasus Singapore Parent Company', 'PriceOther' => 0.0, 'CreateDateD' => 30, 'Steering' => 'Right', 'AreaID' => 6,
        'Title' => 'test_suzuki_car_pv_test_10000 - PegasusSGP', 'Displacement' => 11_111, 'Door' => 0, 'IsNewZealand' => 0, 'VehicleLength' => 0,
        'ManufactureDateY' => 2019, 'IsDelete' => false, 'StartDate_15min_Intervals' => '2024-01-30T17:15:00Z',
        'EntryDate' => '2024-01-30T17:15:45.950Z', 'vcHandleName' => 'PegasusSGP', '_version_' => 1_789_592_769_909_489_664, 'FOBPrice' => '-',
        'SellerAwardIconName' => nil, 'Options' => [], 'OptionsForSP' => [11_111_111, 11_111, '-'],
        'path' => '/used_car/chevrolet/aveo/561993/' }], 319, 0]
  end
  let(:condition_internal_links) do
    [{ :condition => [[['prcf=1500&prct=2000', 'US$1500-2000'], 0], [['prcf=2000&prct=2500', 'US$2000-2500'], 0],
                      [['prcf=2500&prct=5000', 'US$2500-5000'], 0]], title: 'Car Price (FOB), often combined' },
     { :condition => [[['dr=10', '4WD'], 0], [['st=13', 'LeftHandDrive'], 0], [['tmns=6', 'Manual'], 0],
                      [['fues=17', 'Diesel'], 0]], title: 'Popular Conditions, often combined' }]
  end

  let(:solr_result) do
    { 'grouped' => { 'MakeID:1 & ModelID:54743' => { 'matches' => 306, 'doclist' => { 'numFound' => 0, 'start' => 0, 'docs' => [] } },
                     'MakeID:2 & ModelID:50034' => { 'matches' => 306, 'doclist' => { 'numFound' => 0, 'start' => 0, 'docs' => [] } },
                     'MakeID:5 & ModelID:61161' => { 'matches' => 306, 'doclist' => { 'numFound' => 0, 'start' => 0, 'docs' => [] } },
                     'MakeID:38 & ModelID:1257' => { 'matches' => 306, 'doclist' => { 'numFound' => 0, 'start' => 0, 'docs' => [] } },
                     'MakeID:5 & ModelID:4483' => { 'matches' => 306, 'doclist' => { 'numFound' => 0, 'start' => 0, 'docs' => [] } } } }
  end

  let(:facet_result) do
    {
      'facet_counts' => {
        'facet_queries' => {
          'Price:[0 TO 500] AND BodyStyle1:1' => 8,
          'Price:[500 TO 1000] AND BodyStyle1:1' => 7,
          'MakeID:1 AND BodyStyle1:1' => 12,
          'MakeID:2 AND BodyStyle1:1' => 8,
          'MakeID:3 AND BodyStyle1:1' => 0,
          'MakeID:5 AND BodyStyle1:1' => 0,
          'MakeID:4 AND BodyStyle1:1' => 12,
          'SteeringID:13 AND BodyStyle1:1' => 0,
          'TransmissionID:6 AND BodyStyle1:1' => 10
        }
      }
    }
  end

  let(:body_style_data) do
    [
      MPrimaryBodyStyle.new(name: 'Bus', primary_body_style_id: 1),
      MPrimaryBodyStyle.new(name: 'Convertible', primary_body_style_id: 2),
      MPrimaryBodyStyle.new(name: 'Machinery', primary_body_style_id: 4),
    ]
  end

  let(:user_country_code) { 404 }
  let(:solr_facet_double) { instance_double(Solr::FacetService) }

  describe 'GET #index' do
    context 'PC mode without param[:pn]' do
      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_price_data).and_return([])
        allow(Search::Cars).to receive_message_chain(:new, :exec).and_return(cars)
        allow_any_instance_of(Solr::FacetService).to receive(:call_facet).and_return({ 'facet_counts' => { 'facet_queries' => {} } })
        allow_any_instance_of(Search::GetPopularForAllMakeService).to receive(:list_all_model).and_return([])
        allow_any_instance_of(Search::GetPopularForAllMakeService).to receive(:list_all_maker).and_return([])
        allow(MasterInfo::InternalLinkAllMakeCondition).to receive(:all).and_return([])
        allow(MasterInfo::InternalLink::BodyStyle).to receive(:all).and_return([])
        allow(MasterInfo::InternalLink::OtherCategory).to receive(:all).and_return([])
        allow(MasterInfo::VehiclesInStock).to receive(:use_in_canonical).and_return([])
        allow_any_instance_of(Search::InternalLinks).to receive(:call).and_return([])
        allow_any_instance_of(Search::InternalLinksAllMake).to receive(:call).and_return([])
        get search_path(make: 'all', model: 'all')
      end

      it 'return 200' do
        expect(response).to have_http_status(200)
      end

      it 'render tempale' do
        expect(response).to render_template(:index)
      end
    end

    context 'PC mode with param[:pn]' do
      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_price_data).and_return([])
        allow(Search::Cars).to receive_message_chain(:new, :exec).and_return(cars)
        allow_any_instance_of(Solr::FacetService).to receive(:call_facet).and_return({ 'facet_counts' => { 'facet_queries' => {} } })
        allow_any_instance_of(Search::GetPopularForAllMakeService).to receive(:list_all_model).and_return([])
        allow_any_instance_of(Search::GetPopularForAllMakeService).to receive(:list_all_maker).and_return([])
        allow(MasterInfo::InternalLinkAllMakeCondition).to receive(:all).and_return([])
        allow(MasterInfo::InternalLink::BodyStyle).to receive(:all).and_return([])
        allow(MasterInfo::InternalLink::OtherCategory).to receive(:all).and_return([])
        allow(MasterInfo::VehiclesInStock).to receive(:use_in_canonical).and_return([])
        allow_any_instance_of(Search::InternalLinks).to receive(:call).and_return([])
        allow_any_instance_of(Search::InternalLinksAllMake).to receive(:call).and_return([])
        get search_path(make: 'all', model: 'all', pn: 1)
      end

      it 'return 200' do
        expect(response).to have_http_status(200)
      end

      it 'render tempale' do
        expect(response).to render_template(:index)
      end

      it 'returns the title and description containing the page number' do
        title = Nokogiri::HTML(response.body).at('title').text
        description = Nokogiri::HTML(response.body).at('meta[name="description"]')['content']
        expect(title).to eq('Search by TCV(former tradecarview) | Japanese used cars for sale(2)')
        expect(description).to include('[tradecarview] became【TCV】. cars for sale. 319 Stock(2) Items.')
      end
    end

    context 'PC mode with invalid param[:pn]' do
      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_price_data).and_return([])
        allow(Search::Cars).to receive_message_chain(:new, :exec).and_return([[], 0, 0])
        allow_any_instance_of(Solr::FacetService).to receive(:call_facet).and_return({ 'facet_counts' => { 'facet_queries' => {} } })
        allow_any_instance_of(Search::GetPopularForAllMakeService).to receive(:list_all_model).and_return([])
        allow_any_instance_of(Search::GetPopularForAllMakeService).to receive(:list_all_maker).and_return([])
        allow(MasterInfo::InternalLinkAllMakeCondition).to receive(:all).and_return([])
        allow(MasterInfo::InternalLink::BodyStyle).to receive(:all).and_return([])
        allow(MasterInfo::InternalLink::OtherCategory).to receive(:all).and_return([])
        allow(MasterInfo::VehiclesInStock).to receive(:use_in_canonical).and_return([])
        allow_any_instance_of(Search::InternalLinks).to receive(:call).and_return([])
        allow_any_instance_of(Search::InternalLinksAllMake).to receive(:call).and_return([])
        get search_path(make: 'all', model: 'all', pn: -1)
      end

      it 'return 404' do
        expect(response).to have_http_status(404)
      end
    end

    context 'SP mode' do
      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_price_data).and_return([])
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data_for_sp).and_return([])
        allow(Search::Cars).to receive_message_chain(:new, :exec).and_return(cars)
        allow_any_instance_of(Solr::FacetService).to receive(:call_facet).and_return({ 'facet_counts' => { 'facet_queries' => {} } })
        allow_any_instance_of(Search::GetPopularForAllMakeService).to receive(:list_all_model).and_return([])
        allow_any_instance_of(Search::GetPopularForAllMakeService).to receive(:list_all_maker).and_return([])
        allow(MasterInfo::InternalLinkAllMakeCondition).to receive(:all).and_return([])
        allow(MasterInfo::InternalLink::BodyStyle).to receive(:all).and_return([])
        allow(MasterInfo::InternalLink::OtherCategory).to receive(:all).and_return([])
        allow(MasterInfo::VehiclesInStock).to receive(:use_in_canonical).and_return([])
        allow_any_instance_of(Search::InternalLinks).to receive(:call).and_return([])
        allow_any_instance_of(Search::InternalLinksAllMake).to receive(:call).and_return([])
        get search_path(make: 'all', model: 'all'), headers: { HTTP_USER_AGENT: TEST_SP_USER_AGENT }
      end

      it 'return 200' do
        expect(response).to have_http_status(200)
      end

      it 'render tempale' do
        expect(response).to render_template(:index)
      end
      it "shows button 'Show more information'" do
        expect(response.body).to include('Show more information')
      end
    end

    shared_context 'common mocks for bsty tests' do
      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :fetch_search_form_body_style).and_return(body_style_data)
        allow(Solr::GroupService).to receive_message_chain(:new, :group_query).and_return(solr_result)
        allow(Search::Cars).to receive_message_chain(:new, :exec).and_return(cars)

        allow(solr_facet_double).to receive(:call_facet).and_return(facet_result)
        allow(Solr::FacetService).to receive(:new).and_return(solr_facet_double)

        allow(MasterInfo::InternalLink::PriceRange).to receive(:all).and_return([
                                                                                  double(value: '[0 TO 500]', text: 'Under US$500',
                                                                                         query: 'prct=500', id: 1),
                                                                                  double(value: '[500 TO 1000]', text: 'US$500-1,000',
                                                                                         query: 'prcf=500&prct=1000', id: 2),
                                                                                ])

        allow(MasterInfo::InternalLink::OtherCategory).to receive(:all).and_return([
                                                                                     double(text: 'Left Hand Drive', url: 'used_car/all/all/?st=13',
                                                                                            query: 'st=13', solr_query: 'SteeringID:13', id: 1),
                                                                                     double(text: 'Manual', url: 'used_car/all/all/?tmns=6',
                                                                                            query: 'tmns=6', solr_query: 'TransmissionID:6', id: 2),
                                                                                   ])
      end
    end

    context 'PC mode with param[:bsty]' do
      include_context 'common mocks for bsty tests'

      before do
        get search_path(make: 'all', model: 'all', bsty: 1)
      end

      it 'returns 200' do
        expect(response).to have_http_status(200)
      end

      it 'renders template' do
        expect(response).to render_template(:index)
      end

      it 'shows internal link body type section' do
        expect(response.body).to include('Make x Bus')
        expect(response.body).to include('/used_car/toyota/all/?bsty=1')
      end
    end

    context 'SP mode with param[:bsty]' do
      include_context 'common mocks for bsty tests'

      before do
        get search_path(make: 'all', model: 'all', bsty: 1), headers: { HTTP_USER_AGENT: TEST_SP_USER_AGENT }
      end

      it 'returns 200' do
        expect(response).to have_http_status(200)
      end

      it 'renders template' do
        expect(response).to render_template(:index)
      end

      it 'shows internal link body type section' do
        expect(response.body).to include('Make x Bus')
        expect(response.body).to include('/used_car/toyota/all/?bsty=1')
      end
    end

    context 'PC mode with invalid param[:bsty]' do
      include_context 'common mocks for bsty tests'

      before do
        get search_path(make: 'all', model: 'all', bsty: 'ab')
      end

      it 'return 404' do
        expect(response).to have_http_status(404)
      end
    end

    context 'PC mode with body type 2 params' do
      include_context 'common mocks for bsty tests'

      before do
        get search_path(make: 'all', model: 'all', bsty: '4.3')
      end

      it 'return 200' do
        expect(response).to have_http_status(200)
      end

      it 'not shows internal link body type section' do
        expect(response.body).not_to include('Make x')
      end
    end
  end

  describe 'GET /advancedsearch' do
    before do
      allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
      get advanced_search_path
    end

    it 'return 200' do
      expect(response).to have_http_status(200)
    end

    it 'render tempale' do
      expect(response).to render_template(:advanced_search)
    end
  end

  describe 'POST search#search_counter' do
    before do
      allow(Search::SearchConditionFilter).to receive_message_chain(:new, :count).and_return('5')
    end

    let(:params) { { search_params: { make: 'toyota', model: 'yaris', tmns: '6' } } }

    before do
      post advancedsearch_search_counter_path, params: params, xhr: true
    end

    it 'return 200' do
      expect(response).to have_http_status(200)
    end

    it 'return the count of search results as JSON' do
      expect(response.headers['Content-Type']).to include('application/json')
    end

    it 'return correct number counted' do
      expect(JSON.parse(response.body)['count']).to eq('5')
    end
  end

  describe 'GET search#search_redirect' do
    let(:params) { { make: 'toyota', model: 'yaris' } }

    before do
      get redirect_path(params)
    end

    it 'return 302' do
      expect(response).to have_http_status(302)
    end

    it 'redirects to the destination URL' do
      redirect_url = search_url(params) << '/' if search_url(params).last != '/'
      expect(response).to redirect_to(redirect_url)
    end
  end

  describe 'POST search#update_per_page' do
    before do
      post update_per_page_path(per: 25), xhr: true
    end

    it 'return 200' do
      expect(response).to have_http_status(200)
    end
  end

  describe 'POST search#change_sort_condition' do
    before do
      post change_sort_condition_path(ptid: 1), xhr: true
    end

    it 'updates the sort condition and returns a successful response' do
      expect(response).to have_http_status(200)
    end
  end

  describe 'GET same_model' do
    context 'Invalid model' do
      let(:make) { FactoryBot.create(:master_make) }
      let(:model) { FactoryBot.create(:master_model) }

      before do
        get same_model_path(make: make.vc_name_e, model: model.vc_name_e, valid_page_param: true), xhr: true
      end

      it 'return 400' do
        expect(response).to have_http_status(400)
      end
    end

    context 'Valid model' do
      before do
        get same_model_path(make: 'toyota', model: 'yaris', valid_page_param: true), xhr: true
      end

      it 'return 200' do
        expect(response).to have_http_status(200)
      end
    end
  end

  describe 'GET search#popular_models' do
    context 'with valid make and model' do
      let(:params) { { make: 'toyota', model: 'yaris', valid_page_param: true } }
      context 'PC mode' do
        before do
          get popular_models_path(params), xhr: true
        end

        it 'renders the popular models block' do
          expect(response).to have_http_status(200)
        end
      end

      context 'SP mode' do
        before do
          get popular_models_path(params), headers: { HTTP_USER_AGENT: TEST_SP_USER_AGENT }, xhr: true
        end

        it 'renders the popular models block' do
          expect(response).to have_http_status(200)
        end
      end
    end

    context 'with invalid make or model' do
      before do
        get popular_models_path(make: 'invalid_make', model: 'invalid_model', valid_page_param: true), xhr: true
      end

      it 'returns a bad request status' do
        expect(response).to have_http_status(400)
      end
    end
  end

  describe 'GET search#favorite_cars' do
    context 'User signed in' do
      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
        allow_any_instance_of(ApplicationController).to receive(:user_signed_in?).and_return(true)
        allow_any_instance_of(FavoritesControllable).to receive(:favorite_add_or_remove).and_return(double(success?: true))
      end

      it 'returns the favorite car IDs as JSON' do
        get favorite_cars_path, params: { car_ids: [1, 2, 3].to_json }, xhr: true

        expect(response).to have_http_status(200)
        expect(response.headers['Content-Type']).to include('application/json')
        expect(JSON.parse(response.body)).to eq({ 'favorite_car_ids' => [] })
      end
    end

    context 'Invalid car_ids' do
      it 'returns empty favorite_car_ids' do
        get favorite_cars_path, params: { car_ids: 'invalid' }, xhr: true

        expect(response).to have_http_status(200)
        expect(JSON.parse(response.body)).to eq({ 'favorite_car_ids' => [] })
      end
    end
  end

  describe 'GET search#description' do
    context 'when make is not valid' do
      it 'returns a bad request response' do
        allow_any_instance_of(ApplicationController).to receive(:make_valid?).and_return(false)

        get description_path(make: 'invalid_make', model: 'invalid_model'), xhr: true

        expect(response).to have_http_status(:bad_request)
      end
    end

    context 'when make is valid' do
      it 'renders the description' do
        get description_path(make: 'toyota', model: '86'), xhr: true

        expect(assigns(:catalog_title)).to be_present
        expect(assigns(:catalog_description)).to be_present
        expect(response).to render_template('search/_description')
        expect(response).to have_http_status(:ok)
      end
    end

    context 'when make is valid and model is valid' do
      it 'renders the description' do
        get description_path(make: 'toyota', model: 'vitz'), xhr: true

        expect(assigns(:catalog_title)).to be_present
        expect(assigns(:catalog_description)).to be_present
        expect(response).to render_template('search/_description')
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'GET /fetch_partial_user_review' do
    context 'when make and model are not valid' do
      it 'returns a no content response' do
        get fetch_partial_user_review_path(make: 'invalid', model: 'invalid'), xhr: true

        expect(response).to have_http_status(:no_content)
        expect(response.body).to eq('')
      end
    end

    context 'when make and model are valid' do
      it 'renders the user_reviews partial template' do
        get fetch_partial_user_review_path(make: 'toyota', model: '86'), xhr: true

        expect(response).to render_template('search/_user_reviews')
        expect(response).to have_http_status(:ok)
        expect(assigns(:user_reviews)).to be_present
      end
    end
  end

  describe 'GET /inquiries_cars' do
    let(:car_ids) { [1, 2, 3] }
    let(:inquiries_counter_data) { { '1' => 3, '2' => 1 } }

    context 'when car_ids parameter is valid' do
      before do
        allow_any_instance_of(Inquiries::OfferCount).to receive(:exec).and_return(inquiries_counter_data)
      end

      it 'returns the inquiries counter for the specified car_ids' do
        get '/ajax_v2/inquiries_cars', params: { car_ids: car_ids.to_json }, xhr: true

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq({ 'inquiries_counter' => inquiries_counter_data })
      end
    end

    context 'when car_ids parameter is invalid' do
      it 'returns an empty inquiries counter' do
        get '/ajax_v2/inquiries_cars', params: { car_ids: 'invalid_json' }, xhr: true

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq({ 'inquiries_counter' => {} })
      end
    end
  end

  describe 'GET /fetch_modals' do
    context 'when the request is an XMLHttpRequest (XHR)' do
      before do
        allow_any_instance_of(ApplicationController).to receive(:user_country_code).and_return(704)
        allow_any_instance_of(ActionController::Base).to receive(:render_to_string).and_return('rendered_modal')
      end

      it 'returns the rendered modals as JSON' do
        get '/ajax_v2/search_fetch_modals', xhr: true

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq({
                                                  'modal_signin' => 'rendered_modal',
                                                  'modal_signup' => 'rendered_modal',
                                                  'modal_contact_seller' => 'rendered_modal'
                                                })
      end
    end

    context 'when the request is not an XMLHttpRequest (XHR)' do
      it 'redirects to the root path' do
        get '/ajax_v2/search_fetch_modals'

        expect(response).to redirect_to(root_path)
      end
    end
  end
end
