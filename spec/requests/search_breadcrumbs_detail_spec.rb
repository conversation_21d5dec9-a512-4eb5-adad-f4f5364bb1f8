require 'rails_helper'

RSpec.describe 'SearchBreadcrumbs Detail Tests', type: :request do
  describe 'SearchController with SearchBreadcrumbs concern' do
    let(:total_count) { 100 }
    let(:cars) { [[], 0, 0] }

    before do
      # Mock SearchController dependencies
      allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return([])
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return([])
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return([])
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_price_data).and_return([])
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data_for_sp).and_return([])
      allow(Search::Cars).to receive_message_chain(:new, :exec).and_return(cars)
      allow_any_instance_of(Solr::FacetService).to receive(:call_facet).and_return({ 'facet_counts' => { 'facet_queries' => {} } })
      allow_any_instance_of(Search::GetPopularForAllMakeService).to receive(:list_all_model).and_return([])
      allow_any_instance_of(Search::GetPopularForAllMakeService).to receive(:list_all_maker).and_return([])
      allow(MasterInfo::InternalLinkAllMakeCondition).to receive(:all).and_return([])
      allow(MasterInfo::InternalLink::BodyStyle).to receive(:all).and_return([])
      allow(MasterInfo::InternalLink::OtherCategory).to receive(:all).and_return([])
      allow(MasterInfo::VehiclesInStock).to receive(:use_in_canonical).and_return([])
      allow_any_instance_of(Search::InternalLinks).to receive(:call).and_return([])
      allow_any_instance_of(Search::InternalLinksAllMake).to receive(:call).and_return([])
    end

    context 'helper method integration in views' do
      it 'allows search_path_with_trailing_slash to be used in views' do
        get search_path(make: 'all', model: 'all')
        expect(response).to have_http_status(200)

        # Verify controller has the method
        expect(controller).to respond_to(:search_path_with_trailing_slash)
      end
    end

    context 'method exposure as helper' do
      it 'exposes method for view context usage' do
        get search_path(make: 'all', model: 'all')

        # Test helper method accessibility in views
        view_context = controller.view_context
        expect(view_context).to respond_to(:search_path_with_trailing_slash)
      end
    end
  end

  describe 'CarsController with SearchBreadcrumbs concern' do
    context 'method availability verification' do
      it 'includes search_path_with_trailing_slash method' do
        expect(CarsController.instance_methods).to include(:search_path_with_trailing_slash)
      end
    end

    context 'helper method accessibility' do
      it 'exposes search_path_with_trailing_slash as helper method' do
        expect(CarsController.new).to respond_to(:search_path_with_trailing_slash)
      end
    end
  end
end
