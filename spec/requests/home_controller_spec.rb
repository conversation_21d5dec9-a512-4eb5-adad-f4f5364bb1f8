require 'rails_helper'

RSpec.describe HomeController, type: :request do
  let(:fake_response) do
    [{ 'CountryID' => '392', 'IsBroken' => 0, 'dtUpdated' => '2024-01-23T19:24:01.920Z', 'CountTotal' => 18,
       'CountHalfYear' => 0, 'ImagePath' => '', 'id' => '521521', 'IsValid' => 1, 'Status' => 11,
       'VehicleIdentificationNumber' => '123', 'ServicePlan' => 1, 'StockID_MP' => 0, 'Make' => 'BMW', 'UserNumber' => '240',
       'Country' => 'JAPAN', 'IsPostedOption' => 0, 'VehicleWidth' => 1860, 'MakeID' => 23, 'PriceExchangeRateID' => 1,
       'PriceText' => '8000.00', 'DealerComment' => '成約課金でCarviewにお\r\n輸出業者です。', 'URL' => '', 'OfferAverage' => 0,
       'ColorID' => 27, 'PriceOtherText' => '8000.00', 'FeedbackTotal' => 4.8, 'TrimName' => '', 'VehicleHeight' => 1470,
       'FeedbackOneYear' => 0.0, 'IsOffer' => true, 'SortNum' => '2024-01-23T19:05:57.9', 'TerminateDate' => '2024-02-22T19:05:57.940Z',
       'EndDate' => '2024-02-22T19:05:57.940Z', 'SteeringID' => 12, 'dtCreated' => '2024-01-23T19:08:04.873Z', 'IsMoneyCollection' => true,
       'Model' => '5 Series', 'ModelYear' => 2020, 'HangingCount' => 0, 'HangingUsers' => ':', 'YesCountTotal' => 14, 'MakeModelID' => 230_426,
       'IsInternational' => 1, 'ManufactureDateM' => 5, 'IsNoAccidentsHistory' => false, 'DriveTypeID' => 10, 'IsBuyItNow' => false,
       'OdometerOption' => 0, 'Name' => 'Wondeful Vehicle7-7', 'PurchaseCharge' => 2, 'IsDomestic' => false,
       'StartDate' => '2024-01-23T19:05:57.940Z', 'FinalCountries' => ',28,72,90,104,108,180,212,214,308,320,328,388,404,454,508,516,598',
       'Sort' => 1, 'CategoryID' => 1, 'ModelNumber' => 'XG20', 'IsRecommendedItem' => true, 'BodyStyle1' => 11,
       'BodyStyle2' => 0, 'FeedbackHalfYear' => 0.0, 'EditDate' => '2024-01-23T19:07:25.780Z', 'PortID' => 0,
       'Odometer' => 10_000, 'ItemID' => 561_865, 'ServiceID' => 300, 'SpecialPriceStatus' => 0, 'IsAccident' => 0,
       'Price' => 8000.0, 'IsHanging' => false, 'CreateDateMI' => 5, 'Detail' => 'Must see', 'ModelID' => 426,
       'TransmissionID' => 5, 'IsNew' => 0, 'FuelTypeID' => 20, 'ModelYearMonth' => 202_005, 'PriceRawText' => '8000.00',
       'ImageCount' => 1, 'CreateDateY' => 2024, 'UserID' => 837_000, 'CreateDateM' => 1, 'CreateDateHH' => 19,
       'OfferCount' => 0, 'YesCountOneYear' => 0, 'Body' => '輸出業者です。 : CD Player:',
       'BodyM' => ['ewにお金を払います。\r\n輸出業者ですe' => '成約課金株式会社', 'PriceOther' => 8000.0,
                   'CreateDateD' => 23, 'Steering' => 'Right', 'AreaID' => 5, 'Title' => 'Wondeful Vehicle7-7 - akm001 huga23',
                   'Displacement' => 3000, 'Door' => 4, 'IsNewZealand' => 0, 'VehicleLength' => 4920,
                   'ManufacY' => 2020, 'CountOneYear' => 0, 'IsDelete' => false,
                   'StartDate_15min_Intervals' => '2024-01-23T19:00:00Z',
                   'EntryDate' => '2024-01-23T19:05:57.937Z', 'vcHandleName' => 'akm001 huga23',
                   '_version_' => 1_789_114_934_466_969_601, 'FOBPrice' => 'US$8,000',
                   'SellerAwardIconName' => 'b_BestSeller_2021', 'Options' => %w[RHD Gasoline 4WD AT],
                   'OptionsForSP' => [10_000, 3000, '4WD', 'AT'], 'path' => '/used_car/bmw/5%20series/561865/'] }]
  end
  let(:total_count) { 319 }
  let(:top_car_rank_presenter) do
    [[{ model: '86', make: 'Toyota', count: '-', top_models: [] },
      { model: 'Fairlady', make: 'Nissan', count: '-', top_models: [] },
      { model: 'CX-8', make: 'Mazda', count: '-', top_models: [] }],
     [{ model: 'Alfa Romeo Others', make: 'Alfa Romeo', count: '-', top_models: [] },
      { model: 'Atenza', make: 'Mazda', count: '-', top_models: [] },
      { model: 'Serena', make: 'Nissan', count: '-', top_models: [] },
      { model: 'AQUA', make: 'Toyota', count: 9, top_models: [] },
      { model: 'Hilux Surf', make: 'Toyota', count: '-', top_models: [] },
      { model: 'Citroen Triomphe', make: 'Dongfeng Peugeot Citroen Automobile',
        count: '-', top_models: [] }, { model: 'Aerio', make: 'Suzuki', count: '-', top_models: [] }]]
  end
  let(:browse_by_maker_data) do
    [{ name: 'Make 1', svg_name: 'maker-1', stock: 92 },
     { name: 'Make 2', svg_name: 'maker-2', stock: 4 },
     { name: 'Make 3', svg_name: 'maker-3', stock: 17 }]
  end
  let(:browse_by_price) do
    [{ range: 'Under $500', stock: 10, url: 'used_car/all/all/?prct=500' },
     { range: '$500 - $1,000', stock: 1, url: 'used_car/all/all/?prcf=500&prct=1000' },
     { range: '$1,000 - $1,500', stock: 7, url: 'used_car/all/all/?prcf=1000&prct=1500' },
     { range: '$1,500 - $2,000', stock: 69, url: 'used_car/all/all/?prcf=1500&prct=2000' },
     { range: '$2,000 - $2,500', stock: 5, url: 'used_car/all/all/?prcf=2000&prct=2500' },
     { range: '$2,500 - $5,000', stock: 49, url: 'used_car/all/all/?prcf=2500&prct=5000' },
     { range: '$5,000 - $10,000', stock: 191, url: 'used_car/all/all/?prcf=5000&prct=10000' },
     { range: '$10,000 - $20,000', stock: 135, url: 'used_car/all/all/?prcf=10000&prct=20000' },
     { range: 'Over $20,000', stock: 8, url: 'used_car/all/all/?prcf=20000' }]
  end

  describe 'GET #INDEX' do
    context 'when request is from PC' do
      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
        get root_path
      end

      it 'return 200' do
        expect(response).to have_http_status(200)
      end

      it 'renders the :index view' do
        expect(response).to render_template('index')
      end
    end

    context 'when request is from SP' do
      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return([])
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data_for_sp).and_return(browse_by_maker_data)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_price_data).and_return(browse_by_price)

        headers = { 'HTTP_USER_AGENT' => TEST_SP_USER_AGENT }
        get root_path, headers: headers
      end

      it 'return 200' do
        expect(response).to have_http_status(200)
      end

      it 'renders the :index view' do
        expect(response).to render_template('index')
      end
    end
  end

  describe 'GET #LOCAL' do
    before do
      allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
    end

    it 'return 200 Kenya' do
      get local_page_path(region: 'Kenya')
      expect(response).to have_http_status(200)
      expect(response).to render_template('index')
    end

    it 'return 200 Zambia' do
      get local_page_path(region: 'Zambia')
      expect(response).to have_http_status(200)
      expect(response).to render_template('index')
    end

    it 'return 200 UnitedStates' do
      allow_any_instance_of(Cars::MatchingByCountry).to receive(:call).and_return(fake_response)
      get local_page_path(region: 'UnitedStates')
      expect(response).to have_http_status(200)
      expect(response).to render_template('index')
    end
  end

  describe 'GET /browsing_history' do
    ids = [561_865]

    context 'when request is from PC' do
      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
        get root_path
        allow_any_instance_of(HomeController).to receive(:session).and_return(session)
      end

      it 'return 200 pc' do
        allow(Cars::CarsBrowshistry).to receive(:new).and_return(double('fake_browsing_history', call: fake_response))
        allow_any_instance_of(Cars::CarsBrowshistry).to receive(:call).and_return(fake_response)
        get browsing_history_path, headers: { 'HTTP_COOKIE' => "browshistry=#{ids}" }, xhr: true
        expect(response).to have_http_status(200)
      end

      it 'return 200 SP' do
        allow(Cars::CarsBrowshistry).to receive(:new).and_return(double('fake_browsing_history', call: fake_response))
        allow_any_instance_of(Cars::CarsBrowshistry).to receive(:call).and_return(fake_response)
        get browsing_history_path, headers: { 'HTTP_USER_AGENT' => TEST_SP_USER_AGENT, 'HTTP_COOKIE' => "browshistry=#{ids}" }, xhr: true

        expect(response).to have_http_status(200)
      end
    end
  end

  describe 'GET /new_arrival_cars' do
    context 'when request is from PC/SP' do
      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
        get root_path
        allow_any_instance_of(HomeController).to receive(:session).and_return(session)
      end

      it 'return 200' do
        allow(Cars::NewArrival).to receive(:new).and_return(double('fake_browsing_history', call: fake_response))
        allow_any_instance_of(Cars::NewArrival).to receive(:call).and_return(fake_response)
        get new_arrival_cars_path, xhr: true
        expect(response).to have_http_status(200)
      end

      it 'return 200 for SP' do
        allow(Cars::NewArrival).to receive(:new).and_return(double('fake_browsing_history', call: fake_response))
        allow_any_instance_of(Cars::NewArrival).to receive(:call).and_return(fake_response)
        get new_arrival_cars_path, headers: { 'HTTP_USER_AGENT' => TEST_SP_USER_AGENT }, xhr: true
        expect(response).to have_http_status(200)
      end
    end
  end

  describe 'GET /fetch_car_matching_by_country' do
    before do
      allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
      get local_page_path(region: 'UnitedStates')
      allow_any_instance_of(HomeController).to receive(:session).and_return(session)
    end

    it 'return 200 for SP' do
      allow(Cars::NewArrival).to receive(:new).and_return(double('fake_browsing_history', call: fake_response))
      allow_any_instance_of(Cars::MatchingByCountry).to receive(:call).and_return(fake_response)
      get fetch_car_matching_by_country_path, xhr: true, headers: { 'HTTP_USER_AGENT' => TEST_SP_USER_AGENT }
      expect(response).to have_http_status(200)
    end
  end

  describe 'GET /fetch_car_ranking' do
    before do
      allow_any_instance_of(CarRankPresenter).to receive(:tops).and_return(top_car_rank_presenter)
      get fetch_car_ranking_path, xhr: true
    end

    it 'return 200' do
      expect(response).to have_http_status(200)
    end
  end

  describe 'GET /fetch_popular_ranking' do
    before do
      get fetch_popular_ranking_path, xhr: true
    end

    it 'return 200' do
      expect(response).to have_http_status(200)
    end
  end

  describe 'GET /fetch_local_infomation' do
    before do
      get fetch_local_infomation_path, xhr: true
    end

    it 'return 200' do
      expect(response).to have_http_status(200)
    end
  end

  describe 'GET /fetch_testimonials' do
    before do
      get fetch_testimonials_path, xhr: true
    end

    it 'return 200' do
      expect(response).to have_http_status(200)
    end
  end
end
