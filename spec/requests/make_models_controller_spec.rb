require 'rails_helper'

RSpec.describe MakeModelsController, type: :request do
  let(:total_count) { 319 }

  describe 'GET #index' do
    context 'when maker is present' do
      let(:maker) { create(:master_make) }
      let(:models) do
        [{ name: 'Model A', stock: 10 }, { name: 'Model B', stock: 15 },
         { name: 'A', stock: 10 }, { name: 'D', stock: 10 },
         { name: 'G', stock: 10 }, { name: 'J', stock: 10 },
         { name: 'P', stock: 10 }, { name: 'S', stock: 10 },
         { name: 'V', stock: 10 }, { name: 'Y', stock: 10 },
         { name: '123', stock: 10 }]
      end

      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
        allow(MasterMake).to receive(:get_by_vc_name_e).and_return(maker)
      end

      it 'assigns variables and renders the index template' do
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :models_by_maker_id).and_return(models)

        get model_list_path(maker_name: maker.vc_name_e)

        expect(assigns(:maker_id)).to eq(maker.id_make)
        expect(assigns(:maker_name)).to eq(maker.vc_name_e)
        expect(assigns(:models)).to eq(models)
        expect(assigns(:model_groups)).to be_present
        expect(assigns(:total_count)).to eq(115)
        expect(assigns(:err_message)).to be_nil
        expect(assigns(:h1_top_content)).to be_present
        expect(response).to render_template('index')
      end

      it 'assigns an error message when total count is zero' do
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :models_by_maker_id).and_return([])

        get model_list_path(maker_name: maker.vc_name_e)

        expect(assigns(:err_message)).to eq('0 results found for selected model.')
        expect(response).to render_template('index')
      end

      it 'assigns an error message when total count is zero SP device' do
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :models_by_maker_id).and_return([])
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data_for_sp).and_return(models)

        get model_list_path(maker_name: maker.vc_name_e), headers: { 'HTTP_USER_AGENT' => TEST_SP_USER_AGENT }

        expect(assigns(:err_message)).to eq('0 results found for selected model.')
        expect(response).to render_template('sp/make_models/index')
      end
    end

    context 'when maker is not present' do
      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
      end

      it 'assigns an error message and renders the index template' do
        allow(MasterMake).to receive(:get_by_vc_name_e).and_return(nil)

        get model_list_path(maker_name: 'not_available_maker')

        expect(assigns(:err_message)).to eq('No models found for selected Make.')
        expect(response).to render_template('index')
      end
    end
  end

  describe 'GET #fetch_popular_rankings' do
    context 'when maker is present' do
      let(:maker) { create(:master_make) }
      let(:models) { [{ id: 1, name: 'Model A' }, { id: 2, name: 'Model B' }] }
      let(:popular_rankings) { { show: false } }

      before do
        allow(MasterMake).to receive(:get_by_vc_name_e).and_return(maker)
        allow_any_instance_of(DataLoader::CarCounterService).to receive(:models_by_maker_id).and_return(models)
        allow(MakeModels::PopularRankings).to receive_message_chain(:new, :data).and_return(popular_rankings)
      end

      it 'returns popular rankings and renders the partial template' do
        get make_model_rankings_path(maker_name: maker.vc_name_e), xhr: true

        expect(response).to render_template(partial: 'sp/make_models/_popular_rankings')
        expect(response).to have_http_status(:ok)
      end
    end

    context 'when maker is not present' do
      it 'returns no content' do
        allow(MasterMake).to receive(:get_by_vc_name_e).and_return(nil)

        get make_model_rankings_path(maker_name: 'not_available_maker'), xhr: true

        expect(response).to have_http_status(:no_content)
      end
    end

    context 'when request is not xhr' do
      let(:maker) { create(:master_make) }
      it 'return unprocessable entity' do
        get make_model_rankings_path(maker_name: maker.vc_name_e)

        expect(response).to have_http_status(422)
      end
    end
  end
end
