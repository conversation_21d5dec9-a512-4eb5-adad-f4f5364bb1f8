require 'rails_helper'

RSpec.describe BackordersController, type: :request do
  describe 'backorder' do
    let(:backorder_params) do
      {
        make: 1, model: 52_691, fid: 2024, budget: 1000, bsty: 2,
        country: 704, name: 'tester', phone: '0123456789', whatsapp: 0,
        description: 'sample', ecls: 36, mileage: 40_000, utm_source: '',
        utm_medium: '', utm_campaign: '', preposition: 1, lowest_price: 1000
      }
    end
    let(:total_count) { 319 }

    context 'GET /backorder' do
      before do
        allow_any_instance_of(BackordersController).to receive(:require_user_signed_in)
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
        get backorder_path
      end

      it 'return 200' do
        expect(response).to have_http_status(200)
      end

      it 'render new' do
        expect(response).to render_template('new')
      end
    end

    context 'GET /backorder2' do
      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
        get backorder2_path
      end

      it 'return 200' do
        expect(response).to have_http_status(200)
      end

      it 'render new' do
        expect(response).to render_template('new')
      end
    end

    context 'fetch from data' do
      before do
        get backorders_data_path, xhr: true
      end

      it '200' do
        expect(response).to have_http_status(200)
        expect(response.content_type).to eq 'application/json; charset=utf-8'
      end
    end

    context 'fetch lowest price' do
      before do
        get backorder_lowest_price_path, xhr: true
      end

      it '200' do
        expect(response).to have_http_status(200)
      end

      it 'render lowest price' do
        expect(response).to render_template('shared/backorder/_lowest_price')
      end
    end

    context 'create backorder' do
      let(:success_response) do
        { success: true, body: '{"contactID":2329,"contactDetailID":4357}', status: 200 }
      end

      it '200' do
        allow(Backorders::CreateBackorder).to receive(:new).and_return(double(call: success_response))
        post backorders_create_path, params: backorder_params, xhr: true
        expect(response).to have_http_status(:ok)
      end

      it 'error' do
        post backorders_create_path, params: backorder_params, xhr: true
        expect(response).to have_http_status(401)
      end

      it 'returns a status code of 408 when there is a Faraday error' do
        allow(Backorders::CreateBackorder).to receive(:new).and_raise(Faraday::Error)
        post backorders_create_path, params: backorder_params, xhr: true
        expect(response).to have_http_status(:request_timeout)
      end

      it 'returns a status code of 500 when there is a generic error' do
        allow(Backorders::CreateBackorder).to receive(:new).and_raise(StandardError)
        post backorders_create_path, params: backorder_params, xhr: true
        expect(response).to have_http_status(:internal_server_error)
      end
    end

    context 'thanks' do
      before do
        allow_any_instance_of(BackordersController).to receive(:require_user_signed_in)
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
      end

      it 'return 200 backorder2_thanks_path' do
        get backorder_thanks_path
        expect(response).to have_http_status(200)
        expect(response).to render_template('thanks')
      end

      it 'return 200 backorder2_thanks_path' do
        get backorder2_thanks_path
        expect(response).to have_http_status(200)
        expect(response).to render_template('thanks')
      end
    end
  end
end
