require 'rails_helper'

RSpec.describe <PERSON><PERSON><PERSON>roller, type: :request do
  let(:total_count) { 319 }
  let(:all_makes) { [{ name: 'Make 1', stock: 92 }, { name: 'Make 2', stock: 4 }, { name: 'Make 3', stock: 17 }] }

  describe 'GET #index' do
    before do
      allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :all_makes).and_return(all_makes)
    end

    it 'assigns variables and renders the index template' do
      get all_makes_path

      expect(assigns(:make_groups)).to be_present
      expect(assigns(:h1_top_content)).to eq('TCV(former tradecarview)|Japanese best quality online used cars for sale')
      expect(response).to render_template('sp/makes/index')
    end
  end
end
