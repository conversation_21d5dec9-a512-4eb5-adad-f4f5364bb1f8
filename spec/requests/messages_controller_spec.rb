require 'rails_helper'

RSpec.describe MessagesController, type: :request do
  describe 'GET #notify_counter' do
    context 'when user is signed in' do
      let(:unread_message_count) { 5 }
      let(:response_body) { { 'unreadMessageCount' => unread_message_count } }

      before do
        allow_any_instance_of(MessagesController).to receive(:user_signed_in?).and_return(true)
      end

      context 'when the API request is successful' do
        it 'returns the unread message count in the response' do
          allow_any_instance_of(TcvCoreApi::Request).to receive(:send).and_return(instance_double(TcvCoreApi::Response, success?: true,
                                                                                                                        body: response_body))

          get '/ajax_v2/messages/notify_counter', xhr: true

          expect(response).to have_http_status(:ok)
          expect(response.body).to eq({ unread_msg: unread_message_count }.to_json)
        end
      end

      context 'when the API request fails' do
        it 'returns 0 as the unread message count in the response' do
          get '/ajax_v2/messages/notify_counter', xhr: true

          expect(response).to have_http_status(:ok)
          expect(response.body).to eq({ unread_msg: 0 }.to_json)
        end
      end
    end

    context 'when user is not signed in' do
      it 'returns 0 as the unread message count in the response' do
        allow_any_instance_of(MessagesController).to receive(:user_signed_in?).and_return(false)

        get '/ajax_v2/messages/notify_counter', xhr: true

        expect(response).to have_http_status(:ok)
        expect(response.body).to eq({ unread_msg: 0 }.to_json)
      end
    end
  end
end
