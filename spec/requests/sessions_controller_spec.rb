require 'rails_helper'

RSpec.describe SessionsController, type: :request do
  let(:total_count) { 319 }

  describe 'GET #simple_loginregist' do
    let(:car_id) { '123456' }
    let(:cookies) { { 'CVPD' => 'cvpd' } }

    context 'when user is signed in' do
      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
        allow_any_instance_of(described_class).to receive(:user_signed_in?).and_return(true)
      end

      it 'redirects to root path' do
        get simple_loginregist_path
        expect(response).to redirect_to(root_path)
      end
    end

    context 'when user is not signed in' do
      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
        allow_any_instance_of(described_class).to receive(:user_signed_in?).and_return(false)
      end

      it 'set atrribute_params for backorder' do
        get simple_loginregist_path(type: 'backorder')

        expect(assigns(:attribute_params)).to be_present
      end

      it 'set atributes params for offer' do
        get simple_loginregist_path

        expect(assigns(:attribute_params)).to be_present
      end
    end

    context 'when car response is not successful' do
      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
        allow_any_instance_of(described_class).to receive(:cookies).and_return(cookies)
        allow_any_instance_of(TcvCoreApi::Request).to receive(:send).and_return(double(success?: false))
      end

      it 'does not set the car_presenter' do
        get simple_loginregist_path(id: car_id)
        expect(assigns(:car_presenter)).to be_nil
      end

      it 'does not set the seller_info' do
        get simple_loginregist_path(id: car_id)
        expect(assigns(:seller_info)).to be_nil
      end
    end

    context 'when car response in successful' do
      before do
        allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
        allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
        allow_any_instance_of(TcvCoreApi::Response).to receive(:success?).and_return(true)
        allow_any_instance_of(TcvCoreApi::Response).to receive(:body).and_return({})
        allow_any_instance_of(described_class).to receive(:fetch_car).with(car_id).and_call_original
        # in order not to render sessions/components/thumb_car beacause it makes the request not terminated
        allow_any_instance_of(CarStockPresenter).to receive(:present?).and_return(false)
      end

      it 'creat CarStockPresenter instance' do
        get simple_loginregist_path(id: car_id)

        expect(assigns(:car_presenter)).to be_a(CarStockPresenter)
        expect(assigns(:seller_info)).to be_a(Hash)
      end
    end
  end
end
