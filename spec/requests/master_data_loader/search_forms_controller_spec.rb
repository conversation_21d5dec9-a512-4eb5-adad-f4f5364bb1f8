require 'rails_helper'

RSpec.describe MasterDataLoader::SearchFormsController, type: :request do
  describe 'search form' do
    let(:controller_instance) { described_class.new }

    before do
      allow(controller_instance).to receive(:car_counter_instance).and_return(car_counter_instance)
      allow(controller_instance).to receive(:smart_phone?).and_return(true)
      allow(Search::MasterDataService).to receive(:master_data).and_return(master_data)
    end

    context 'when car_counter_instance returns valid data' do
      let(:car_counter_instance) { 'car_counter_instance' }
      let(:master_data) do
        {
          regis_year: [['2020', 1]], regis_month: [['January', 2]], mileage_options: [['Low', 3]],
          fob_options_data: [['5000', 4]], engine_capacity_options_data: [['2000', 5]],
          any_door_options: [['2 Doors', 6]], driver_type_options: [['Automatic', 7]],
          maker_options_data: [['Toyota', 8]], model_options_data: [['Camry', 9]],
          accident_options: [['Yes', 10]], steering_options: [['Power Steering', 11]],
          color_options: [['Red', 12]], transmission_options: [['Automatic', 13]],
          fuel_type_options: [['Gasoline', 14]], body_style_options: [['Sedan', 15]],
          secondary_body_style_options: [['Convertible', 16]]
        }
      end

      it 'formats data correctly' do
        expected_result = {
          regis_year_from: [['From', 0], ['2020', 1]],
          regis_year_to: [['To', 0], ['2020', 1]],
          regis_month_from: [['From', 0], ['January', 2]],
          regis_month_to: [['To', 0], ['January', 2]],
          mileage_options_from: [['From', 0], ['Low', 3]],
          mileage_options_to: [['To', 0], ['Low', 3]],
          fob_options_min: [['Min', 0], ['5000', 4]],
          fob_options_max: [['Max', 0], ['5000', 4]],
          engine_options_from: [['From', 0], ['2000', 5]],
          engine_options_to: [['To', 0], ['2000', 5]],
          any_door_options: [['Any Door', 0], ['2 Doors', 6]],
          driver_type_options: [['Any', 0], ['Automatic', 7]],
          maker_options: [['Any Make', 0], ['Toyota', 8]],
          model_options: [['Camry', 9]],
          accident_options: [['Choose Accident Car', 0], ['Yes', 10]],
          steering_options: [['Any', 0], ['Power Steering', 11]],
          color_options: [['Choose Exterior Color', 0], ['Red', 12]],
          transmission_options: [['Choose Transmission', 0], ['Automatic', 13]],
          fuel_type_options: [['Choose  Fuel Type', 0], ['Gasoline', 14]],
          body_style_options: [['Choose BodyStyle', 0], ['Sedan', 15]],
          secondary_body_style_options: [['Convertible', 16]]
        }

        expect(controller_instance.send(:data_formater)).to eq(expected_result)
      end

      it 'return success' do
        get '/ajax_v2/init_search_form_data', xhr: true
        expect(response.content_type).to eq 'application/json; charset=utf-8'
        expect(response).to have_http_status(:success)
      end
    end
  end
end
