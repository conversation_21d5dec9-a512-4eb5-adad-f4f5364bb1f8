require 'rails_helper'

RSpec.describe PromotionsController, type: :request do
  describe 'GET #check_affiliate' do
    context 'when whitelist_path? is false' do
      let(:current_path) { '/false_path' }

      before do
        allow_any_instance_of(PromotionsController).to receive(:params).and_return(current_path: current_path)
      end

      it 'does not set the affiliate cookie and returns show_affiliate as false in the response' do
        get '/ajax_v2/promotions/check_affiliate', xhr: true

        expect(response).to have_http_status(:ok)
        expect(response.body).to eq({ show_affiliate: false }.to_json)
        expect(cookies[:affiliate]).to be_nil
      end
    end

    context 'when check_condition is false' do
      let(:current_path) { '/' }
      let(:cookies) { {} }
      let(:current_user) { nil }

      before do
        allow_any_instance_of(PromotionsController).to receive(:params).and_return(current_path: current_path)
      end

      it 'does not set the affiliate cookie and returns show_affiliate as false in the response' do
        get '/ajax_v2/promotions/check_affiliate', xhr: true

        expect(response).to have_http_status(:ok)
        expect(response.body).to eq({ show_affiliate: false }.to_json)
        expect(cookies[:affiliate]).to be_nil
      end
    end

    context 'when current_user is present and country_has_ability_affiliate? is true' do
      let(:current_path) { '/' }
      let(:cookies) { {} }
      let(:current_user) { { 'userServices' => [123] } }

      before do
        allow_any_instance_of(PromotionsController).to receive(:params).and_return(current_path: current_path)
        allow_any_instance_of(PromotionsController).to receive(:cookies).and_return(cookies)
        allow_any_instance_of(PromotionsController).to receive(:current_user).and_return(current_user)
        allow_any_instance_of(PromotionsController).to receive(:country_has_ability_affiliate?).and_return(true)
      end

      it 'does not set the affiliate cookie and returns show_affiliate as false in the response' do
        get '/ajax_v2/promotions/check_affiliate', xhr: true

        expect(response).to have_http_status(:ok)
        expect(response.body).to eq({ show_affiliate: true }.to_json)
        expect(cookies[:affiliate]).to be_present
      end
    end
  end

  context 'when current_user is present and include seller code' do
    let(:current_path) { '/' }
    let(:cookies) { {} }
    let(:current_user) { { 'userServices' => [320] } }

    before do
      allow_any_instance_of(PromotionsController).to receive(:params).and_return(current_path: current_path)
      allow_any_instance_of(PromotionsController).to receive(:cookies).and_return(cookies)
      allow_any_instance_of(PromotionsController).to receive(:current_user).and_return(current_user)
      allow_any_instance_of(PromotionsController).to receive(:country_has_ability_affiliate?).and_return(true)
    end

    it 'does not set the affiliate cookie and returns show_affiliate as false in the response' do
      get '/ajax_v2/promotions/check_affiliate', xhr: true

      expect(response).to have_http_status(:ok)
      expect(response.body).to eq({ show_affiliate: false }.to_json)
      expect(cookies[:affiliate]).to be_nil
    end
  end
end
