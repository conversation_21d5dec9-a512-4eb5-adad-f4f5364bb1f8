require 'rails_helper'

RSpec.describe CurrenciesController, type: :request do
  describe 'POST #create' do
    let(:currency) { 'JPY' }

    before do
      cookies[:tcvcookies] = 'exrate='
      post currencies_path, params: { currency: currency }
    end

    it 'return 200' do
      expect(response).to have_http_status(:ok)
    end

    it 'cookies exrate = JPY' do
      expect(cookies[:tcvcookies]).to include("exrate=#{currency}")
    end
  end
end
