require 'rails_helper'

FGKEY = 'fgkey=10c43fd0-2dfa-4e33-84fa-79b6a6bc322a'.freeze

RSpec.describe Cars::FavouritesController, type: :request do
  let(:id_test) { rand(10_000..99_999) }

  describe 'POST DELETE #register_favorite' do
    context 'when user signed in' do
      before do
        allow_any_instance_of(ApplicationController).to receive(:user_signed_in?).and_return(true)
        allow_any_instance_of(FavoritesControllable).to receive(:favorite_add_or_remove).and_return(double(success?: true))
      end

      it 'add favorite' do
        post "/ajax_v2/register_favorite/#{id_test}"
        expect(response).to have_http_status(:success)
        expect(response.content_type).to eq 'application/json; charset=utf-8'
        expect(JSON.parse(response.body)).to eq({ 'success' => true })
      end

      it 'remove favorite' do
        delete "/ajax_v2/register_favorite/#{id_test}"
        expect(response).to have_http_status(:success)
        expect(response.content_type).to eq 'application/json; charset=utf-8'
        expect(JSON.parse(response.body)).to eq({ 'success' => true })
      end
    end

    context 'when smart_phone' do
      let(:headers) do
        { 'HTTP_USER_AGENT' => TEST_SP_USER_AGENT, 'HTTP_COOKIE' => FGKEY }
      end

      it 'add favorite' do
        post "/ajax_v2/register_favorite/#{id_test}", headers: headers
        expect(response).to have_http_status(:success)
        expect(response.content_type).to eq 'application/json; charset=utf-8'
        expect(JSON.parse(response.body)).to eq({ 'success' => true })
      end

      it 'remove favorite' do
        delete "/ajax_v2/register_favorite/#{id_test}", headers: headers
        expect(response).to have_http_status(:success)
        expect(response.content_type).to eq 'application/json; charset=utf-8'
        expect(JSON.parse(response.body)).to eq({ 'success' => true })
      end
    end

    context 'when occur error' do
      it 'add favorite' do
        post "/ajax_v2/register_favorite/#{id_test}", headers: { 'HTTP_USER_AGENT' => TEST_SP_USER_AGENT }
        expect(response).to have_http_status(:success)
        expect(response.content_type).to eq 'application/json; charset=utf-8'
        expect(JSON.parse(response.body)).to eq({ 'success' => false })
      end

      it 'remove favorite' do
        delete "/ajax_v2/register_favorite/#{id_test}", headers: { 'HTTP_USER_AGENT' => TEST_SP_USER_AGENT }
        expect(response).to have_http_status(:success)
        expect(response.content_type).to eq 'application/json; charset=utf-8'
        expect(JSON.parse(response.body)).to eq({ 'success' => false })
      end
    end

    context 'when occur exception' do
      it 'add favorite' do
        post "/ajax_v2/register_favorite/#{id_test}"
        expect(response).to have_http_status(:success)
        expect(response.content_type).to eq 'application/json; charset=utf-8'
        expect(JSON.parse(response.body)).to eq({ 'success' => false, 'error' => true })
      end

      it 'remove favorite' do
        delete "/ajax_v2/register_favorite/#{id_test}"
        expect(response).to have_http_status(:success)
        expect(response.content_type).to eq 'application/json; charset=utf-8'
        expect(JSON.parse(response.body)).to eq({ 'success' => false, 'error' => true })
      end
    end
  end
end
