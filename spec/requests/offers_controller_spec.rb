require 'rails_helper'

RSpec.describe Offers, type: :request do
  let(:total_count) { 319 }

  describe 'GET /offer/thankyou' do
    let(:make) { FactoryBot.create(:master_make) }
    let(:model) { FactoryBot.create(:master_model) }
    let(:sid) { 72_234 }
    let(:pa_seller_id) { 3_147_874 } # Pegasus Auto seller
    let(:other_seller_id) { 850_102 }
    let(:car_stock) do
      {
        'itemId' => 561_440,
        'name' => '2078',
        'detail' => '',
        'makeId' => make.id,
        'modelId' => model.id,
        'firstRegistrationDate' => 1.month.ago
      }
    end

    before do
      allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
      get offers_thank_you_path(makeId: make.id, modelId: model.id, itemId: car_stock['itemId'], sellerId: other_seller_id, sid: sid)
    end

    it 'return 200' do
      expect(response).to have_http_status(200)
    end

    it 'Chek title on html response' do
      expect(response.body).to include("#{make.vc_name_e} #{model.vc_name_e} [TCV]")
    end

    it 'assigns correct values to variables' do
      expect(assigns(:title)).to eq("#{make.vc_name_e} #{model.vc_name_e} [TCV]")
      expect(assigns(:car_detail_url)).to eq(car_detail_url(make: make.vc_name_e, model: model.vc_name_e, car_id: car_stock['itemId']))
    end

    it 'render thank_you' do
      expect(response).to render_template('thank_you')
    end

    shared_examples 'has no Contact on Whatsapp' do
      specify do
        get offers_thank_you_path(makeId: make.id, modelId: model.id, itemId: car_stock['itemId'], sellerId: seller_id, sid: sid)
        expect(response.body).to_not include('Contact on Whatsapp')
      end
    end

    context 'when user signed in' do
      let(:current_user) do
        {
          'userId' => 853_664,
          'userAccountID' => 'abb120038',
          'userHandleName' => 'abb120038',
          'email' => '<EMAIL>',
          'userServices' => [],
          'suppotersSignUpStatus' => 0,
          'userCountryNumber' => 4,
          'authentication' => {
            'cvpd' => 'i=853664&u=dp44JLF%2fQKfiX99atE9LQg%3d%3d&uh=abb120038&te=2024%2f01%2'
          }
        }
      end

      before do
        allow_any_instance_of(OffersController).to receive(:user_signed_in?).and_return(true)
        allow_any_instance_of(OffersController).to receive(:current_user).and_return(current_user)
      end

      context 'when seller is Pegasus Auto' do
        let(:seller_id) { pa_seller_id }

        it 'has Contact on Whatsapp' do
          get offers_thank_you_path(makeId: make.id, modelId: model.id, itemId: car_stock['itemId'], sellerId: seller_id, sid: sid)
          expect(response.body).to include('Contact on Whatsapp')
        end
      end

      context 'when seller is not Pegasus Auto' do
        let(:seller_id) { other_seller_id }

        include_examples 'has no Contact on Whatsapp'
      end
    end

    context 'when user not signed in' do
      context 'when seller is Pegasus Auto' do
        let(:seller_id) { pa_seller_id }

        include_examples 'has no Contact on Whatsapp'
      end

      context 'when seller is not Pegasus Auto' do
        let(:seller_id) { other_seller_id }

        include_examples 'has no Contact on Whatsapp'
      end
    end
  end
end
