require 'rails_helper'

RSpec.describe LandingPagesController, type: :request do
  let(:total_count) { 319 }

  describe 'GET listing/l24' do
    before do
      allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
      get kenya_landing_page_path
    end

    it 'return 200' do
      expect(response).to have_http_status(200)
    end

    it 'Chek description on html response' do
      expect(response.body).to include('Split your payment into up to 60 installments!')
    end

    it 'Check money promotion on html response' do
      expect(response.body).to include('Balance payable in full or in installments!')
    end

    it 'Check title variable' do
      expect(response.body).to include('Japanese used cars / about BNPL(buy now pay later) [ TCV ]')
    end

    it 'render kenya' do
      expect(response).to render_template('kenya')
    end
  end

  describe 'GET listing/l25' do
    before do
      allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
      get zambia_landing_page_path
    end

    it 'return 200' do
      expect(response).to have_http_status(200)
    end

    it 'Check money promotion on html response' do
      expect(response.body).to include('Pay the rest of 50% and receive the vehicle')
    end

    it 'Check description on html response' do
      expect(response.body).to include('Pay 50% C&amp;F price, clear the balance when your car arrives in Dar es Salaam!')
    end

    it 'Check title variable' do
      expect(response.body).to include('Japanese used cars / about BNPL(buy now pay later) [ TCV ]')
    end

    it 'render zambia' do
      expect(response).to render_template('zambia')
    end
  end

  describe 'GET listing/l26' do
    before do
      allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)
      get backorder_landing_page_path
    end

    it 'return 200' do
      expect(response).to have_http_status(200)
    end

    it 'Chek title on html response' do
      expect(response.body).to include('Welcome to TCV Back Order')
    end

    it 'Check title variable' do
      expect(response.body).to include('Japanese used cars / about Backorder [ TCV ]')
    end

    it 'render backorder' do
      expect(response).to render_template('backorder')
    end
  end
end
