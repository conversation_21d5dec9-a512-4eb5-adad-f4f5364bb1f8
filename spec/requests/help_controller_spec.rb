require 'rails_helper'

RSpec.describe HelpController, type: :request do
  let(:total_count) { 319 }

  describe 'GET /help/importguide/' do
    before do
      allow(Solr::Base).to receive_message_chain(:new, :count).and_return(total_count)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_maker_data).and_return(RSPEC_MAKER_DATA)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_body_style_data).and_return(RSPEC_BODY_STYLE_DATA)
      allow(DataLoader::CarCounterService).to receive_message_chain(:new, :process_category_data).and_return(RSPEC_CATEGORY_DATA)

      get import_guide_page_path
    end

    it 'returns 200' do
      expect(response).to have_http_status(200)
    end

    it 'renders the import guide template' do
      expect(response).to render_template('help/import_guide')
    end

    it 'includes benefits content in the response' do
      expect(response.body).to include('Extensive Inventory')
    end

    it 'includes imports content in the response' do
      expect(response.body).to include('Vehicle Pickup at Port')
    end

    it 'includes documents content in the response' do
      expect(response.body).to include('Japan Export Certificate')
    end

    it 'includes strategies content in the response' do
      expect(response.body).to include('Vehicle Purchase Costs')
    end

    it 'includes checklist content in the response' do
      expect(response.body).to include('Safety Standards')
    end

    it 'includes cost payment content in the response' do
      expect(response.body).to include('The purchase price of a vehicle is only a fraction')
    end

    it 'includes logistics content in the response' do
      expect(response.body).to include('Vehicle Transportation: Optimal Methods and Logistics')
    end
  end
end
