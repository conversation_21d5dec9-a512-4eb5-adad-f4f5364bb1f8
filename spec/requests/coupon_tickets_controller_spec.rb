require 'rails_helper'

RSpec.describe CouponTicketsController, type: :request do
  describe 'POST #create' do
    context 'when successful' do
      it 'return 200' do
        allow_any_instance_of(Precoupons::CreatePrecoupon).to receive(:call).and_return({ success: true })
        post precoupon_register_path
        expect(response).to have_http_status(:ok)
      end
    end

    context 'when unsuccessful' do
      it 'return 400' do
        allow_any_instance_of(Precoupons::CreatePrecoupon).to receive(:call).and_return({ success: false })
        post precoupon_register_path
        expect(response).to have_http_status(:bad_request)
      end
    end
  end
end
