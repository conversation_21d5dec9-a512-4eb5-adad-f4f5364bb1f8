require 'rails_helper'

RSpec.describe 'SearchBreadcrumbs concern', type: :request do
  describe '#search_path_with_trailing_slash' do
    let(:controller) { SearchController.new }

    before do
      # Mock search_path method
      allow(controller).to receive(:search_path) do |params|
        make = params[:make] || 'all'
        model = params[:model] || 'all'
        base_path = "/used_car/#{make}/#{model}"

        # Add query parameters if present
        query_params = []
        query_params << "prcf=#{params[:prcf]}" if params[:prcf]
        query_params << "prct=#{params[:prct]}" if params[:prct]
        query_params << "fid=#{params[:fid]}" if params[:fid]
        query_params << "jid=#{params[:jid]}" if params[:jid]
        query_params << "smo=#{params[:smo]}" if params[:smo]
        query_params << "emo=#{params[:emo]}" if params[:emo]
        query_params << "bsty=#{params[:bsty]}" if params[:bsty]

        query_string = query_params.any? ? "?#{query_params.join('&')}" : ''
        "#{base_path}#{query_string}"
      end
    end

    context 'when URL has no query parameters' do
      it 'adds trailing slash to the end of path' do
        result = controller.search_path_with_trailing_slash(make: 'toyota', model: 'yaris')
        expect(result).to eq('/used_car/toyota/yaris/')
      end

      it 'adds trailing slash for make = all' do
        result = controller.search_path_with_trailing_slash(make: 'all', model: 'all')
        expect(result).to eq('/used_car/all/all/')
      end
    end

    context 'when URL has query parameters' do
      it 'adds trailing slash and preserves query parameters' do
        result = controller.search_path_with_trailing_slash(
          make: 'honda',
          model: 'civic',
          prcf: 1000,
          prct: 2000,
        )
        expect(result).to eq('/used_car/honda/civic/?prcf=1000&prct=2000')
      end

      it 'handles multiple query parameters correctly' do
        result = controller.search_path_with_trailing_slash(
          make: 'toyota',
          model: 'camry',
          fid: 2020,
          jid: 2022,
          smo: 1,
          emo: 12,
        )
        expect(result).to eq('/used_car/toyota/camry/?fid=2020&jid=2022&smo=1&emo=12')
      end

      it 'handles body style parameter correctly' do
        result = controller.search_path_with_trailing_slash(
          make: 'nissan',
          model: 'all',
          bsty: 1,
        )
        expect(result).to eq('/used_car/nissan/all/?bsty=1')
      end
    end

    context 'edge cases' do
      it 'handles path that already has trailing slash' do
        # Mock returns path with existing trailing slash
        allow(controller).to receive(:search_path).and_return('/used_car/toyota/yaris/')

        result = controller.search_path_with_trailing_slash(make: 'toyota', model: 'yaris')
        expect(result).to eq('/used_car/toyota/yaris//')
      end

      it 'handles model name with special characters' do
        result = controller.search_path_with_trailing_slash(
          make: 'mazda',
          model: 'rx-7',
        )
        expect(result).to eq('/used_car/mazda/rx-7/')
      end

      it 'handles empty params' do
        result = controller.search_path_with_trailing_slash({})
        expect(result).to eq('/used_car/all/all/')
      end

      it 'handles query parameters without make/model' do
        result = controller.search_path_with_trailing_slash(prcf: 1000)
        expect(result).to eq('/used_car/all/all/?prcf=1000')
      end
    end

    context 'URI parsing validation' do
      it 'parses URI with query string correctly' do
        # Mock for URI parsing test
        allow(controller).to receive(:search_path).and_return('/used_car/honda/civic?prcf=1000&prct=2000')

        result = controller.search_path_with_trailing_slash(make: 'honda', model: 'civic')
        expect(result).to eq('/used_car/honda/civic/?prcf=1000&prct=2000')
      end

      it 'handles empty query string correctly' do
        allow(controller).to receive(:search_path).and_return('/used_car/toyota/yaris?')

        result = controller.search_path_with_trailing_slash(make: 'toyota', model: 'yaris')
        expect(result).to eq('/used_car/toyota/yaris/')
      end
    end
  end

  describe 'helper method availability' do
    it 'exposes search_path_with_trailing_slash as helper method' do
      expect(SearchController.instance_methods).to include(:search_path_with_trailing_slash)
    end
  end
end
