require 'rails_helper'

RSpec.describe UrlGenerator::RequestDesiredVehicles, type: :service do
  describe '#exec' do
    let(:total_count) { 4 }
    let(:queries) { { 'make' => 'Toyota', 'fid' => '2010', 'jid' => '2020', 'tmns' => '5', 'ecls' => '28' } }
    let(:user_not_logged_in) { true }
    let(:is_buyer_user) { true }
    let(:subject) { described_class.new(total_count, queries, user_not_logged_in, is_buyer_user) }

    context 'when user is not logged in or is a buyer user, 0 < total_count <= 4, queries present' do
      it 'generates a URL' do
        expect(subject.exec).to include('/smart/contacts/inquiryform.aspx?')
      end
    end

    context 'when user is logged in and not buyer user' do
      let(:user_not_logged_in) { false }
      let(:is_buyer_user) { false }

      it 'generates a URL' do
        expect(subject.exec).to be_nil
      end
    end

    context 'when total count is zero or more than minimum total car' do
      let(:total_count) { 0 }
      let(:subject) { described_class.new(total_count, queries, user_not_logged_in, is_buyer_user) }

      it 'does not generate a URL' do
        expect(subject.exec).to be_nil
      end
    end

    context 'when queries are blank' do
      let(:queries) { {} }

      it 'does not generate a URL' do
        expect(subject.exec).to be_nil
      end
    end

    context 'when expected params are nil' do
      let(:queries) { { 'model' => 'xl7', 'fues' => '15' } }

      it 'does not generate a URL' do
        expect(subject.exec).to be_nil
      end
    end

    context 'when make id is not found' do
      before do
        allow(MasterMake).to receive(:get_by_vc_name_e).and_return(nil)
      end

      it 'also generate a URL' do
        expect(subject.exec).not_to be_nil
      end
    end
  end
end
