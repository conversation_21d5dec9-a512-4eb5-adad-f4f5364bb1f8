require 'rails_helper'

RSpec.describe Solr::FacetService, type: :service do
  let(:core_number) { 0 }
  let(:solr_url) { Settings.solr.search.send("tcv#{core_number}") }
  let(:open_timeout) { Settings.solr.open_timeout }
  let(:read_timeout) { Settings.solr.read_timeout }

  subject { described_class.new(core_number) }

  describe '#initialize' do
    it 'initializes RSolr with correct parameters' do
      expect(RSolr).to receive(:connect).with(url: solr_url, open_timeout: open_timeout, read_timeout: read_timeout)
      subject
    end
  end

  describe '#call_facet' do
    let(:args) do
      {
        field_name: 'field_name',
        queries: %w[query1 query],
        group_field_name: 'group_field_name',
        query: '*:*',
        row: 0,
        limit: 100
      }
    end

    let(:expected_params) do
      {
        wt: 'json',
        rows: args[:row],
        q: args[:query],
        'facet.limit' => args[:limit],
        'facet.query' => args[:queries],
        'facet.field' => args[:field_name],
        'facet.pivot' => args[:group_field_name],
        'facet' => 'on'
      }
    end

    it 'calls the solr select endpoint with correct parameters' do
      expect(subject.instance_variable_get(:@solr)).to receive(:get).with('select', params: expected_params)
      subject.call_facet(**args)
    end
  end
end
