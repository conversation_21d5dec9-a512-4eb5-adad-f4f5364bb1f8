require 'rails_helper'

RSpec.describe Solr::Base, type: :model do
  let(:solr_base) { Solr::Base.new }

  describe '#initialize' do
    it 'initializes with default core_number' do
      expect(solr_base.instance_variable_get(:@core_number)).to eq(0)
    end

    it 'initializes with provided core_number' do
      solr_base = Solr::Base.new(0)
      expect(solr_base.instance_variable_get(:@core_number)).to eq(0)
    end
  end

  describe '#find' do
    context 'when solr find is successful' do
      before do
        allow(solr_base.instance_variable_get(:@solr)).to receive(:find).and_return('response')
      end

      it 'returns the response' do
        expect(solr_base.find).to eq('response')
      end
    end

    context 'when solr find raises an Http error' do
      before do
        allow(solr_base.instance_variable_get(:@solr)).to receive(:find).and_raise(RSolr::Error::Http.new({}, {}))
      end

      it 'returns a default response' do
        expect(solr_base.find).to eq({ 'response' => { 'numFound' => 0, 'start' => 0, 'docs' => [] } })
      end
    end

    context 'when solr find raises a ConnectionRefused error' do
      before do
        allow(solr_base.instance_variable_get(:@solr)).to receive(:find).and_raise(RSolr::Error::ConnectionRefused)
      end

      it 'returns a default response' do
        expect(solr_base.find).to eq({ 'response' => { 'numFound' => 0, 'start' => 0, 'docs' => [] } })
      end
    end
  end

  describe '#count' do
    context 'when solr count is successful' do
      before do
        allow(solr_base.instance_variable_get(:@solr)).to receive(:count).and_return('10')
      end

      it 'returns the count as integer' do
        expect(solr_base.count).to eq(10)
      end
    end

    context 'when solr count returns blank' do
      before do
        allow(solr_base.instance_variable_get(:@solr)).to receive(:count).and_return('')
      end

      it 'returns 0' do
        expect(solr_base.count).to eq(0)
      end
    end
  end

  describe '#set_default_params' do
    it 'sets default params when none are provided' do
      solr_base.set_default_params
      params = solr_base.instance_variable_get(:@params)
      # start = per_page * (page - 1)
      expect(params[:start]).to eq(0)
      expect(params[:rows]).to eq(DEFAULT_PERPAGE_SOLR)
    end

    it 'sets provided params' do
      solr_base.set_default_params(page: 3, per_page: 20)
      params = solr_base.instance_variable_get(:@params)
      # start = per_page * (page - 1)
      expect(params[:start]).to eq(40)
      expect(params[:rows]).to eq(20)
    end
  end
end
