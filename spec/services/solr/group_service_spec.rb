require 'rails_helper'

RSpec.describe Solr::GroupService, type: :service do
  let(:core_number) { 0 }
  let(:subject) { described_class.new(core_number) }

  describe '#initialize' do
    it 'initializes with default core_number' do
      expect(subject.instance_variable_get(:@core_number)).to eq(core_number)
    end

    it 'establishes a connection to Solr' do
      expect(subject.instance_variable_get(:@solr)).to be_a(RSolr::Client)
    end
  end

  describe '#group_query' do
    let(:args) { { field_list: 'field1,field2', queries: 'query1,query2', query: 'query', row: 1, limit: 1, sort: 'asc' } }

    context 'when all arguments are provided' do
      let(:expected_params) do
        {
          wt: 'json',
          rows: args[:row],
          q: args[:query],
          fl: args[:field_list],
          'group' => 'on',
          'group.query' => args[:queries],
          'group.limit' => args[:limit],
          'group.sort' => args[:sort]
        }
      end

      it 'calls the solr select endpoint with correct parameters' do
        expect(subject.instance_variable_get(:@solr)).to receive(:get).with('select', params: expected_params)
        subject.group_query(**args)
      end
    end

    context 'when an error occurs' do
      it 'returns an empty grouped hash' do
        allow(subject.instance_variable_get(:@solr)).to receive(:get).and_raise(StandardError)
        expect(subject.group_query(**args)).to eq({ 'grouped' => {} })
      end
    end
  end
end
