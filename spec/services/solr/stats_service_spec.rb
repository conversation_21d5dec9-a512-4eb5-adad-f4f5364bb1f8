require 'rails_helper'

RSpec.describe Solr::StatsService do
  let(:query) { '*:*' }
  let(:wt) { 'json' }
  let(:rows) { 0 }
  let(:limit) { nil }
  let(:stats_field) { nil }
  let(:core_number) { 0 }
  let(:url) { Settings.solr.search.send("tcv#{core_number}") }

  subject { described_class.new(query: query, wt: wt, rows: rows, limit: limit, stats_field: stats_field, core_number: core_number) }

  describe '#initialize' do
    it 'initializes with correct parameters' do
      expect(subject.instance_variable_get(:@q)).to eq(query)
      expect(subject.instance_variable_get(:@wt)).to eq(wt)
      expect(subject.instance_variable_get(:@rows)).to eq(rows)
      expect(subject.instance_variable_get(:@limit)).to eq(limit)
      expect(subject.instance_variable_get(:@stats_field)).to eq(stats_field)
      expect(subject.instance_variable_get(:@core_number)).to eq(core_number)
      expect(subject.instance_variable_get(:@url)).to eq(url)
    end
  end

  describe '#call' do
    it 'calls the solr service with correct parameters' do
      expect(subject.instance_variable_get(:@solr)).to receive(:get).with(
        'select',
        params: {
          q: query,
          wt: wt,
          rows: rows,
          limit: limit,
          stats: true,
          'stats.field': stats_field
        },
      )

      subject.call
    end

    it 'returns the response from the solr service' do
      response = { 'response' => 'test' }
      allow(subject.instance_variable_get(:@solr)).to receive(:get).and_return(response)
      expect(subject.call).to eq(response)
    end
  end
end
