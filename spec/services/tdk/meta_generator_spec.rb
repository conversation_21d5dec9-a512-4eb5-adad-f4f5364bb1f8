require 'rails_helper'

RSpec.describe Tdk::MetaGenerator do
  let(:search_service) do
    Search::SearchConditionFilter.new(
      {
        'controller' => 'search',
        'action' => 'index',
        'make' => 'toyota',
        'bsty' => '1',
        'prcf' => '500',
        'prct' => '1000'
      }, 0
    )
  end
  let(:searcher) { Tdk::Searcher.new(search_service, 0) }
  let(:car_count) { 150 }
  let(:page_num) { '(Page 2)' }
  let(:generator) { described_class.new(searcher, car_count: car_count, page_num: page_num) }

  before do
    allow(MasterMake).to receive(:get_by_vc_name_e).with('toyota').and_return(
      double(vc_name_e: 'TOYOTA'),
    )
  end

  describe '#initialize' do
    it 'sets instance variables correctly' do
      expect(generator.instance_variable_get(:@searcher)).to eq(searcher)
      expect(generator.instance_variable_get(:@car_count)).to eq(car_count)
      expect(generator.instance_variable_get(:@page_num)).to eq(page_num)
    end
  end

  describe '#generate_meta_info' do
    context 'when body style price range combination is applicable' do
      let(:search_service) do
        Search::SearchConditionFilter.new(
          {
            'controller' => 'search',
            'action' => 'index',
            'bsty' => '1',
            'prcf' => '500',
            'prct' => '1000'
          }, 0
        )
      end
      let(:searcher) { Tdk::Searcher.new(search_service, 0) }

      before do
        allow(searcher).to receive(:body_style_name).and_return('Bus')
      end

      it 'returns meta info from body style price range combination' do
        result = generator.generate_meta_info

        expect(result).to include(
          title: 'Used Bus US$500-US$1,000 imports for sale (Page 2) at TCV (formerly trade carview)',
          description: include('Browse 150 high-quality Bus US$500-US$1,000 (Page 2) on TCV'),
          h1: 'Used Bus US$500-US$1,000 imports for sale (Page 2)',
        )
      end
    end

    context 'when body style category combination is applicable' do
      let(:search_service) do
        Search::SearchConditionFilter.new(
          {
            'controller' => 'search',
            'action' => 'index',
            'bsty' => '1',
            'st' => '13'
          }, 0
        )
      end
      let(:searcher) { Tdk::Searcher.new(search_service, 0) }

      before do
        allow(searcher).to receive(:body_style_name).and_return('Bus')
      end

      it 'returns meta info from body style category combination' do
        result = generator.generate_meta_info

        expect(result).to include(
          title: 'Used Bus Left Handle Only imports for sale (Page 2) at TCV (formerly trade carview)',
          description: include('Browse 150 high-quality Bus Left Handle Only (Page 2) on TCV'),
          h1: 'Used Bus Left Handle Only imports for sale (Page 2)',
        )
      end
    end

    context 'when price range category combination is applicable' do
      let(:search_service) do
        Search::SearchConditionFilter.new(
          {
            'controller' => 'search',
            'action' => 'index',
            'st' => '13',
            'prcf' => '500',
            'prct' => '1000'
          }, 0
        )
      end
      let(:searcher) { Tdk::Searcher.new(search_service, 0) }

      it 'returns meta info from price range category combination' do
        result = generator.generate_meta_info

        expect(result).to include(
          title: 'Used Left Handle Only US$500-US$1,000 imports for sale (Page 2) at TCV (formerly trade carview)',
          description: include('Browse 150 high-quality Left Handle Only US$500-US$1,000 (Page 2) on TCV'),
          h1: 'Used Left Handle Only US$500-US$1,000 imports for sale (Page 2)',
        )
      end
    end

    context 'when make body style price range combination is applicable' do
      let(:search_service) do
        Search::SearchConditionFilter.new(
          {
            'controller' => 'search',
            'action' => 'index',
            'make' => 'toyota',
            'bsty' => '1',
            'prcf' => '500',
            'prct' => '1000'
          }, 0
        )
      end
      let(:searcher) { Tdk::Searcher.new(search_service, 0) }

      before do
        allow(searcher).to receive(:body_style_name).and_return('Bus')
      end

      it 'returns meta info from make body style price range combination' do
        result = generator.generate_meta_info

        expect(result).to include(
          title: 'Used TOYOTA Bus US$500-US$1,000 imports for sale (Page 2) at TCV (formerly trade carview)',
          description: include('Browse 150 high-quality TOYOTA Bus US$500-US$1,000 (Page 2) on TCV'),
          h1: 'Used TOYOTA Bus US$500-US$1,000 imports for sale (Page 2)',
        )
      end
    end

    context 'when make body style category combination is applicable' do
      let(:search_service) do
        Search::SearchConditionFilter.new(
          {
            'controller' => 'search',
            'action' => 'index',
            'make' => 'toyota',
            'bsty' => '1',
            'st' => '13'
          }, 0
        )
      end
      let(:searcher) { Tdk::Searcher.new(search_service, 0) }

      before do
        allow(searcher).to receive(:body_style_name).and_return('Bus')
      end

      it 'returns meta info from make body style category combination' do
        result = generator.generate_meta_info

        expect(result).to include(
          title: 'Used TOYOTA Bus Left Handle Only imports for sale (Page 2) at TCV (formerly trade carview)',
          description: include('Browse 150 high-quality TOYOTA Bus Left Handle Only (Page 2) on TCV'),
          h1: 'Used TOYOTA Bus Left Handle Only imports for sale (Page 2)',
        )
      end
    end

    context 'when make category price range combination is applicable' do
      let(:search_service) do
        Search::SearchConditionFilter.new(
          {
            'controller' => 'search',
            'action' => 'index',
            'make' => 'toyota',
            'st' => '13',
            'prcf' => '500',
            'prct' => '1000'
          }, 0
        )
      end
      let(:searcher) { Tdk::Searcher.new(search_service, 0) }

      it 'returns meta info from make category price range combination' do
        result = generator.generate_meta_info

        expect(result).to include(
          title: 'Used TOYOTA Left Handle Only US$500-US$1,000 imports for sale (Page 2) at TCV (formerly trade carview)',
          description: include('Browse 150 high-quality TOYOTA Left Handle Only US$500-US$1,000 (Page 2) on TCV'),
          h1: 'Used TOYOTA Left Handle Only US$500-US$1,000 imports for sale (Page 2)',
        )
      end
    end

    context 'when no combination is applicable' do
      let(:search_service) do
        Search::SearchConditionFilter.new(
          {
            'controller' => 'search',
            'action' => 'index',
            'make' => 'toyota',
            'model' => 'yaris'
          }, 0
        )
      end
      let(:searcher) { Tdk::Searcher.new(search_service, 0) }

      it 'returns nil' do
        result = generator.generate_meta_info

        expect(result).to be_nil
      end
    end
  end
end
