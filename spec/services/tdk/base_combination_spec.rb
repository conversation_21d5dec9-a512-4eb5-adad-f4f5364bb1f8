require 'rails_helper'

RSpec.describe Tdk::BaseCombination do
  let(:search_service) do
    Search::SearchConditionFilter.new(
      {
        'controller' => 'search',
        'action' => 'index',
        'make' => 'toyota',
        'bsty' => '1',
        'prcf' => '500',
        'prct' => '1000'
      }, 0
    )
  end
  let(:searcher) { Tdk::Searcher.new(search_service, 0) }
  let(:car_count) { 150 }
  let(:page_num) { '(Page 2)' }
  let(:combination) { described_class.new(searcher, car_count: car_count, page_num: page_num) }

  describe '#initialize' do
    it 'sets instance variables correctly' do
      expect(combination.searcher).to eq(searcher)
      expect(combination.car_count).to eq(car_count)
      expect(combination.page_num).to eq(page_num)
      expect(combination.condition_hash).to eq(searcher.condition_hash)
      expect(combination.country_code).to eq(searcher.country_code)
    end
  end

  describe '#applicable?' do
    it 'raises NotImplementedError' do
      expect { combination.applicable? }.to raise_error(NotImplementedError, /must implement #applicable?/)
    end
  end

  describe '#generate_meta_info' do
    it 'raises NotImplementedError' do
      expect { combination.generate_meta_info }.to raise_error(NotImplementedError, /must implement #generate_meta_info/)
    end
  end

  describe '#build_meta_info' do
    let(:text_parts) { ['Bus', 'US$500-US$1,000'] }

    it 'returns correct meta info hash' do
      result = combination.build_meta_info(text_parts)

      expect(result).to eq(
        {
          title: 'Used Bus US$500-US$1,000 imports for sale (Page 2) at TCV (formerly trade carview)',
          description: 'Browse 150 high-quality Bus US$500-US$1,000 (Page 2) on TCV (formerly trade carview), ' \
                       'the trusted marketplace for Japanese used cars. Enjoy secure payments, reliable exporters, and worldwide shipping.',
          h1: 'Used Bus US$500-US$1,000 imports for sale (Page 2)'
        },
      )
    end
  end

  describe 'private methods' do
    describe '#body_style_name' do
      it 'delegates to searcher' do
        expect(searcher).to receive(:body_style_name).and_return('Bus')
        result = combination.send(:body_style_name)
        expect(result).to eq('Bus')
      end
    end

    describe '#number_format' do
      it 'formats number with delimiter' do
        result = combination.send(:number_format, 12_000)
        expect(result).to eq('12,000')
      end
    end

    describe '#single_body_style?' do
      context 'when body style is present and single' do
        it 'returns true' do
          expect(combination.send(:single_body_style?)).to be true
        end
      end

      context 'when body style contains multiple values' do
        let(:search_service) do
          Search::SearchConditionFilter.new(
            {
              'controller' => 'search',
              'action' => 'index',
              'bsty' => '1*2*3'
            }, 0
          )
        end
        let(:searcher) { Tdk::Searcher.new(search_service, 0) }

        it 'returns false' do
          expect(combination.send(:single_body_style?)).to be false
        end
      end

      context 'when body style is blank' do
        let(:search_service) do
          Search::SearchConditionFilter.new(
            {
              'controller' => 'search',
              'action' => 'index'
            }, 0
          )
        end
        let(:searcher) { Tdk::Searcher.new(search_service, 0) }

        it 'returns false' do
          expect(combination.send(:single_body_style?)).to be false
        end
      end
    end

    describe '#supported_body_style?' do
      context 'with supported body style id' do
        it 'returns true' do
          expect(combination.send(:supported_body_style?)).to be true
        end
      end

      context 'with unsupported body style id' do
        let(:search_service) do
          Search::SearchConditionFilter.new(
            {
              'controller' => 'search',
              'action' => 'index',
              'bsty' => '99'
            }, 0
          )
        end
        let(:searcher) { Tdk::Searcher.new(search_service, 0) }

        it 'returns false' do
          expect(combination.send(:supported_body_style?)).to be false
        end
      end
    end

    describe '#supported_price_range?' do
      context 'with supported price range' do
        it 'returns true' do
          expect(combination.send(:supported_price_range?)).to be true
        end
      end

      context 'with unsupported price range' do
        let(:search_service) do
          Search::SearchConditionFilter.new(
            {
              'controller' => 'search',
              'action' => 'index',
              'prcf' => '100',
              'prct' => '200'
            }, 0
          )
        end
        let(:searcher) { Tdk::Searcher.new(search_service, 0) }

        it 'returns false' do
          expect(combination.send(:supported_price_range?)).to be false
        end
      end
    end

    describe '#category_text' do
      context 'with st category' do
        let(:search_service) do
          Search::SearchConditionFilter.new(
            {
              'controller' => 'search',
              'action' => 'index',
              'st' => '13'
            }, 0
          )
        end
        let(:searcher) { Tdk::Searcher.new(search_service, 0) }

        it 'returns correct category text' do
          expect(combination.send(:category_text)).to eq('Left Handle Only')
        end
      end

      context 'with tmns category' do
        let(:search_service) do
          Search::SearchConditionFilter.new(
            {
              'controller' => 'search',
              'action' => 'index',
              'tmns' => '6'
            }, 0
          )
        end
        let(:searcher) { Tdk::Searcher.new(search_service, 0) }

        it 'returns correct category text' do
          expect(combination.send(:category_text)).to eq('Manual')
        end
      end
    end

    describe '#supported_category?' do
      context 'with supported category' do
        let(:search_service) do
          Search::SearchConditionFilter.new(
            {
              'controller' => 'search',
              'action' => 'index',
              'st' => '13'
            }, 0
          )
        end
        let(:searcher) { Tdk::Searcher.new(search_service, 0) }

        it 'returns true' do
          expect(combination.send(:supported_category?)).to be true
        end
      end

      context 'with unsupported category' do
        let(:search_service) do
          Search::SearchConditionFilter.new(
            {
              'controller' => 'search',
              'action' => 'index',
              'st' => '99'
            }, 0
          )
        end
        let(:searcher) { Tdk::Searcher.new(search_service, 0) }

        it 'returns false' do
          expect(combination.send(:supported_category?)).to be false
        end
      end
    end

    describe '#price_range_text' do
      it 'returns formatted price range' do
        expect(combination.send(:price_range_text)).to eq('US$500-US$1,000')
      end

      context 'with only minimum price' do
        let(:search_service) do
          Search::SearchConditionFilter.new(
            {
              'controller' => 'search',
              'action' => 'index',
              'prcf' => '500'
            }, 0
          )
        end
        let(:searcher) { Tdk::Searcher.new(search_service, 0) }

        it 'returns only minimum price' do
          expect(combination.send(:price_range_text)).to eq('US$500')
        end
      end
    end

    describe '#supported_make?' do
      context 'when make is present' do
        it 'returns true' do
          expect(combination.send(:supported_make?)).to be true
        end
      end

      context 'when make is blank' do
        let(:search_service) do
          Search::SearchConditionFilter.new(
            {
              'controller' => 'search',
              'action' => 'index',
              'bsty' => '1'
            }, 0
          )
        end
        let(:searcher) { Tdk::Searcher.new(search_service, 0) }

        it 'returns false' do
          expect(combination.send(:supported_make?)).to be false
        end
      end
    end

    describe '#make_name' do
      before do
        allow(MasterMake).to receive(:get_by_vc_name_e).with('toyota').and_return(
          double(vc_name_e: 'TOYOTA'),
        )
      end

      it 'returns uppercase make name' do
        expect(combination.send(:make_name)).to eq('TOYOTA')
      end
    end
  end
end
