require 'rails_helper'

RSpec.describe Tdk::Searcher do
  let(:search_service) do
    Search::SearchConditionFilter.new({ 'controller' => 'search', 'action' => 'index', 'make' => 'toyota', 'model' => 'yaris' },
                                      0)
  end
  let(:service) { described_class.new(search_service, 0) }
  let(:condition_hash) { { :make => 'toyota', :model => 'yaris' } }
  let(:country_code) { 0 }

  describe '#initialize' do
    it 'check instance value' do
      expect(service.instance_variable_get(:@search_service)).to eq(search_service)
      expect(service.instance_variable_get(:@condition_hash)).to eq(condition_hash)
      expect(service.instance_variable_get(:@condition_size)).to eq(condition_hash.size)
      expect(service.instance_variable_get(:@country_code)).to eq(country_code)
    end
  end

  describe 'check method' do
    it '#kodawari_condition_not_exist?' do
      result = service.kodawari_condition_not_exist?

      expect(result).to be true
    end

    it '#maker?' do
      result = service.maker?

      expect(result).to be true
    end

    it '#model?' do
      result = service.model?

      expect(result).to be true
    end

    it '#bodystyle?' do
      result = service.bodystyle?

      expect(result).to be false
    end

    context '#one_bodystyle?' do
      it 'empty body style id' do
        result = service.one_bodystyle?

        expect(result).to be false
      end

      it 'existing body style id' do
        search_service = Search::SearchConditionFilter.new({ 'controller' => 'search', 'action' => 'index', 'make' => 'toyota',
                                                             'model' => 'yaris', 'bsty' => '1' },
                                                           0)
        service = described_class.new(search_service, 0)
        result = service.one_bodystyle?

        expect(result).to be true
      end
    end

    it '#just_a_kodawari?' do
      search_service = Search::SearchConditionFilter.new({ 'controller' => 'search', 'action' => 'index', 'make' => 'toyota',
                                                           'model' => 'yaris', 'bsty' => '1' },
                                                         0)
      service = described_class.new(search_service, 0)
      result = service.just_a_kodawari?

      expect(result).to be true
    end

    it '#the_kodawari_has_multi_values?' do
      search_service = Search::SearchConditionFilter.new({ 'controller' => 'search', 'action' => 'index', 'make' => 'toyota',
                                                           'model' => 'yaris', 'bsty' => '1*2*11*8*12*9' },
                                                         0)
      service = described_class.new(search_service, 0)
      result = service.the_kodawari_has_multi_values?

      expect(result).to be true
    end

    it '#only_rfc?' do
      service.instance_variable_set(:@rfc, 1)
      result = service.only_rfc?

      expect(result).to be true
    end

    it '#seller_id?' do
      service.instance_variable_set(:@uid, '123')
      result = service.seller_id?

      expect(result).to be true
    end

    it '#couple_registration_year?' do
      search_service = Search::SearchConditionFilter.new({ 'controller' => 'search', 'action' => 'index', 'make' => 'toyota',
                                                           'model' => 'yaris', 'fid' => '2013', 'jid' => '2025' },
                                                         0)
      service = described_class.new(search_service, 0)
      result = service.couple_registration_year?

      expect(result).to be true
    end

    it '#couple_params?' do
      search_service = Search::SearchConditionFilter.new({ 'controller' => 'search', 'action' => 'index', 'make' => 'toyota',
                                                           'model' => 'yaris', 'prcf' => '500', 'prct' => '20000' },
                                                         0)
      service = described_class.new(search_service, 0)
      result = service.couple_params?

      expect(result).to be true
    end

    context '#seller_name' do
      let(:solr_seller_response) do
        { 'responseHeader' => { 'status' => 0, 'QTime' => 1, 'params' => { 'q' => 'UserID: 850014', 'fl' => 'DealerName', 'start' => '0',
                                                                           'rows' => '25', 'wt' => 'json' } }, 'response' =>
            { 'numFound' => 2, 'start' => 0, 'docs' => [{ 'DealerName' => 'sss' }, { 'DealerName' => 'sss' }] } }
      end

      before do
        allow(Solr::Base).to receive_message_chain(:new, :find).and_return(solr_seller_response)
      end

      it 'check seller name' do
        search_service = Search::SearchConditionFilter.new({ 'controller' => 'search', 'action' => 'index', 'make' => 'toyota',
                                                             'model' => 'yaris', 'uid' => '850014' },
                                                           0)
        service = described_class.new(search_service, 0)
        result = service.seller_name

        expect(result).to eq('sss')
      end
    end

    it '#kodawari_text' do
      search_service = Search::SearchConditionFilter.new({ 'controller' => 'search', 'action' => 'index', 'make' => 'toyota',
                                                           'model' => 'yaris', 'prcf' => '500', 'prct' => '20000' },
                                                         0)
      service = described_class.new(search_service, 0)
      result = service.kodawari_text

      expect(result).to eq('US$500-US$20,000')
    end

    it '#body_style_name' do
      search_service = Search::SearchConditionFilter.new({ 'controller' => 'search', 'action' => 'index', 'make' => 'toyota',
                                                           'model' => 'yaris', 'bsty' => '1' },
                                                         0)
      service = described_class.new(search_service, 0)
      result = service.body_style_name

      expect(result).to eq('Bus')
    end

    it '#search_condition_text_sp' do
      search_service = Search::SearchConditionFilter.new({ 'controller' => 'search', 'action' => 'index', 'make' => 'toyota',
                                                           'model' => 'yaris', 'bsty' => '1' },
                                                         0)
      service = described_class.new(search_service, 0)
      result = service.search_condition_text_sp

      expect(result).to eq("'for Bus'")
    end
  end

  describe 'test private method' do
    it '#number_format' do
      result = service.send(:number_format, 12_000)

      expect(result).to eq('12,000')
    end

    context '#beauty_text' do
      it 'when MasterInfo::FobSearchOption' do
        result = service.send(:beauty_text).call(MasterInfo::SearchKeyMappingSolr.find_by(key: 'prcf'), '500', :prcf, false, '-')

        expect(result).to eq('US$500-')
      end

      it 'MasterInfo::MileageSearchOption' do
        result = service.send(:beauty_text).call(MasterInfo::SearchKeyMappingSolr.find_by(key: 'mimn'), '50000', :mimn, false, '-')

        expect(result).to eq('50,000km-')
      end

      it 'MasterInfo::CapacitySearchOption' do
        result = service.send(:beauty_text).call(MasterInfo::SearchKeyMappingSolr.find_by(key: 'sds'), '550', :sds, false, '-')

        expect(result).to eq('550cc-')
      end

      it 'MCountry' do
        result = service.send(:beauty_text).call(MasterInfo::SearchKeyMappingSolr.find_by(key: 'co'), 20, :co, false, '-')

        expect(result).to eq('Andorra')
      end

      it 'MArticle' do
        result = service.send(:beauty_text).call(MasterInfo::SearchKeyMappingSolr.find_by(key: 'tmns'), 5, :tmns, false, '-')

        expect(result).to eq('Automatic')
      end

      it 'anything else' do
        result = service.send(:beauty_text).call(MasterInfo::SearchKeyMappingSolr.find_by(key: 'st'), 13, :st, false, '-')

        expect(result).to eq('Left Handle Only')
      end
    end

    it '#model_year_month_text' do
      search_service = Search::SearchConditionFilter.new({ 'controller' => 'search', 'action' => 'index', 'make' => 'toyota',
                                                           'model' => 'yaris', 'fid' => '2013', 'jid' => '2025', 'smo' => '12', 'emo' => '1' },
                                                         0)
      service = described_class.new(search_service, 0)
      result = service.send(:model_year_month_text)

      expect(result).to eq('2013/12 ～ 2025/1, ')
    end

    context '#regular_car_text' do
      it 'check data' do
        search_service = Search::SearchConditionFilter.new({ 'controller' => 'search', 'action' => 'index', 'make' => 'toyota',
                                                             'model' => 'yaris', 'fid' => '2013', 'jid' => '2025', 'smo' => '12', 'emo' => '1' },
                                                           404)
        service = described_class.new(search_service, 404)
        service.instance_variable_set(:@rfc, 1)
        result = service.send(:regular_car_text)

        expect(result).to eq(' For KEN')
      end
    end

    it '#new_car_text' do
      search_service = Search::SearchConditionFilter.new({ 'controller' => 'search', 'action' => 'index', 'make' => 'toyota',
                                                           'model' => 'yaris', 'fid' => '2013', 'jid' => '2025', 'smo' => '12',
                                                           'nw' => '1', 'emo' => '1' }, 404)
      service = described_class.new(search_service, 404)
      result = service.send(:new_car_text)

      expect(result).to eq('New Stocks')
    end

    it '#normalize_params' do
      search_service = Search::SearchConditionFilter.new({ 'controller' => 'search', 'action' => 'index', 'make' => 'toyota',
                                                           'model' => 'yaris', 'fid' => '2013', 'jid' => '2025', 'smo' => '12',
                                                           'nw' => '1', 'emo' => '1' }, 404)
      service = described_class.new(search_service, 404)
      result = service.send(:normalize_params)

      expect(result).to eq({ make: 'toyota', model: 'yaris', fid: '2013', jid: '2025', smo: '12', nw: '1', emo: '1' })
    end

    it '#intersection_kodawari_params' do
      search_service = Search::SearchConditionFilter.new({ 'controller' => 'search', 'action' => 'index', 'make' => 'toyota',
                                                           'model' => 'yaris', 'fid' => '2013', 'jid' => '2025', 'smo' => '12',
                                                           'nw' => '1', 'emo' => '1' }, 404)
      service = described_class.new(search_service, 404)
      result = service.send(:intersection_kodawari_params)

      expect(result).to eq(%i[nw fid smo jid emo])
    end

    it '#body_style_id' do
      search_service = Search::SearchConditionFilter.new({ 'controller' => 'search', 'action' => 'index', 'make' => 'toyota',
                                                           'model' => 'yaris', 'bsty' => '1' }, 404)
      service = described_class.new(search_service, 404)
      result = service.send(:body_style_id)

      expect(result).to eq('1')
    end

    it '#add_specific_unit' do
      result = service.send(:add_specific_unit, :bsty, 'Name 1', true)

      expect(result).to eq('Name 1')
    end

    context '#data_parser' do
      it 'when CHECKBOX_FIELDS' do
        result = service.send(:data_parser, '1', :nw)

        expect(result).to eq(1)
      end

      it 'when ARRAY_FIELDS' do
        result = service.send(:data_parser, '1', :bsty)

        expect(result).to eq(['1'])
      end

      it 'when INT_VALUE_LIST' do
        result = service.send(:data_parser, '2013', :fid)

        expect(result).to eq('2013')
      end

      it 'anything else' do
        result = service.send(:data_parser, 'toyota', :make)

        expect(result).to eq('toyota')
      end
    end

    it '#convert_to_model' do
      result = service.send(:convert_to_model, 'MasterMake')

      expect(result).to eq(MasterMake)
    end

    it '#convert_to_model' do
      result = service.send(:convert_to_model, 'MasterMake')

      expect(result).to eq(MasterMake)
    end

    it '#check_valid_amount_params' do
      search_service = Search::SearchConditionFilter.new({ 'controller' => 'search', 'action' => 'index', 'make' => 'toyota',
                                                           'model' => 'yaris', 'bsty' => '1*29' }, 404)
      service = described_class.new(search_service, 404)
      result = service.send(:check_valid_amount_params)

      expect(result).to be nil
    end

    context '#fetch_option_name' do
      it 'smart_phone' do
        result = service.send(:fetch_option_name, MasterInfo::FobSearchOption, 500, true)

        expect(result).to eq('US$500')
      end

      it 'pc' do
        result = service.send(:fetch_option_name, MasterInfo::FobSearchOption, 500, false)

        expect(result).to eq('US$500')
      end
    end
  end
end
