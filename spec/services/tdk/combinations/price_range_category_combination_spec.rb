require 'rails_helper'

RSpec.describe Tdk::Combinations::PriceRangeCategoryCombination do
  let(:search_service) do
    Search::SearchConditionFilter.new(
      {
        'controller' => 'search',
        'action' => 'index',
        'st' => '13',
        'prcf' => '500',
        'prct' => '1000'
      }, 0
    )
  end
  let(:searcher) { Tdk::Searcher.new(search_service, 0) }
  let(:car_count) { 150 }
  let(:page_num) { '(Page 2)' }
  let(:combination) { described_class.new(searcher, car_count: car_count, page_num: page_num) }

  describe '#applicable?' do
    context 'when all conditions are met' do
      it 'returns true' do
        expect(combination.applicable?).to be true
      end
    end

    context 'when price range is not supported' do
      let(:search_service) do
        Search::SearchConditionFilter.new(
          {
            'controller' => 'search',
            'action' => 'index',
            'st' => '13',
            'prcf' => '100',
            'prct' => '200'
          }, 0
        )
      end
      let(:searcher) { Tdk::Searcher.new(search_service, 0) }

      it 'returns false' do
        expect(combination.applicable?).to be false
      end
    end

    context 'when category is not supported' do
      let(:search_service) do
        Search::SearchConditionFilter.new(
          {
            'controller' => 'search',
            'action' => 'index',
            'st' => '99',
            'prcf' => '500',
            'prct' => '1000'
          }, 0
        )
      end
      let(:searcher) { Tdk::Searcher.new(search_service, 0) }

      it 'returns false' do
        expect(combination.applicable?).to be false
      end
    end

    context 'when condition size is not valid' do
      let(:search_service) do
        Search::SearchConditionFilter.new(
          {
            'controller' => 'search',
            'action' => 'index',
            'st' => '13',
            'prcf' => '500',
            'prct' => '1000',
            'make' => 'toyota'
          }, 0
        )
      end
      let(:searcher) { Tdk::Searcher.new(search_service, 0) }

      it 'returns false' do
        expect(combination.applicable?).to be false
      end
    end
  end

  describe '#generate_meta_info' do
    it 'returns correct meta info' do
      result = combination.generate_meta_info

      expect(result).to eq(
        {
          title: 'Used Left Handle Only US$500-US$1,000 imports for sale (Page 2) at TCV (formerly trade carview)',
          description: 'Browse 150 high-quality Left Handle Only US$500-US$1,000 (Page 2) on TCV (formerly trade carview), ' \
                       'the trusted marketplace for Japanese used cars. Enjoy secure payments, reliable exporters, and worldwide shipping.',
          h1: 'Used Left Handle Only US$500-US$1,000 imports for sale (Page 2)'
        },
      )
    end
  end

  describe 'private methods' do
    describe '#valid_condition_size?' do
      context 'when only category and price range are present' do
        it 'returns true' do
          expect(combination.send(:valid_condition_size?)).to be true
        end
      end

      context 'when additional parameters are present' do
        let(:search_service) do
          Search::SearchConditionFilter.new(
            {
              'controller' => 'search',
              'action' => 'index',
              'st' => '13',
              'prcf' => '500',
              'prct' => '1000',
              'make' => 'toyota'
            }, 0
          )
        end
        let(:searcher) { Tdk::Searcher.new(search_service, 0) }

        it 'returns false' do
          expect(combination.send(:valid_condition_size?)).to be false
        end
      end
    end
  end
end
