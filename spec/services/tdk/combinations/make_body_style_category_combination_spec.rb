require 'rails_helper'

RSpec.describe Tdk::Combinations::MakeBodyStyleCategoryCombination do
  let(:search_service) do
    Search::SearchConditionFilter.new(
      {
        'controller' => 'search',
        'action' => 'index',
        'make' => 'toyota',
        'bsty' => '1',
        'st' => '13'
      }, 0
    )
  end
  let(:searcher) { Tdk::Searcher.new(search_service, 0) }
  let(:car_count) { 150 }
  let(:page_num) { '(Page 2)' }
  let(:combination) { described_class.new(searcher, car_count: car_count, page_num: page_num) }

  before do
    allow(MasterMake).to receive(:get_by_vc_name_e).with('toyota').and_return(
      double(vc_name_e: 'TOYOTA'),
    )
  end

  describe '#applicable?' do
    context 'when all conditions are met' do
      before do
        allow(searcher).to receive(:body_style_name).and_return('Bus')
      end

      it 'returns true' do
        expect(combination.applicable?).to be true
      end
    end

    context 'when make is not supported' do
      let(:search_service) do
        Search::SearchConditionFilter.new(
          {
            'controller' => 'search',
            'action' => 'index',
            'bsty' => '1',
            'st' => '13'
          }, 0
        )
      end
      let(:searcher) { Tdk::Searcher.new(search_service, 0) }

      it 'returns false' do
        expect(combination.applicable?).to be false
      end
    end

    context 'when body style is not supported' do
      let(:search_service) do
        Search::SearchConditionFilter.new(
          {
            'controller' => 'search',
            'action' => 'index',
            'make' => 'toyota',
            'bsty' => '99',
            'st' => '13'
          }, 0
        )
      end
      let(:searcher) { Tdk::Searcher.new(search_service, 0) }

      it 'returns false' do
        expect(combination.applicable?).to be false
      end
    end

    context 'when category is not supported' do
      let(:search_service) do
        Search::SearchConditionFilter.new(
          {
            'controller' => 'search',
            'action' => 'index',
            'make' => 'toyota',
            'bsty' => '1',
            'st' => '99'
          }, 0
        )
      end
      let(:searcher) { Tdk::Searcher.new(search_service, 0) }

      it 'returns false' do
        expect(combination.applicable?).to be false
      end
    end

    context 'when condition size is not valid' do
      let(:search_service) do
        Search::SearchConditionFilter.new(
          {
            'controller' => 'search',
            'action' => 'index',
            'make' => 'toyota',
            'bsty' => '1',
            'st' => '13',
            'model' => 'yaris'
          }, 0
        )
      end
      let(:searcher) { Tdk::Searcher.new(search_service, 0) }

      it 'returns false' do
        expect(combination.applicable?).to be false
      end
    end
  end

  describe '#generate_meta_info' do
    before do
      allow(searcher).to receive(:body_style_name).and_return('Bus')
    end

    it 'returns correct meta info' do
      result = combination.generate_meta_info

      expect(result).to eq(
        {
          title: 'Used TOYOTA Bus Left Handle Only imports for sale (Page 2) at TCV (formerly trade carview)',
          description: 'Browse 150 high-quality TOYOTA Bus Left Handle Only (Page 2) on TCV (formerly trade carview), ' \
                       'the trusted marketplace for Japanese used cars. Enjoy secure payments, reliable exporters, and worldwide shipping.',
          h1: 'Used TOYOTA Bus Left Handle Only imports for sale (Page 2)'
        },
      )
    end
  end

  describe 'private methods' do
    describe '#valid_condition_size?' do
      context 'when only make, body style and category are present' do
        it 'returns true' do
          expect(combination.send(:valid_condition_size?)).to be true
        end
      end

      context 'when additional parameters are present' do
        let(:search_service) do
          Search::SearchConditionFilter.new(
            {
              'controller' => 'search',
              'action' => 'index',
              'make' => 'toyota',
              'bsty' => '1',
              'st' => '13',
              'model' => 'yaris'
            }, 0
          )
        end
        let(:searcher) { Tdk::Searcher.new(search_service, 0) }

        it 'returns false' do
          expect(combination.send(:valid_condition_size?)).to be false
        end
      end
    end
  end
end
