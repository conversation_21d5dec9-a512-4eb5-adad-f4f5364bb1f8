require 'rails_helper'

RSpec.describe MakeModels::PopularRankings do
  let(:maker_id) { 1 }
  let(:model_ids) { [1, 2, 3] }
  let(:service) { described_class.new(user_country_code, maker_id, model_ids) }

  describe '#data' do
    context 'when user country is included in COUNTRY_NUMBERS' do
      let(:user_country_code) { 894 }
      it 'returns data with rankings' do
        expect(service.data).to eq({ show: true, rankings: service.send(:rankings) })
      end
    end

    context 'when user country is not included in COUNTRY_NUMBERS' do
      let(:user_country_code) { 900 }
      it 'returns data without rankings' do
        expect(service.data).to eq({ show: false })
      end
    end
  end

  describe '#rankings' do
    let(:user_country_code) { 894 }
    it 'returns the correct rankings' do
      make_model = build(:t_invoice_issue_ranking_country, make_id: maker_id, model_id: model_ids.first, country_number: user_country_code)
      allow(TInvoiceIssueRankingCountry).to receive_message_chain(:select, :where, :group, :order, :limit).and_return([make_model])

      expect(service.send(:rankings)).to eq([make_model])
    end
  end
end
