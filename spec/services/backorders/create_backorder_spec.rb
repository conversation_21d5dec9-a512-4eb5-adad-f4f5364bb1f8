require 'rails_helper'

RSpec.describe Backorders::CreateBackorder, type: :service do
  let(:cvpd) { 'i=854159&u=4ybcy7pQT1qijbtULhwAbA==&uh=abbasd89238&te=2024/01/08 12:19:19&ud=2024/01/08 11:59:53&h=18be4ad9fe33ab86897ed58930eb34aa' }
  let(:user_ip) { '**********' }
  let(:params) do
    {
      make: '2',
      model: '50026',
      fid: '2018',
      budget: '10000',
      bsty: '3',
      country: '533',
      name: '123123',
      phone: '123123123123',
      whatsapp: '1',
      description: 'test',
      ecls: '52',
      mileage: '213',
      utm_source: '',
      utm_medium: 'gronavi',
      utm_campaign: '',
      preposition: '1',
      lowest_price: '',
      file: Rack::Test::UploadedFile.new('public/apple-touch-icon.png', 'image/png')
    }
  end
  let(:service) { described_class.new(cvpd, params, user_ip) }

  describe '#call' do
    context 'when the API call is successful' do
      let(:fake_response) { double('response', success?: true, body: 'response body', status: 200) }

      before do
        allow_any_instance_of(TcvCoreApi::Request).to receive(:send_multipart_connection).and_return(fake_response)
      end

      it 'makes a successful API call' do
        expect(service.call).to eq({ success: true, body: 'response body', status: 200 })
      end
    end

    context 'when the API call is failed' do
      let(:fake_response) { double('response', success?: false, body: 'response body', status: 400) }

      before do
        allow_any_instance_of(TcvCoreApi::Request).to receive(:send_multipart_connection).and_return(fake_response)
      end

      it 'makes a failed API call' do
        expect(service.call).to eq({ success: false, body: 'response body', status: 400 })
      end
    end
  end

  describe 'private methods' do
    describe '#payload' do
      it 'check payload present' do
        payload = {
          MakeID: service.instance_variable_get(:@make_id),
          ModelID: service.instance_variable_get(:@model_id),
          RegistationYear: service.instance_variable_get(:@regis_year),
          UserBudgetUSD: service.instance_variable_get(:@budget),
          BodyStyleID: service.instance_variable_get(:@body_style),
          UserCountryNumber: service.instance_variable_get(:@country),
          UserName: service.instance_variable_get(:@name),
          UserPhoneNumber: service.instance_variable_get(:@phone),
          WhatsAppFlg: service.instance_variable_get(:@whatsapp),
          Comment: service.instance_variable_get(:@comment),
          AttachedFile: service.instance_variable_get(:@file),
          UserIpAddress: service.instance_variable_get(:@user_ip),
          Color: service.instance_variable_get(:@color),
          Mileage: service.instance_variable_get(:@mileage),
          Source: service.instance_variable_get(:@source),
          Medium: service.instance_variable_get(:@medium),
          Campaign: service.instance_variable_get(:@campaign),
          BNPL: service.instance_variable_get(:@bnpl),
          RegistationYearType: service.instance_variable_get(:@preposition),
          DisplayedLowestPrice: service.instance_variable_get(:@lowest_price)
        }

        expect(service.send(:payload)).to eq(payload)
      end
    end

    describe '#fetch_value' do
      it 'returns nil for zero' do
        expect(service.send(:fetch_value, 0)).to be_nil
      end

      it 'returns integer for non-zero string' do
        random = rand(1..10)
        expect(service.send(:fetch_value, random)).to eq random
      end
    end

    describe '#fetch_measurement_param_value' do
      it 'returns the value if within max length' do
        expect(service.send(:fetch_measurement_param_value, 'short_value')).to eq 'short_value'
      end

      it 'returns nil if value exceeds max length' do
        long_string = 'a' * 65
        expect(service.send(:fetch_measurement_param_value, long_string)).to be_nil
      end
    end
  end
end
