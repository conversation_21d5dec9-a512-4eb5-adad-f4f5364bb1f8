require 'rails_helper'

RSpec.describe Backorders::FetchLowestPrice, type: :service do
  let(:params) { { make: '2', model: '50026', preposition: '1', year: '2020' } }
  let(:service) { described_class.new(params) }

  describe '#call' do
    before do
      allow_any_instance_of(Solr::Base).to receive(:find).and_return(solr_response)
    end

    context 'when make or model is nil' do
      let(:service) { described_class.new({}) }
      let(:solr_response) { { 'response' => { 'docs' => [] } } }

      it 'returns default price text' do
        expect(service.call).to eq(service.send(:default_price_text))
      end
    end

    context 'when Solr query returns a price' do
      let(:solr_response) { { 'response' => { 'docs' => [{ 'Price' => '1000' }] } } }

      it 'returns available price text with price' do
        allow(Solr::Base).to receive_message_chain(:new, :find).and_return(solr_response)
        allow(solr_response).to receive(:dig).and_return('1000')
        result = service.call
        expect(result.first).to include('The lowest reference price is $1,000')
        expect(result.last).to eq(1000)
      end
    end

    context 'when Solr query does not return a price' do
      let(:solr_response) { { 'response' => { 'docs' => [] } } }

      it 'returns no price text' do
        allow(Solr::Base).to receive_message_chain(:new, :find).and_return(solr_response)
        allow(solr_response).to receive(:dig).and_return(nil)
        expect(service.call).to eq(service.send(:no_price_text))
      end
    end

    context 'when preposition is :to' do
      let(:params) { { make: '2', model: '50026', preposition: '2', year: '2020' } }
      let(:service) { described_class.new(params) }
      let(:solr_response) { { 'response' => { 'docs' => [{ 'Price' => '1000' }] } } }

      it 'forms the correct Solr query for :to preposition' do
        expect(service.send(:model_year_query)).to eq('ModelYear:[* TO 2020]')
      end
    end

    context 'when preposition is :in' do
      let(:params) { { make: '2', model: '50026', preposition: '3', year: '2020' } }
      let(:service) { described_class.new(params) }
      let(:solr_response) { { 'response' => { 'docs' => [{ 'Price' => '1000' }] } } }

      it 'forms the correct Solr query for :in preposition' do
        expect(service.send(:model_year_query)).to eq('ModelYear:2020')
      end
    end
  end
end
