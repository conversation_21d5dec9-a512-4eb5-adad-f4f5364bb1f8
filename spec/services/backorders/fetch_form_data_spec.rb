require 'rails_helper'

RSpec.describe Backorders::FetchFormData, type: :service do
  let(:car_counter_instance) { DataLoader::CarCounterService.new(0) }
  let(:service) { described_class.new(car_counter_instance) }

  describe '#call' do
    it 'returns form data with correct structure' do
      result = service.call
      expect(result[:maker_options]).to be_present
      expect(result[:model_options]).to be_present
      expect(result[:regis_year_from]).to be_present
      expect(result[:body_style_options]).to be_present
      expect(result[:color_options]).to be_present
    end
  end
end
