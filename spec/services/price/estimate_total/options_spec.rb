require 'rails_helper'

RSpec.describe Price::EstimateTotal::Options do
  let(:country_code) { 28 }
  let(:country_fob_list) { [392, 36, 242, 470, 554, 826, 152, 372] }
  let(:service) { described_class.new(country_code) }

  before(:each) do
    service.instance_variable_set(:@country_fob_list, country_fob_list)
  end

  describe '#initialize' do
    it 'assigns user_country_code' do
      expect(service.instance_variable_get(:@country_code)).to eq(country_code)
      expect(service.instance_variable_get(:@country_fob_list)).to eq(country_fob_list)
    end
  end

  describe 'check method' do
    it '#all' do
      result = service.all

      expect(result).to eq({ shipping: false, insurance: true, pre_ship_inspection: false })
    end

    it '#shipping?' do
      result = service.shipping?

      expect(result).to be false
    end
  end

  describe 'check private method' do
    it '#insurance?' do
      result = service.send(:insurance?)

      expect(result).to be true
    end

    it '#insurance?' do
      result = service.send(:pre_ship_inspection?)

      expect(result).to be false
    end
  end
end
