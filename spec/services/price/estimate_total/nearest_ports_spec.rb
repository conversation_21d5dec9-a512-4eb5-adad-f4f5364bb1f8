require 'rails_helper'

RSpec.describe Price::EstimateTotal::NearestPorts do
  let(:service) { described_class.new(404) }

  describe '#initialize' do
    it 'assigns user_country_code' do
      expect(service.instance_variable_get(:@country_code)).to eq(404)
    end
  end

  describe '#all' do
    it 'additional_ports present' do
      result = service.all

      expect(result).to eq([[16_640, 'MOMBASA']])
    end

    it 'not existing additional_ports' do
      result = described_class.new(16).all

      expect(result.first[0].class).to eq(Integer)
      expect(result.first[1].class).to eq(String)
    end
  end
end
