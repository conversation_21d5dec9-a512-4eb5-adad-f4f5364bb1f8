require 'rails_helper'

RSpec.describe Price::EstimateTotal::Alerts do
  let(:service) { described_class.new(404) }

  describe '#initialize' do
    let(:user_country_code) { 404 }
    subject(:service) { Price::EstimateTotal::Alerts.new(user_country_code) }

    it 'assigns user_country_code' do
      expect(service.instance_variable_get(:@country_code)).to eq(user_country_code)
    end

    it 'assigns current_year' do
      expect(service.instance_variable_get(:@current_year)).to eq(Time.zone.now.year)
    end
  end

  describe 'Test function' do
    context '#all' do
      let!(:keys_data) { %i[text show_asterisk year before_year kind] }
      it '#existing pattern_alert_1' do
        result = described_class.new(404).all

        expect(result.keys).to eq(keys_data)
      end

      it '#existing pattern_alert_2' do
        result = described_class.new(598).all

        expect(result.keys).to eq(keys_data)
      end

      it '#existing pattern_alert_3' do
        result = described_class.new(50).all

        expect(result.keys).to eq(keys_data)
      end

      it '#existing pattern_alert_4' do
        result = described_class.new(840).all

        expect(result.keys).to eq(keys_data)
      end

      it '#existing pattern_alert_5' do
        result = described_class.new(388).all

        expect(result.keys).to eq(keys_data)
        expect(result[:text]).to include('<p>*Jamaica Regulations</p>')
      end
    end

    context 'test private method' do
      it '#generate_alert with invalid' do
        result = service.send(:generate_alert, 0)
        expect(result).to eq({})
      end

      it '#pattern_alert_1' do
        country = MasterInfo::EstimatePriceAlert.find_by(country_number: 404)
        result = service.send(:pattern_alert_1, country)
        expect(result[:text]).to include("Please select #{Time.zone.now.year - country.year} or above model of Manufacture year for Kenya")
        expect(result[:show_asterisk]).to be true
      end

      it '#pattern_alert_2' do
        result = service.send(:pattern_alert_2)
        expect(result[:text]).to eq('')
        expect(result[:show_asterisk]).to be true
      end

      it '#pattern_alert_3' do
        country = MasterInfo::EstimatePriceAlert.find_by(country_number: 50)
        result = service.send(:pattern_alert_3, country)
        expect(result[:text]).to include("Please select #{Time.zone.now.year - country.year} or above model of Registration year for Bangladesh")
        expect(result[:show_asterisk]).to be false
      end

      it '#pattern_alert_4' do
        result = service.send(:pattern_alert_4, MasterInfo::EstimatePriceAlert.find_by(country_number: 840))
        expect(result[:text]).to include('Please select below')
        expect(result[:show_asterisk]).to be false
      end

      it '#pattern_alert_5' do
        result = service.send(:pattern_alert_5)
        expect(result[:text]).to include('<p>*Jamaica Regulations</p>')
      end

      it '#country_data' do
        result = service.send(:country_data, 404)

        expect(result.class).to eq(MasterInfo::EstimatePriceAlert)
      end
    end
  end
end
