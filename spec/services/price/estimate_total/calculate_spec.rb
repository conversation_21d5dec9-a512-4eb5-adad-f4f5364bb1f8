require 'rails_helper'

RSpec.describe Price::EstimateTotal::Calculate do
  let(:car_ids) { '[409582,561884,561904,416801,409549,562005,562007,562016,409598,561973]' }
  let(:checkbox_price_options) { { :insurance => true, :inspection => false, :shipping => true } }
  let(:port_country_number) { '28' }
  let(:port_id) { '125' }
  let(:exchange_rate) { 1 }

  let(:fake_api_response) do
    { 'totalPriceItems' => [{ 'itemId' => 409_582, 'isSuccess' => true, 'totalPriceUSD' => 4_858.0, 'totalPriceView' => 'US$4,858' },
                            { 'itemId' => 561_884, 'isSuccess' => false, 'totalPriceUSD' => 0.0, 'totalPriceView' => nil },
                            { 'itemId' => 561_904, 'isSuccess' => false, 'totalPriceUSD' => 0.0, 'totalPriceView' => nil },
                            { 'itemId' => 416_801, 'isSuccess' => false, 'totalPriceUSD' => 0.0, 'totalPriceView' => nil },
                            { 'itemId' => 409_549, 'isSuccess' => true, 'totalPriceUSD' => 11_558.0, 'totalPriceView' => 'US$11,558' },
                            { 'itemId' => 562_005, 'isSuccess' => false, 'totalPriceUSD' => 0.0, 'totalPriceView' => nil },
                            { 'itemId' => 562_007, 'isSuccess' => false, 'totalPriceUSD' => 0.0, 'totalPriceView' => nil },
                            { 'itemId' => 562_016, 'isSuccess' => false, 'totalPriceUSD' => 0.0, 'totalPriceView' => nil },
                            { 'itemId' => 409_598, 'isSuccess' => true, 'totalPriceUSD' => 15_854.0, 'totalPriceView' => 'US$15,854' },
                            { 'itemId' => 561_973, 'isSuccess' => true, 'totalPriceUSD' => 2_222_844.0, 'totalPriceView' => 'US$2,222,844' }],
      'exchangeRateId' => 1, 'authentication' => { 'cvpd' => '' } }
  end
  let(:service) { described_class.new(car_ids, checkbox_price_options, port_country_number, port_id, exchange_rate) }

  describe '#initialize' do
    it 'check instance variable' do
      expect(service.instance_variables).to eq(%i[@car_ids @checkbox_price_options @port_country_number @port_id @exchange_rate])
    end
  end

  describe 'Test function' do
    context '#exec' do
      it 'should response success' do
        allow_any_instance_of(TcvCoreApi::Request).to receive(:send).and_return(double(body: fake_api_response, success?: true))
        response = service.exec
        expect(response).to eq(fake_api_response)
      end

      it 'should response failed' do
        allow_any_instance_of(TcvCoreApi::Request).to receive(:send).and_return(double(body: {}, success?: false))
        response = service.exec
        expect(response).to eq({})
      end
    end

    context 'test private method' do
      let(:param_data) do
        {
          itemids: service.send(:item_ids),
          dischargePortCountryNumber: port_country_number&.to_i,
          dischargePortId: port_id&.to_i,
          hasMarineInsurance: checkbox_price_options[:insurance],
          hasInspection: checkbox_price_options[:inspection],
          hasFreightCost: checkbox_price_options[:shipping],
          exchangeRateId: exchange_rate
        }.to_json
      end
      let(:invalid_json_string) { 'not a valid JSON string' }

      it '#param_parsed' do
        expect(service.send(:param_parsed)).to eq(param_data)
      end

      it '#item_ids' do
        expect(service.send(:item_ids)).to eq([409_582, 561_884, 561_904, 416_801, 409_549, 562_005, 562_007, 562_016, 409_598, 561_973])
      end

      it '#item_ids with invalid_json_string' do
        allow(service).to receive(:car_ids).and_return(invalid_json_string)

        expect(service.send(:item_ids)).to eq([])
      end
    end
  end
end
