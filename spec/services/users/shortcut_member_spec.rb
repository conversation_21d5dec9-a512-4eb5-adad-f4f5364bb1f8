require 'rails_helper'

RSpec.describe Users::ShortcutMember, type: :service do
  describe '#create' do
    let(:cookie_cvpd) { 'sample_cookie' }
    let(:subject) { described_class.new(cookie_cvpd) }

    context 'when cookie_cvpd is present' do
      it 'sends a POST request with correct headers' do
        allow_any_instance_of(TcvCoreApi::Request).to receive(:send).and_return(instance_double(TcvCoreApi::Response, success?: true))
        expect(subject.create).to eq(true)
      end
    end

    context 'when cookie_cvpd is not present' do
      let(:cookie_cvpd) { nil }

      it 'sends a POST request without X-Tcv-Zigexn-CVPD header' do
        expect(subject.create).to eq(false)
      end
    end
  end
end
