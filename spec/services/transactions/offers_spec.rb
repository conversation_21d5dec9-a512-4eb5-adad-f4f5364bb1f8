require 'rails_helper'

RSpec.describe Transactions::Offers, type: :service do
  let(:params) do
    {
      message: 'I want to negotiate the best price\n',
      insurance: true,
      inspection: false,
      shipping: true,
      receive_newsletter: true,
      user_ip_address: '***********',
      user_agent: 'Mozilla/5.0',
      cookies: {
        current_country: '392',
        exrate: '1',
        source: 'source',
        source3: 'source3',
        guid: SecureRandom.uuid.delete('-').upcase,
        cookie_cvpd: 'cookie_cvpd'
      },
      request_url: 'http://localhost:18080/ajax_v2/transactions/offers',
      referer_url: 'http://localhost:18080/used_car/bmw/mini%20park%20lane/561665/',
      is_pc: true,
      ab_test_content: { 'test1' => 'A', 'test2' => 'B' },
      item_id: 561_665,
      country_number: 704,
      port_id: 21_850,
      temporary_contact_id: 1
    }
  end

  subject { described_class.new(params) }

  describe '#initialize' do
    it 'initializes with correct parameters' do
      expect(subject.instance_variable_get(:@message)).to eq(params[:message])
      expect(subject.instance_variable_get(:@insurance)).to eq(params[:insurance])
      expect(subject.instance_variable_get(:@inspection)).to eq(params[:inspection])
      expect(subject.instance_variable_get(:@shipping)).to eq(params[:shipping])
      expect(subject.instance_variable_get(:@receive_newsletter)).to eq(params[:receive_newsletter])
      expect(subject.instance_variable_get(:@user_ip_address)).to eq(params[:user_ip_address])
      expect(subject.instance_variable_get(:@user_agent)).to eq(params[:user_agent])
      expect(subject.instance_variable_get(:@access_country_number)).to eq(params.dig(:cookies, :current_country))
      expect(subject.instance_variable_get(:@exrate)).to eq(params.dig(:cookies, :exrate))
      expect(subject.instance_variable_get(:@request_url)).to eq(params[:request_url])
      expect(subject.instance_variable_get(:@referer_url)).to eq(params[:referer_url])
      expect(subject.instance_variable_get(:@is_pc)).to eq(params[:is_pc])
      expect(subject.instance_variable_get(:@ab_test_contents)).to eq(params[:ab_test_content].map { |k, v| { testNameV2: k, valiationNameV2: v } })
      expect(subject.instance_variable_get(:@item_id)).to eq(params[:item_id])
      expect(subject.instance_variable_get(:@ship_estimated_country_number)).to eq(params[:country_number])
      expect(subject.instance_variable_get(:@ship_estimated_port_id)).to eq(params[:port_id])
      expect(subject.instance_variable_get(:@temporary_contact_id)).to eq(params[:temporary_contact_id])
    end
  end

  describe '#url' do
    it 'returns the correct url' do
      expected_url = "#{Settings.tcv_core_api.domain}#{Settings.tcv_core_api.v1_paths.offer.create}"
      expect(subject.send(:url)).to eq(expected_url)
    end
  end

  describe '#params' do
    it 'returns the correct params' do
      expected_params = {
        'itemId' => params[:item_id],
        'dischargePortCountryNumber' => params[:country_number],
        'dischargePortId' => params[:port_id],
        'guid' => params.dig(:cookies, :guid),
        'src' => params.dig(:cookies, :source),
        'src3' => params.dig(:cookies, :source3),
        'msg' => params[:message],
        'hasMarineInsurance' => params[:insurance],
        'hasInspection' => params[:inspection],
        'hasFreightCost' => params[:shipping],
        'isReceiveNewsletter' => params[:receive_newsletter],
        'userIpAddress' => params[:user_ip_address],
        'userAgent' => params[:user_agent],
        'accessCountryNumber' => params.dig(:cookies, :current_country),
        'exchangeRateId' => params.dig(:cookies, :exrate),
        'requestURL' => params[:request_url],
        'refererURL' => params[:referer_url],
        'isPC' => params[:is_pc],
        'abtestContents' => params[:ab_test_content].map { |k, v| { testNameV2: k, valiationNameV2: v } },
        'isBNPL' => params[:is_bnpl]
      }.to_json
      expect(subject.send(:params)).to eq(expected_params)
    end
  end

  describe '#create' do
    it 'when validation is error' do
      response = subject.create

      expect(response.first['errorCode']).to eq(nil)
    end

    it 'when offer successfuly' do
      response_body = { 'itemId' => params[:item_id], 'serviceContactId' => 71_221, 'memberContactId' => 70_432, 'makeId' => 23,
                        'modelId' => 55_498, 'isOrder' => false, 'authentication' => { 'cvpd' => 'test_CVPD' } }

      allow_any_instance_of(TcvCoreApi::Request).to receive(:send)
        .and_return(instance_double(TcvCoreApi::Response, success?: true, body: response_body, status: 200))

      response = subject.create
      expect(response).to eq([response_body, true])
    end
  end
end
