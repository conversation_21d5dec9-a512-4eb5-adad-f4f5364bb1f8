require 'rails_helper'

RSpec.describe Transactions::TempOffers, type: :service do
  let(:params) do
    {
      comment: 'I would like to get a quote.\n',
      item_id: 561_665,
      country_number: 704,
      port_id: 21_850,
      cookies: {
        source: 'test_source',
        source3: 'test_source3',
        guid: SecureRandom.uuid.delete('-').upcase,
        cookie_cvpd: 'test_cookie_cvpd'
      },
      temporary_contact_id: 1,
      hs: SecureRandom.uuid.delete('-').upcase
    }
  end

  subject { described_class.new(params) }

  describe '#initialize' do
    it 'initializes with correct parameters' do
      expect(subject.instance_variable_get(:@comment)).to eq(params[:comment])
      expect(subject.instance_variable_get(:@item_id)).to eq(params[:item_id])
      expect(subject.instance_variable_get(:@ship_estimated_country_number)).to eq(params[:country_number])
      expect(subject.instance_variable_get(:@ship_estimated_port_id)).to eq(params[:port_id])
      expect(subject.instance_variable_get(:@source)).to eq(params.dig(:cookies, :source))
      expect(subject.instance_variable_get(:@source3)).to eq(params.dig(:cookies, :source3))
      expect(subject.instance_variable_get(:@guid)).to eq(params.dig(:cookies, :guid))
      expect(subject.instance_variable_get(:@cookie_cvpd)).to eq(params.dig(:cookies, :cookie_cvpd))
      expect(subject.instance_variable_get(:@temporary_contact_id)).to eq(params[:temporary_contact_id])
      expect(subject.instance_variable_get(:@hs)).to eq(params[:hs])
    end
  end

  describe '#url' do
    it 'returns the correct url' do
      expected_url = "#{Settings.tcv_core_api.domain}#{Settings.tcv_core_api.v1_paths.offer.create_temp}"
      expect(subject.send(:url)).to eq(expected_url)
    end
  end

  describe '#params' do
    it 'returns the correct params' do
      expected_params = {
        'itemId' => params[:item_id],
        'dischargePortCountryNumber' => params[:country_number],
        'dischargePortId' => params[:port_id],
        'guid' => params.dig(:cookies, :guid),
        'src' => params.dig(:cookies, :source),
        'src3' => params.dig(:cookies, :source3),
        'comment' => params[:comment]
      }.to_json
      expect(subject.send(:params)).to eq(expected_params)
    end
  end

  describe '#get' do
    it 'when @cookie_cvpd is incorrect' do
      response = subject.get
      expect(response).to eq([{}, false])
    end

    it 'when get successfuly' do
      response_body = { 'comment' => params[:comment], 'dischargePortCountryNumber' => params[:country_number],
                        'dischargePortId' => params[:port_id], 'itemId' => params[:item_id], 'createDate' => Time.now,
                        'src' => params.dig(:cookies, :source), 'src2' => nil, 'src3' => params.dig(:cookies, :source3),
                        'authentication' => { 'cvpd' => 'test_CVPD' } }

      allow_any_instance_of(TcvCoreApi::Request).to receive(:send)
        .and_return(instance_double(TcvCoreApi::Response, success?: true, body: response_body))

      response = subject.get
      expect(response).to eq([response_body, true])
    end
  end
end
