require 'rails_helper'

RSpec.describe Offers::SaveReferrerService, type: :service do
  describe '#call' do
    let(:domain) { 'example.com' }
    let(:referrer) { 'https://example.com' }
    let(:service_contact_id) { 1 }
    let(:cookies) { { 'CVPD' => 'cvpd_cookie_value', 'source' => 'src1', 'source2' => 'src2', 'source3' => 'src3' } }
    let(:subject) { described_class.new(referrer, service_contact_id, cookies) }

    before do
      allow(AServiceContact).to receive(:create!)
      allow_any_instance_of(TcvCoreApi::Request).to receive(:send)
    end

    context 'when URI.parse fails' do
      it 'logs the error' do
        allow(URI).to receive(:parse).and_raise(StandardError.new('Error message'))
        expect(subject.call['value']).to eq(nil)
      end
    end

    it 'creates a new AServiceContact' do
      expect(AServiceContact).to receive(:create!) do |data|
        expect(data[:service_contact_id]).to eq(service_contact_id)
        expect(data[:referrer_type]).to eq(domain)
        expect(data[:src]).to eq('src1')
        expect(data[:src_2]).to eq('src2')
        expect(data[:src_3]).to eq('src3')
      end

      subject.call
    end

    context 'when AServiceContact creation fails' do
      it 'logs the error' do
        allow(AServiceContact).to receive(:create!).and_raise(StandardError.new('Error message'))

        expect(subject.instance_variable_get('@logger')).to receive(:error).with('Error message')
        subject.call
      end
    end

    it 'sends a request to TcvCoreApi' do
      expect_any_instance_of(TcvCoreApi::Request).to receive(:send) do |request|
        expect(request.instance_variable_get('@url')).to eq(Offers::SaveReferrerService::ADD_SERVICE_CONTACT_URL)
        expect(request.instance_variable_get('@method')).to eq('POST')
        expect(request.instance_variable_get('@params')).to eq({
          serviceContactId: service_contact_id,
          refererType: 'example.com',
          src: 'src1',
          src2: 'src2',
          src3: 'src3'
        }.to_json)
      end

      subject.call
    end

    context 'when TcvCoreApi request fails' do
      it 'logs the error' do
        request_double = instance_double(TcvCoreApi::Request)
        allow(TcvCoreApi::Request).to receive(:new).and_return(request_double)
        allow(request_double).to receive(:send).and_raise(StandardError.new('Error message'))
        expect(subject.instance_variable_get('@logger')).to receive(:error).with('Error message')
        subject.call
      end
    end

    it 'sets the referrer to cookies' do
      subject.call

      expect(subject.instance_variable_get('@cookies')[:referrer]).to eq({ value: domain, expires: 30.days })
    end
  end
end
