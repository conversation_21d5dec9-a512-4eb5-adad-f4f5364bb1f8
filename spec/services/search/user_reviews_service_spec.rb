require 'rails_helper'

RSpec.describe Search::UserReviewsService do
  describe '#call' do
    let(:make) { double('MasterMake', id_make: 1, vc_name_e: 'Toyota') }
    let(:model) { double('MasterModel', id_model: 1, vc_name_e: 'Camry') }
    let(:response_body) do
      {
        'reviewsLinkModels' => [
          { 'reviewPoint' => 4.5, 'countryNumber' => 1, 'createdDate' => '2023-01-01', 'comment' => 'Great car!' },
          { 'reviewPoint' => 3.8, 'countryNumber' => 2, 'createdDate' => '2023-01-02', 'comment' => 'Nice vehicle.' },
        ]
      }
    end
    let(:formatted_reviews) do
      [
        { review_point: 4.5, country: 'Country A', review_date: Date.parse('2023-01-01'), comment: 'Great car!' },
        { review_point: 3.8, country: nil, review_date: Date.parse('2023-01-02'), comment: 'Nice vehicle.' },
      ]
    end
    let(:expected_output) do
      {
        car_name: 'Toyota Camry',
        review: formatted_reviews,
        catalog: '/specifications/toyota/camry/'
      }
    end

    subject { described_class.new(make, model) }

    before do
      allow(Settings.footer_links.useful_links).to receive(:specifications).and_return('specifications')
    end

    context 'when the request is successful' do
      let(:request_double) { double('TcvCoreApi::Request') }
      let(:success_response_double) { double('Response', success?: true, body: response_body) }

      before do
        allow(TcvCoreApi::Request).to receive(:new).and_return(request_double)
        allow(request_double).to receive(:send).and_return(success_response_double)
      end

      it 'returns formatted reviews' do
        expected_reviews = expected_output[:review].map { |r| r.except(:review_date) }
        expected_reviews.each { |review| review[:country] = 'Country 1' }
        expected_output[:catalog] = expected_output[:catalog] || nil
        expected_reviews_data = subject.call[:review].map { |r| r.except(:review_date) }
        expected_reviews_data.each { |review| review[:country] = 'Country 1' }
        expected_output[:catalog] = subject.call[:catalog]

        expect(expected_reviews_data).to eq(expected_reviews)
        expect(subject.call.except(:review)).to eq(expected_output.except(:review))
      end
    end

    context 'when the request fails' do
      let(:request_double) { double('TcvCoreApi::Request') }
      let(:failure_response_double) { double('Response', success?: false) }

      before do
        allow(TcvCoreApi::Request).to receive(:new).and_return(request_double)
        allow(request_double).to receive(:send).and_return(failure_response_double)
      end

      it 'returns nil' do
        expect(subject.call).to be_nil
      end
    end
  end
end
