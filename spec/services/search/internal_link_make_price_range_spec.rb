require 'rails_helper'

RSpec.describe Search::InternalLink::MakePriceRange do
  let(:make) { instance_double('MasterMake', vc_name_e: 'Toyota', id_make: 1) }
  let(:price_params) { { prcf: 1_000, prct: 1_500 } }
  let(:price_range_query) { price_params.to_query }
  let(:price_range) { { query: price_range_query, text: 'US$1,000-1,500', value: '[1000 TO 1500]', master_info_id: 3 } }
  let(:solr_facet_service) { instance_double(Solr::FacetService) }
  let(:popular_service) { instance_double(Search::GetPopularForAllMakeService) }
  let(:body_styles) { [{ id: 6, text: 'Sedan', query: 'bsty=6' }] }
  let(:options) { [{ text: 'Left Hand Drive', query: 'st=13', solr_query: 'SteeringID:13' }] }
  let(:price_ranges) { [{ text: 'US$500-1,000', query: 'prcf=500&prct=1000', value: '[500 TO 1000]' }] }
  let(:popular_models) { [instance_double('PopularModel', maker_nm: 'Toyota', model_nm: 'Corolla', maker_id: 1, model_id: 101)] }

  subject(:service) { described_class.new(make, price_params) }

  before do
    allow(MasterInfo::InternalLink::PriceRange).to receive(:find_by).with(query: price_range_query).and_return(price_range)
    allow(MasterInfo::InternalLink::PriceRange).to receive(:all).and_return(price_ranges)
    allow(MasterInfo::InternalLink::BodyStyle).to receive(:all).and_return(body_styles)
    allow(MasterInfo::InternalLink::OtherCategory).to receive(:all).and_return(options)
    allow(Solr::FacetService).to receive(:new).and_return(solr_facet_service)
    allow(Search::GetPopularForAllMakeService).to receive(:new).and_return(popular_service)
    allow(popular_service).to receive(:list_top_model_for_make_in_price_range).with(1, 3).and_return(popular_models)
    allow(Rails.cache).to receive(:fetch).and_yield
  end

  describe '#initialize' do
    it 'sets up instance variables correctly' do
      expect(service.instance_variable_get(:@make)).to eq(make)
      expect(service.instance_variable_get(:@make_name)).to eq('Toyota')
      expect(service.instance_variable_get(:@make_id)).to eq(1)
      expect(service.instance_variable_get(:@price_range_query)).to eq(price_range_query)
    end
  end

  describe '#call' do
    context 'when make and price range are valid' do
      let(:facet_counts) do
        {
          'popular_model_0' => 7,
          'price_range_0' => 5,
          'body_style_0' => 3,
          'category_0' => 2
        }
      end

      before do
        allow(solr_facet_service).to receive(:call_facet).and_return('facet_counts' => { 'facet_queries' => facet_counts })
      end

      it 'caches the result' do
        expect(Rails.cache).to receive(:fetch).with("internal_links_make_price_range_Toyota_#{price_range_query}", expires_in: 1.hour)
        service.call
      end

      it 'returns array of sections with data' do
        result = service.call

        expect(result).to be_an(Array)
        expect(result.length).to eq(4)

        # Check popular models section
        popular_section = result.find { |r| r[:title].include?('Popular Models') }
        expect(popular_section).not_to be_nil
        expect(popular_section[:title]).to eq('Toyota x US$1,000-1,500 Popular Models')

        # Check varying prices section
        prices_section = result.find { |r| r[:title] == 'Same Brand, Varying Prices' }
        expect(prices_section).not_to be_nil

        # Check varying body type section
        body_type_section = result.find { |r| r[:title] == 'Same Brand and Prices, Varying Body Type' }
        expect(body_type_section).not_to be_nil

        # Check varying category section
        category_section = result.find { |r| r[:title] == 'Same Brand and Price Tier, Varying Category' }
        expect(category_section).not_to be_nil
      end
    end

    context 'when price range is not found' do
      before do
        allow(MasterInfo::InternalLink::PriceRange).to receive(:find_by).and_return(nil)
      end

      it 'returns nil' do
        expect(service.call).to be_nil
      end
    end

    context 'when make is not present' do
      let(:make) { nil }

      it 'raises a NoMethodError on initialization' do
        expect { described_class.new(make, price_params) }.to raise_error(NoMethodError)
      end
    end
  end

  describe 'private methods' do
    describe '#handle_popular_model' do
      before do
        allow(service).to receive(:fetch_condition_counts).and_return('popular_model_0' => 7)
      end

      it 'builds popular model conditions' do
        result = service.send(:handle_popular_model)
        expect(result).to eq(
          title: 'Toyota x US$1,000-1,500 Popular Models',
          condition: [[["used_car/toyota/corolla/?#{price_range_query}", 'Toyota Corolla'], 7]],
        )
      end

      context 'when no popular models' do
        before do
          allow(popular_service).to receive(:list_top_model_for_make_in_price_range).with(1, 3).and_return([])
        end

        it 'returns nil' do
          result = service.send(:handle_popular_model)
          expect(result).to be_nil
        end
      end
    end

    describe '#handle_varying_prices' do
      before do
        allow(service).to receive(:fetch_condition_counts).and_return('price_range_0' => 5)
      end

      it 'builds varying prices conditions' do
        result = service.send(:handle_varying_prices)
        expect(result).to eq(
          title: 'Same Brand, Varying Prices',
          condition: [[['used_car/toyota/all/?prcf=500&prct=1000', 'Toyota x US$500-1,000'], 5]],
        )
      end
    end

    describe '#handle_varying_body_type' do
      before do
        allow(service).to receive(:fetch_condition_counts).and_return('body_style_0' => 3)
      end

      it 'builds varying body type conditions' do
        result = service.send(:handle_varying_body_type)
        expect(result).to eq(
          title: 'Same Brand and Prices, Varying Body Type',
          condition: [[["used_car/toyota/all/?#{price_range_query}&bsty=6", 'Toyota x US$1,000-1,500 x Sedan'], 3]],
        )
      end
    end

    describe '#handle_varying_category' do
      before do
        allow(service).to receive(:fetch_condition_counts).and_return('category_0' => 2)
      end

      it 'builds varying category conditions' do
        result = service.send(:handle_varying_category)
        expect(result).to eq(
          title: 'Same Brand and Price Tier, Varying Category',
          condition: [[["used_car/toyota/all/?#{price_range_query}&st=13", 'Toyota x US$1,000-1,500 x Left Hand Drive'], 2]],
        )
      end
    end

    describe '#combined_facet_queries' do
      it 'builds correct facet queries' do
        result = service.send(:combined_facet_queries)

        expect(result).to include('popular_model_0' => 'MakeID:1 AND ModelID:101 AND Price:[1000 TO 1500]')
        expect(result).to include('price_range_0' => 'MakeID:1 AND Price:[500 TO 1000]')
        expect(result).to include('body_style_0' => 'MakeID:1 AND BodyStyle1:6 AND Price:[1000 TO 1500]')
        expect(result).to include('category_0' => 'MakeID:1 AND SteeringID:13 AND Price:[1000 TO 1500]')
      end
    end
  end
end
