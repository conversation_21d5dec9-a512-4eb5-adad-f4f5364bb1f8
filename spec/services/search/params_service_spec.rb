require 'rails_helper'

RSpec.describe Search::ParamsService do
  describe '#redirect_data' do
    let(:params) { ActionController::Parameters.new({ make: 'Toyota', model: 'Camry', prcf: 1500, prct: 2000 }) }
    let(:service) { described_class.new(params) }

    context 'when params are valid' do
      it 'returns the correct destination URL' do
        expected_url = '/used_car/Toyota/Camry/?prcf=1500&prct=2000'
        expect(service.redirect_data).to eq(expected_url)
      end
    end

    context 'when params are invalid' do
      it 'returns the destination URL with nil make and model' do
        params[:make] = '0'
        params[:model] = '0'
        expected_url = '/used_car/all/all/?prcf=1500&prct=2000'
        expect(service.redirect_data).to eq(expected_url)
      end
    end
  end

  describe '#process_for_body_style' do
    let(:service) { described_class.new }

    context 'when sty_list is nil' do
      it 'returns nil' do
        expect(service.process_for_body_style(nil)).to be_nil
      end
    end

    context 'when sty_list is a string' do
      it 'returns an array' do
        sty_list = '1.2*2.1'
        expect(service.process_for_body_style(sty_list)).to eq(['1.2', '2.1'])
      end
    end
  end

  describe 'private method' do
    let(:service) { described_class.new(params) }
    let(:params) { ActionController::Parameters.new({ make: 'Toyota', model: 'Camry', prcf: 1500, prct: 2000 }) }

    describe '#param_mapping' do
      context 'when params are valid' do
        it 'returns the mapped parameters' do
          result = service.send(:param_mapping)
          expect(result).to eq({ 'bsty' => nil, 'ecls' => nil, 'fd' => nil, 'fues' => nil, 'prcf' => 1500, 'prct' => 2000, 'tmns' => nil,
                                 'uid' => nil })
        end
      end
    end

    describe '#reject_blank_and_modify_format' do
      let(:service) { described_class.new }

      context 'when input is an array' do
        it 'removes blank items and join the array with "*"' do
          input_array = ['1', '', '2', nil, '3']
          expected_result = '1*2**3'
          result = service.send(:reject_blank_and_modify_format, input_array)
          expect(result).to eq(expected_result)
        end

        it 'returns nil if all items are blank' do
          input_array = ['', nil]
          result = service.send(:reject_blank_and_modify_format, input_array)
          expect(result).to eq('')
        end
      end

      context 'when input is not an array' do
        it 'returns the input without modification' do
          input = '1*2*3'
          result = service.send(:reject_blank_and_modify_format, input)
          expect(result).to eq(input)
        end
      end
    end

    describe '#validate_data' do
      let(:service) { described_class.new }

      context 'when key is in ARRAY_FIELDS' do
        it 'returns true if value is present and numeric' do
          result = service.send(:validate_data, 'tmns', 1500)
          expect(result).to eq(true)
        end

        it 'returns false if value is not present' do
          result = service.send(:validate_data, 'tmns', nil)
          expect(result).to eq(false)
        end

        it 'returns false if value is not numeric' do
          result = service.send(:validate_data, 'tmns', 'invalid')
          expect(result).to eq(false)
        end
      end

      context 'when key is in INT_VALUE_LIST' do
        it 'returns true if value is present and numeric' do
          result = service.send(:validate_data, 'test', 1500)
          expect(result).to eq(true)
        end

        it 'returns false if value is not present' do
          result = service.send(:validate_data, 'test', nil)
          expect(result).to eq(false)
        end
      end

      context 'when key is neither in INT_VALUE_LIST nor in ARRAY_FIELDS' do
        it 'returns true if value is present' do
          result = service.send(:validate_data, 'some_key', 'some_value')
          expect(result).to eq(true)
        end

        it 'returns false if value is not present' do
          result = service.send(:validate_data, 'some_key', nil)
          expect(result).to eq(false)
        end
      end
    end
  end
end
