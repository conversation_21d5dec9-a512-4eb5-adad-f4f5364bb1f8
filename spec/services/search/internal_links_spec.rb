require 'rails_helper'

RSpec.describe Search::InternalLinks do
  describe '#call' do
    let(:make_id) { 1 }
    let(:model_id) { 2 }
    let(:user_country_code) { 'US' }
    let(:smart_phone) { true }
    let(:make) { double('MasterMake', id_make: make_id, vc_name_e: 'MakeName') }
    let(:model) { double('MasterModel', id_model: model_id, vc_name_e: 'ModelName') }

    let(:solr_facet_response) do
      {
        'facet_counts' => {
          'facet_queries' => {
            'condition1:1' => 10,
            'condition2:2' => 20,
            'condition3:3' => 15,
            'condition4:4' => 25,
            'condition5:5' => 30
          },
          'facet_pivot' => {
            'ModelYear' => [
              { 'value' => 2023, 'count' => 100 },
              { 'value' => 2024, 'count' => 50 },
            ]
          }
        }
      }
    end

    before do
      allow(Solr::FacetService).to receive(:new).and_return(
        double('Solr::FacetService', call_facet: solr_facet_response),
      )

      allow(MasterInfo::InternalLink::PriceRange).to receive(:all).and_return([
                                                                                double('PriceRange',
                                                                                       value: '[500 TO 1000]',
                                                                                       text: 'US$500-1,000',
                                                                                       query: 'prcf=500&prct=1000',
                                                                                       additional_condition: ''),
                                                                              ])

      allow(MasterInfo::InternalLinkCondition).to receive(:all).and_return([
                                                                             double('InternalLinkCondition',
                                                                                    field: 'DriveTypeID',
                                                                                    value: 10,
                                                                                    text: '4WD',
                                                                                    query: 'dr=10',
                                                                                    additional_condition: ''),
                                                                           ])
      allow(MasterInfo::InternalLinkAllMakeCondition).to receive(:last)
        .with(described_class::NUMBER_OF_RARE_CAR_CONDITION)
        .and_return([])

      allow(MasterInfo::InternalLink::BodyStyle).to receive(:all).and_return([
                                                                               double('BodyStyle',
                                                                                      id: 7,
                                                                                      text: 'SUV',
                                                                                      query: 'bsty=7'),
                                                                             ])
      allow(Search::RecommendMakeModel).to receive(:new).and_return(
        double('RecommendMakeModel', call: []),
      )

      allow(MasterModel).to receive(:where).and_return([])
    end

    it 'returns an array of conditions with counts' do
      subject = described_class.new(make, model, user_country_code, smart_phone).call

      expect(subject).to be_an(Array)
      expect(subject.size).to eq(5)

      subject.each do |section|
        expect(section).to include(:title, :condition)
        expect(section[:title]).to be_a(String)
        expect(section[:condition]).to be_an(Array)
      end
    end

    it 'returns expected data structure' do
      subject = described_class.new(make, model, user_country_code, smart_phone).call

      expect(subject).to eq([
                              {
                                condition: [[['prcf=500&prct=1000', 'US$500-1,000'], 10]],
                                title: 'Car Price (FOB), often combined'
                              },
                              {
                                condition: [[['dr=10', '4WD'], 10]],
                                title: 'Popular Conditions, often combined'
                              },
                              {
                                condition: [[['fid=2024&jid=2024', '2024 (50)', 2024], 50],
                                            [['fid=2023&jid=2023', '2023 (100)', 2023], 100]],
                                title: 'Popular Registration Years, often combined'
                              },
                              {
                                condition: [
                                  [['fid=2024&jid=2024', 'MakeName  (10)'], 10, nil],
                                  [['fid=2024&jid=2024', 'MakeName  (20)'], 20, nil],
                                  [['fid=2024&jid=2024', 'MakeName  (15)'], 15, nil],
                                ],
                                title: "Same Registration Year's recommended model"
                              },
                              {
                                condition: [[['used_car/makename/all/?bsty=7', 'SUV', :url], 10]],
                                title: 'Same Make x BodyStyles'
                              },
                            ])
    end
  end
end
