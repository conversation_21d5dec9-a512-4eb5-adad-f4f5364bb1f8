require 'rails_helper'

RSpec.describe Search::ValidateParamsService do
  describe '#valid_params' do
    let(:params) { { make: 'Toyota', prcf: 1000, prct: 2000 } }
    let(:car_counter_instance) { double }

    it 'returns false for invalid parameters' do
      allow(Search::MasterDataService).to receive(:master_data).and_return({ fob_options_data: [[3000, 'Option 3'], [4000, 'Option 4']] })

      result = described_class.valid_params(params, car_counter_instance)

      expect(result).to eq(false)
    end
  end

  describe 'private methods' do
    describe '#check_integer?' do
      context 'when the input is a valid integer string' do
        it 'returns true' do
          result = described_class.send(:check_integer?, '123')
          expect(result).to eq(true)
        end
      end

      context 'when the input is not a valid integer string' do
        it 'returns false' do
          result = described_class.send(:check_integer?, 'abc')
          expect(result).to eq(false)
        end
      end
    end
  end

  describe '#whitelist_params' do
    context 'when all parameters are valid' do
      let(:params) { { 'prcf' => '1000', 'prct' => '2000', 'make' => 'Toyota', 'model' => 'Camry' } }

      it 'returns true' do
        described_class.instance_variable_set(:@params, params)
        result = described_class.send(:whitelist_params)
        expect(result).to eq(true)
      end
    end

    context 'when some parameters are invalid' do
      let(:params) { { 'prcf' => '1000', 'prct' => '2000', 'make' => 'Toyota', 'invalid' => 'Camry' } }

      it 'returns false' do
        described_class.instance_variable_set(:@params, params)
        result = described_class.send(:whitelist_params)
        expect(result).to eq(false)
      end
    end
  end

  describe '#check_rfc' do
    context 'when rfc parameter is nil' do
      let(:params) { { rfc: nil } }

      it 'returns true' do
        described_class.instance_variable_set(:@params, params)
        result = described_class.send(:check_rfc)
        expect(result).to eq(true)
      end
    end

    context 'when rfc parameter is "0"' do
      let(:params) { { rfc: '0' } }

      it 'returns true' do
        described_class.instance_variable_set(:@params, params)
        result = described_class.send(:check_rfc)
        expect(result).to eq(true)
      end
    end

    context 'when rfc parameter is "1"' do
      let(:params) { { rfc: '1' } }

      it 'returns true' do
        described_class.instance_variable_set(:@params, params)
        result = described_class.send(:check_rfc)
        expect(result).to eq(true)
      end
    end

    context 'when rfc parameter is neither "0" nor "1"' do
      let(:params) { { rfc: '2' } }

      it 'returns false' do
        described_class.instance_variable_set(:@params, params)
        result = described_class.send(:check_rfc)
        expect(result).to eq(false)
      end
    end
  end

  describe '#check_make' do
    context 'when make parameter is nil' do
      let(:params) { { make: nil } }

      it 'returns true' do
        described_class.instance_variable_set(:@params, params)
        result = described_class.send(:check_make)
        expect(result).to eq(true)
      end
    end

    context 'when make parameter is "all"' do
      let(:params) { { make: 'all' } }

      it 'returns true' do
        described_class.instance_variable_set(:@params, params)
        result = described_class.send(:check_make)
        expect(result).to eq(true)
      end
    end

    context 'when make parameter is valid' do
      let(:make) { 'toyota' }
      let(:params) { { make: make } }

      before do
        allow(described_class).to receive(:master_make_name_list).and_return(%w[toyota honda])
      end

      it 'returns true' do
        described_class.instance_variable_set(:@params, params)
        result = described_class.send(:check_make)
        expect(result).to eq(true)
      end
    end

    context 'when make parameter is invalid' do
      let(:make) { 'invalid_make' }
      let(:params) { { make: make } }

      before do
        allow(described_class).to receive(:master_make_name_list).and_return(%w[toyota honda])
      end

      it 'returns false' do
        described_class.instance_variable_set(:@params, params)
        result = described_class.send(:check_make)
        expect(result).to eq(false)
      end
    end
  end

  describe '#check_model' do
    context 'when model parameter is nil' do
      let(:params) { { model: nil } }

      it 'returns true' do
        described_class.instance_variable_set(:@params, params)
        result = described_class.send(:check_model)
        expect(result).to eq(true)
      end
    end

    context 'when model parameter is "all"' do
      let(:params) { { model: 'all' } }

      it 'returns true' do
        described_class.instance_variable_set(:@params, params)
        result = described_class.send(:check_model)
        expect(result).to eq(true)
      end
    end

    context 'when model parameter is valid' do
      let(:make) { 'toyota' }
      let(:model) { 'corolla' }
      let(:params) { { make: make, model: model } }

      before do
        allow(MasterMake).to receive_message_chain(:joins, :where, :take, :present?).and_return(true)
      end

      it 'returns true' do
        described_class.instance_variable_set(:@params, params)
        result = described_class.send(:check_model)
        expect(result).to eq(true)
      end
    end

    context 'when model parameter is invalid' do
      let(:make) { 'invalid_make' }
      let(:model) { 'invalid_model' }
      let(:params) { { make: make, model: model } }

      before do
        allow(MasterMake).to receive_message_chain(:joins, :where, :take, :present?).and_return(false)
      end

      it 'returns false' do
        described_class.instance_variable_set(:@params, params)
        result = described_class.send(:check_model)
        expect(result).to eq(false)
      end
    end
  end

  describe 'check_prcf, check_prct, check_fid, check_jid, check_smo, check_emo, check_mimn, check_mimx,
            check_sds, check_eds, check_ac, check_st, check_dr, check_do' do
    let(:default_master_data) do
      {
        fob_options_data: [['Option1', 1000], ['Option2', 2000]],
        regis_year: [[2010], [2011], [2012]],
        regis_month: [[1], [2], [3]],
        mileage_options: [[10_000], [20_000]],
        engine_capacity_options_data: [[1000], [2000]],
        accident_options: [[0], [1]],
        steering_options: [[0], [1]],
        driver_type_options: [[0], [1]],
        any_door_options: [[0], [1]]
      }
    end

    before do
      allow(described_class).to receive(:default_master_data).and_return(default_master_data)
    end

    shared_examples 'check_method' do |method_name, key_params, key_default_master_data|
      context "when #{key_params} parameter is nil" do
        let(:params) { { key_params => nil } }

        it 'returns true' do
          described_class.instance_variable_set(:@params, params)
          result = described_class.send(method_name)
          expect(result).to eq(true)
        end
      end

      context "when #{key_params} parameter is not an integer" do
        let(:params) { { key_params => 'not_an_integer' } }

        it 'returns false' do
          described_class.instance_variable_set(:@params, params)
          result = described_class.send(method_name)
          expect(result).to eq(false)
        end
      end

      context "when #{key_params} parameter is a valid option" do
        let(:valid_option) { default_master_data[key_default_master_data].first.last.to_s }
        let(:params) { { key_params => valid_option } }

        it 'returns true' do
          described_class.instance_variable_set(:@params, params)
          result = described_class.send(method_name)
          expect(result).to eq(true)
        end
      end

      context "when #{key_params} parameter is an invalid option" do
        let(:invalid_option) { (default_master_data[key_default_master_data].last.last + 100).to_s }
        let(:params) { { key_params => invalid_option } }

        it 'returns false' do
          described_class.instance_variable_set(:@params, params)
          result = described_class.send(method_name)
          expect(result).to eq(false)
        end
      end
    end

    describe '#check_prcf' do
      include_examples 'check_method', :check_prcf, :prcf, :fob_options_data
    end

    describe '#check_prct' do
      include_examples 'check_method', :check_prct, :prct, :fob_options_data
    end

    describe '#check_fid' do
      include_examples 'check_method', :check_fid, :fid, :regis_year
    end

    describe '#check_jid' do
      include_examples 'check_method', :check_jid, :jid, :regis_year
    end

    describe '#check_smo' do
      include_examples 'check_method', :check_smo, :smo, :regis_month
    end

    describe '#check_emo' do
      include_examples 'check_method', :check_emo, :emo, :regis_month
    end

    describe '#check_mimn' do
      include_examples 'check_method', :check_mimn, :mimn, :mileage_options
    end

    describe '#check_mimx' do
      include_examples 'check_method', :check_mimx, :mimx, :mileage_options
    end

    describe '#check_sds' do
      include_examples 'check_method', :check_sds, :sds, :engine_capacity_options_data
    end

    describe '#check_eds' do
      include_examples 'check_method', :check_eds, :eds, :engine_capacity_options_data
    end

    describe '#check_ac' do
      include_examples 'check_method', :check_ac, :ac, :accident_options
    end

    describe '#check_st' do
      include_examples 'check_method', :check_st, :st, :steering_options
    end

    describe '#check_dr' do
      include_examples 'check_method', :check_dr, :dr, :driver_type_options
    end

    describe '#check_do' do
      include_examples 'check_method', :check_do, :do, :any_door_options
    end
  end

  describe '#check_nw and #check_spp' do
    shared_examples 'boolean parameter check' do |key_params|
      context "when #{key_params} parameter is nil" do
        let(:params) { { key_params => nil } }

        it 'returns true' do
          described_class.instance_variable_set(:@params, params)
          result = described_class.send(method_name)
          expect(result).to eq(true)
        end
      end

      context "when #{key_params} parameter is not a boolean" do
        let(:params) { { key_params => 'not_a_boolean' } }

        it 'returns false' do
          described_class.instance_variable_set(:@params, params)
          result = described_class.send(method_name)
          expect(result).to eq(false)
        end
      end

      context "when #{key_params} parameter is a valid boolean option" do
        let(:params) { { key_params => '1' } }

        it 'returns true' do
          described_class.instance_variable_set(:@params, params)
          result = described_class.send(method_name)
          expect(result).to eq(true)
        end
      end

      context "when #{key_params} parameter is an invalid boolean option" do
        let(:params) { { key_params => '999' } }

        it 'returns false' do
          described_class.instance_variable_set(:@params, params)
          result = described_class.send(method_name)
          expect(result).to eq(false)
        end
      end
    end

    describe '#check_nw' do
      it_behaves_like 'boolean parameter check', :nw do
        let(:method_name) { :check_nw }
      end
    end

    describe '#check_spp' do
      it_behaves_like 'boolean parameter check', :spp do
        let(:method_name) { :check_spp }
      end
    end
  end

  describe '#check_tmns, #check_fues and #check_ecls' do
    shared_examples 'check_option_method' do |method_name, key_params|
      let(:default_master_data) do
        {
          transmission_options: [[1], [2], [3]],
          fuel_type_options: [[1], [2], [3]],
          color_options: [[1], [2], [3]]
        }
      end

      before do
        allow(described_class).to receive(:default_master_data).and_return(default_master_data)
      end

      context "when #{key_params} parameter is nil" do
        let(:params) { { key_params => nil } }

        it 'returns true' do
          described_class.instance_variable_set(:@params, params)
          result = described_class.send(method_name)
          expect(result).to eq(true)
        end
      end

      context "when #{key_params} parameter contains invalid integer option" do
        let(:params) { { key_params => 'text*2*3' } }

        it 'returns true' do
          described_class.instance_variable_set(:@params, params)
          result = described_class.send(method_name)
          expect(result).to eq(false)
        end
      end

      context "when #{key_params} parameter contains valid options" do
        let(:params) { { key_params => '1*2*3' } }

        it 'returns true' do
          described_class.instance_variable_set(:@params, params)
          result = described_class.send(method_name)
          expect(result).to eq(true)
        end
      end

      context "when #{key_params} parameter contains invalid options" do
        let(:params) { { key_params => '4*5*6' } }

        it 'returns false' do
          described_class.instance_variable_set(:@params, params)
          result = described_class.send(method_name)
          expect(result).to eq(false)
        end
      end
    end

    describe 'check methods' do
      include_examples 'check_option_method', :check_tmns, :tmns
      include_examples 'check_option_method', :check_fues, :fues
      include_examples 'check_option_method', :check_ecls, :ecls
    end
  end

  describe '#check_bsty?' do
    let(:default_master_data) { { body_style_options: [[1], [2], [3]], secondary_body_style_options: [[1, 0, 0], [2, 0, 0], [3, 0, 0]] } }
    let(:params) { { bsty: '1*2.0*3.0' } }

    before do
      allow(described_class).to receive(:default_master_data).and_return(default_master_data)
      described_class.instance_variable_set(:@params, params)
    end

    context 'when bsty parameter is nil' do
      let(:params) { { bsty: nil } }

      it 'returns true' do
        result = described_class.send(:check_bsty?)
        expect(result).to eq(true)
      end
    end

    context 'when bsty parameter contains valid options' do
      it 'returns true' do
        result = described_class.send(:check_bsty?)
        expect(result).to eq(true)
      end
    end

    context 'when bsty parameter contains invalid options' do
      let(:params) { { bsty: '4*5.0*6.0' } }

      it 'returns false' do
        result = described_class.send(:check_bsty?)
        expect(result).to eq(false)
      end
    end

    context 'when bsty parameter contains invalid integer option' do
      let(:params) { { bsty: 'not_an_integer*2.0*3.0' } }

      it 'returns false' do
        result = described_class.send(:check_bsty?)
        expect(result).to eq(false)
      end
    end

    context 'when bsty parameter contains invalid float option' do
      let(:params) { { bsty: '1*not_a_float*3.0' } }

      it 'returns false' do
        result = described_class.send(:check_bsty?)
        expect(result).to eq(false)
      end
    end

    context 'when bsty parameter contains invalid combination of options' do
      let(:params) { { bsty: '1*2.5*4.0' } }

      it 'returns false' do
        result = described_class.send(:check_bsty?)
        expect(result).to eq(false)
      end
    end

    context 'when bsty parameter contains all valid options are integer' do
      let(:params) { { bsty: '1*2*3' } }
      # let(:default_master_data) { { body_style_options: [[1], [2], [3]], secondary_body_style_options: [[1, 0, 0], [2, 5, 2], [3, 0, 0]] } }

      it 'returns true' do
        allow(described_class).to receive(:default_master_data).and_return(default_master_data)
        result = described_class.send(:check_bsty?)
        expect(result).to eq(true)
      end
    end
  end

  describe '#check_co' do
    context 'when co parameter is nil' do
      let(:params) { { co: nil } }

      it 'returns true' do
        described_class.instance_variable_set(:@params, params)
        result = described_class.send(:check_co)
        expect(result).to eq(true)
      end
    end

    context 'when co parameter is not an integer' do
      let(:params) { { co: 'not_an_integer' } }

      it 'returns false' do
        described_class.instance_variable_set(:@params, params)
        result = described_class.send(:check_co)
        expect(result).to eq(false)
      end
    end

    context 'when co parameter is a valid option' do
      let(:params) { { co: MCountry.first.number.to_s } }

      it 'returns true' do
        described_class.instance_variable_set(:@params, params)
        result = described_class.send(:check_co)
        expect(result).to eq(true)
      end
    end

    context 'when co parameter is an invalid option' do
      let(:params) { { co: '999' } }

      it 'returns false' do
        described_class.instance_variable_set(:@params, params)
        result = described_class.send(:check_co)
        expect(result).to eq(false)
      end
    end
  end

  describe '#default_master_data' do
    let(:car_counter_instance) { double('CarCounterInstance') }
    let(:master_data) do
      {
        regis_year: [2010, 2011, 2012],
        regis_month: [1, 2, 3],
        mileage_options: [10_000, 20_000],
        engine_capacity_options_data: [1000, 2000],
        accident_options: [0, 1],
        steering_options: [0, 1]
      }
    end

    before do
      allow(Search::MasterDataService).to receive(:master_data).and_return(master_data)
      described_class.instance_variable_set(:@default_master_data, nil)
    end

    it 'returns the default master data' do
      described_class.instance_variable_set(:@car_counter_instance, car_counter_instance)
      result = described_class.send(:default_master_data)
      expect(result).to eq(master_data)
    end
  end

  describe '#master_make_name_list' do
    let(:master_makes) { %w[Make1 Make2 Make3] }

    before do
      allow(MasterMake).to receive(:pluck).and_return(master_makes)
    end

    it 'returns a list of master make names' do
      result = described_class.send(:master_make_name_list)
      expect(result).to eq(master_makes.map(&:downcase))
    end
  end
end
