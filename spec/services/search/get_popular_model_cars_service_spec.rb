require 'rails_helper'

RSpec.describe Search::GetPopularModelCarsService do
  let(:make) { instance_double('CarMake', id_make: 1) }
  let(:model) { instance_double('CarModel', id_model: 2) }
  let(:current_currency) { 'USD' }
  let(:user_country_code) { 1 }
  let(:price_range) { instance_double('PriceRange', from: 1500, to: 2000) }
  let(:top_model) { [TAggregateRankingPoint.new(maker_id: 1, model_id: 2)] }
  let(:solr_response) do
    {
      'response' => {
        'docs' => [
          { 'Price' => 1600, 'Status' => 0, 'ModelID' => 3 },
          { 'Price' => 1700, 'Status' => 0, 'ModelID' => 2 },
          { 'Price' => 1800, 'Status' => 0, 'ModelID' => 2 },
          { 'Price' => 1900, 'Status' => 0, 'ModelID' => 3 },
          { 'Price' => 2000, 'Status' => 1, 'ModelID' => 2 },
        ]
      }
    }
  end
  let(:expected_cars) do
    [
      { 'Price' => 1600, 'Status' => 0, 'ModelID' => 3, 'Make' => 'Toyota' },
      { 'Price' => 1700, 'Status' => 0, 'ModelID' => 2, 'Make' => 'Toyota' },
      { 'Price' => 1800, 'Status' => 0, 'ModelID' => 2, 'Make' => 'Toyota' },
    ]
  end

  subject do
    described_class.new(make, model, current_currency, user_country_code)
  end

  before do
    allow(make).to receive(:vc_name_e).and_return('make')
    allow(model).to receive(:vc_name_e).and_return('model')
    allow(MasterInfo::PriceRange).to receive(:fetch_price_range).and_return(price_range)
    allow(TAggregateOffer).to receive(:top_model_by_offer_count).and_return(top_model)
    allow_any_instance_of(Solr::Base).to receive(:find).and_return(solr_response)
    allow_any_instance_of(CarsPresenter).to receive(:cars).and_return(expected_cars)
    allow(TAggregateRankingPoint).to receive(:query_by_make_and_model).and_return(top_model)
    allow(Rails.cache).to receive(:fetch).and_yield
  end

  describe '#call' do
    it 'returns an array of popular model cars' do
      expect(subject.call).to eq(expected_cars)
    end
  end

  describe '#list' do
    it 'returns a list of top models' do
      expect(subject.list).to eq(top_model)
    end
  end
end
