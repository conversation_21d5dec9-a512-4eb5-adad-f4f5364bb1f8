require 'rails_helper'

RSpec.describe Search::InternalLinksAllMake do
  let(:user_country_code) { 410 }
  let(:condition_data) do
    [
      double(text: 'Luxury Cars List', query: 'Price:[10000 TO *]', url: 'used_car/all/all/?prcf=10000'),
      double(text: 'Affordable Cars List', query: 'Price:[* TO 2500]', url: 'used_car/all/all/?prct=2500'),
      double(text: 'Low-Mileage Cars Inventory List', query: 'Odometer:[* TO 50000]', url: 'used_car/all/all/?mimx=50000'),
      double(text: 'Older Model Cars List (1989 and Earlier)', query: 'ModelYear:1989', url: 'used_car/all/all/?jid=1989'),
      double(text: 'Nissan Skyline GT-R', query: 'MakeID:2 AND ModelID:108', url: 'used_car/nissan/skyline%20gt-r'),
      double(text: 'Honda NSX', query: 'MakeID:3 AND ModelID:158', url: 'used_car/honda/nsx'),
      double(text: 'Mazda RX-7', query: 'MakeID:5 AND ModelID:239', url: 'used_car/mazda/rx-7'),
      double(text: 'Mazda Cosmo', query: 'MakeID:5 AND ModelID:4779', url: 'used_car/mazda/cosmo'),
      double(text: 'Subaru Alcyone SVX', query: 'MakeID:6 AND ModelID:1200', url: 'used_car/subaru/alcyone%20svx'),
      double(text: 'Mitsubishi GTO', query: 'MakeID:4 AND ModelID:198', url: 'used_car/mitsubishi/gto'),
      double(text: 'Mitsubishi Lancer Evolution VII', query: 'MakeID:4 AND ModelID:3403', url: 'used_car/mitsubishi/lancer%20evolution%20vii'),
      double(text: 'Toyota Supra', query: 'MakeID:1 AND ModelID:42', url: 'used_car/toyota/supra'),
    ]
  end

  let(:solr_response) do
    {
      'facet_counts' => {
        'facet_queries' => {
          'Price:[10000 TO *]' => 657,
          'Price:[* TO 2500]' => 78,
          'Odometer:[* TO 50000]' => 802,
          'ModelYear:1989' => 2,
          'MakeID:2 AND ModelID:108' => 1,
          'MakeID:3 AND ModelID:158' => 1,
          'MakeID:5 AND ModelID:239' => 1,
          'MakeID:5 AND ModelID:4779' => 1,
          'MakeID:6 AND ModelID:1200' => 1,
          'MakeID:4 AND ModelID:198' => 1,
          'MakeID:4 AND ModelID:3403' => 1,
          'MakeID:1 AND ModelID:42' => 1
        }
      }
    }
  end

  let(:solr_facet_service) { instance_double(Solr::FacetService) }
  let(:body_style_data) do
    [
      double(text: 'SUV', url: 'used_car/all/all/?bsty=7'),
      double(text: 'Truck', url: 'used_car/all/all/?bsty=8'),
      double(text: 'Van / Minivan', url: 'used_car/all/all/?bsty=9'),
      double(text: 'Sedan', url: 'used_car/all/all/?bsty=6'),
      double(text: 'Bus', url: 'used_car/all/all/?bsty=1'),
      double(text: 'Hatchback', url: 'used_car/all/all/?bsty=3'),
      double(text: 'Coupe', url: 'used_car/all/all/?bsty=11'),
      double(text: 'Convertible', url: 'used_car/all/all/?bsty=2'),
      double(text: 'Wagon', url: 'used_car/all/all/?bsty=10'),
      double(text: 'Machinery', url: 'used_car/all/all/?bsty=4'),
      double(text: 'Mini Vehicle', url: 'used_car/all/all/?bsty=5'),
    ]
  end

  let(:other_category_data) do
    [
      double(text: 'Left Hand Drive', url: 'used_car/all/all/?st=13'),
      double(text: 'Manual', url: 'used_car/all/all/?tmns=6'),
      double(text: 'Diesel', url: 'used_car/all/all/?fues=17'),
      double(text: '4WD', url: 'used_car/all/all/?dr=10'),
      double(text: 'No Accidents', url: 'used_car/all/all/?ac=2'),
      double(text: 'Not Repaired', url: 'used_car/all/all/?ac=1'),
      double(text: 'Sunroof', url: 'used_car/all/all/?op=33'),
    ]
  end

  let(:vehicle_stock_data) do
    [
      double(name: 'United Kingdom', number: 826, pc_path: 'used_car/all/all/?co=826'),
      double(name: 'Korea, Republic of', number: 410, pc_path: 'used_car/all/all/?co=410'),
      double(name: 'Singapore', number: 702, pc_path: 'used_car/all/all/?co=702'),
    ]
  end

  let(:popular_model_data) do
    [
      double(maker_nm: 'Nissan', model_nm: 'Serena'),
      double(maker_nm: 'Toyota', model_nm: 'PRIUS α'),
      double(maker_nm: 'Mercedes-Benz', model_nm: 'A-Class'),
      double(maker_nm: 'Daihatsu', model_nm: 'Copen'),
      double(maker_nm: 'Isuzu', model_nm: 'Elf Truck'),
    ]
  end

  let(:popular_maker_data) do
    [
      double(maker_nm: 'Nissan'),
      double(maker_nm: 'Toyota'),
      double(maker_nm: 'Mercedes-Benz'),
      double(maker_nm: 'Daihatsu'),
      double(maker_nm: 'Isuzu'),
    ]
  end

  let(:price_data) do
    [
      { url: 'used_car/all/all/?prcf=10000', range: '$10,000+', stock: 100 },
      { url: 'used_car/all/all/?prct=5000', range: 'Up to $5,000', stock: 50 },
    ]
  end

  before do
    allow(Solr::FacetService).to receive(:new).and_return(solr_facet_service)
    allow(MasterInfo::InternalLinkAllMakeCondition).to receive(:all).and_return(condition_data)
    allow(condition_data).to receive(:pluck).with(:query).and_return(condition_data.map(&:query))
    allow(solr_facet_service).to receive(:call_facet).and_return(solr_response)
    allow(MasterInfo::InternalLink::BodyStyle).to receive(:all).and_return(body_style_data)
    allow(MasterInfo::InternalLink::OtherCategory).to receive(:all).and_return(other_category_data)
    allow(MasterInfo::VehiclesInStock).to receive(:use_in_canonical).and_return(vehicle_stock_data)
    allow(Search::GetPopularForAllMakeService).to receive(:new).and_return(
      double(list_all_model: popular_model_data, list_all_maker: popular_maker_data),
    )
    allow(DataLoader::CarCounterService).to receive(:new).with(user_country_code).and_return(
      double(process_price_data: price_data),
    )
  end

  describe '#call' do
    let(:service) { described_class.new(user_country_code) }
    let(:result) { service.call }

    it 'returns array with eight sections' do
      expect(result.size).to eq(8)
      expect(result.map { |section| section[:title] }).to match_array([
                                                                        'Specially Selected Vehicles',
                                                                        'Rare Cars List',
                                                                        'Other Category',
                                                                        'Vehicles In Stock',
                                                                        'BodyStyle',
                                                                        'Popular Models',
                                                                        'Popular Makes',
                                                                        'Car Price (FOB-US$)',
                                                                      ])
    end

    it 'separates special price conditions correctly' do
      special_price_section = result.find { |section| section[:title] == 'Specially Selected Vehicles' }
      expect(special_price_section[:condition].size).to eq(4)
    end

    it 'groups rare cars correctly' do
      rare_cars_section = result.find { |section| section[:title] == 'Rare Cars List' }
      expect(rare_cars_section[:condition].size).to eq(8)
    end

    it 'formats condition data correctly' do
      special_price_section = result.find { |section| section[:title] == 'Specially Selected Vehicles' }
      first_condition = special_price_section[:condition].first
      expect(first_condition).to eq([['used_car/all/all/?prcf=10000', 'Luxury Cars List', :url], 657])
    end

    it 'calls Solr facet service with correct queries' do
      expected_queries = condition_data.map(&:query)
      expect(solr_facet_service).to receive(:call_facet).with(queries: expected_queries)
      service.call
    end

    context 'when other_category_data is present' do
      it 'includes other_category_data in the result' do
        other_category_section = result.find { |section| section[:title] == 'Other Category' }
        expect(other_category_section[:condition].size).to eq(7)
      end
    end

    context 'when vehicle_stock_data is present' do
      it 'includes vehicle_stock_data in the result' do
        vehicle_stock_section = result.find { |section| section[:title] == 'Vehicles In Stock' }
        expect(vehicle_stock_section[:condition].size).to eq(3)
      end
    end

    context 'when body_style_data is present' do
      it 'includes body_style_data in the result' do
        body_style_section = result.find { |section| section[:title] == 'BodyStyle' }
        expect(body_style_section[:condition].size).to eq(11)
      end
    end

    context 'when popular_model_data is present' do
      it 'includes popular_model_data in the result' do
        popular_model_section = result.find { |section| section[:title] == 'Popular Models' }
        expect(popular_model_section[:condition].size).to eq(5)
      end
    end

    context 'when popular_maker_data is present' do
      it 'includes popular_maker_data in the result' do
        popular_maker_section = result.find { |section| section[:title] == 'Popular Makes' }
        expect(popular_maker_section[:condition].size).to eq(5)
      end
    end

    context 'when price_data is present' do
      it 'includes price_data in the result' do
        price_section = result.find { |section| section[:title] == 'Car Price (FOB-US$)' }
        expect(price_section[:condition].size).to eq(2)
      end
    end
  end
end
