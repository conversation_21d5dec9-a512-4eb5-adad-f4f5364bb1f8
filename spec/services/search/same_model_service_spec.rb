require 'rails_helper'

RSpec.describe Search::SameModelService do
  let(:car_make) { instance_double(Master<PERSON><PERSON>, id_make: 123) }
  let(:car_model) { instance_double(MasterModel, id_model: 456) }

  before do
    allow(car_make).to receive(:vc_name_e).and_return('make')
    allow(car_model).to receive(:vc_name_e).and_return('model')
  end

  subject do
    described_class.new(
      current_currency: nil,
      user_country_code: nil,
      current_user: { userId: 456 },
      car_make: car_make,
      car_model: car_model,
    )
  end

  describe '#similar_models' do
    context 'when prepare_and_check_invalid? returns true' do
      before do
        allow(subject).to receive(:prepare_and_check_invalid?).and_return(true)
        allow(Rails.cache).to receive(:fetch).and_yield
      end

      it 'returns an empty array' do
        expect(subject.similar_models).to eq([])
      end
    end

    context 'when prepare_and_check_invalid? returns false' do
      before do
        allow(subject).to receive(:prepare_and_check_invalid?).and_return(false)
        setup_mock_data_for_similar_models
        allow(Rails.cache).to receive(:fetch).and_yield
      end

      it 'returns a list of similar models' do
        expect(subject.similar_models).to eq(['Car A'])
      end
    end
  end

  describe '#similar_model_list' do
    context 'when prepare_and_check_invalid? returns true' do
      before do
        allow(subject).to receive(:prepare_and_check_invalid?).and_return(true)
      end

      it 'returns nil' do
        expect(subject.similar_model_list).to be_nil
      end
    end

    context 'when prepare_and_check_invalid? returns false' do
      before do
        allow(subject).to receive(:prepare_and_check_invalid?).and_return(false)
        setup_mock_data_for_similar_model_list
      end

      it 'returns a list of similar models' do
        expect(subject.similar_model_list).to eq(['Car A', 'Car B'])
      end
    end
  end

  describe '#prepare_and_check_invalid?' do
    context 'when most_body_type_id is blank' do
      before do
        allow(TAggregateOffer).to receive(:most_body_type_for_model).and_return(nil)
      end

      it 'returns true' do
        expect(subject.send(:prepare_and_check_invalid?)).to eq(true)
      end
    end

    context 'when most_body_type_id is present' do
      before do
        allow(TAggregateOffer).to receive(:most_body_type_for_model).and_return(123)
      end

      it 'returns false' do
        expect(subject.send(:prepare_and_check_invalid?)).to eq(false)
      end
    end
  end

  def setup_mock_data_for_similar_models
    allow(TAggregateOffer).to receive(:most_body_type_for_model).and_return(1)
    allow(TAggregateOffer).to receive(:price_range_for_body_type).and_return(from: 1000, to: 2000)
    allow(Search::SearchConditionFilter).to receive(:new).and_return(instance_double(Search::SearchConditionFilter, query_data: []))
    allow(Solr::Base).to receive_message_chain(:new,
                                               :find).and_return({ 'response' => { 'docs' => [{ 'Price' => 1500, 'ModelID' => 124, 'UserID' => 789,
                                                                                                'BodyStyle1' => 1, 'Status' => 1 }] } })
    allow(CarsPresenter).to receive(:new).and_return(instance_double(CarsPresenter, cars: ['Car A']))
  end

  def setup_mock_data_for_similar_model_list
    allow(TAggregateOffer).to receive(:most_body_type_for_model).and_return(1)
    allow(TAggregateOffer).to receive(:price_range_for_body_type).and_return(from: 1000, to: 2000)
    allow(TAggregateOffer).to receive(:same_model_cars_list).and_return(['Car A', 'Car B'])
  end
end
