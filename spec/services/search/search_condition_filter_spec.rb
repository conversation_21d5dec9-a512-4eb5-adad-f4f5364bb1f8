require 'rails_helper'
require 'json'

RSpec.describe Search::SearchConditionFilter do
  let(:dummy_params) { { search_params: { key1: [10, 20], key2: [30] }.to_json } }
  let(:dummy_country_code) { 123 }

  describe '#documents' do
    it 'returns an array of documents' do
      params = ActionController::Parameters.new(dummy_params)
      search_condition_filter = described_class.new(params, dummy_country_code)

      allow(search_condition_filter).to receive(:solr_response).and_return({ 'docs' => %w[doc1 doc2] })

      expect(search_condition_filter.documents).to be_an(Array)
      expect(search_condition_filter.documents.size).to eq(2)
    end
  end

  describe '#count' do
    it 'returns the count of documents' do
      params = ActionController::Parameters.new(dummy_params)
      search_condition_filter = described_class.new(params, dummy_country_code)

      allow(search_condition_filter).to receive(:solr_response).and_return({ 'numFound' => 5 })

      expect(search_condition_filter.count).to eq(5)
    end
  end

  describe '#query_data' do
    context 'when there are search parameters' do
      let(:dummy_params) { { search_params: { make: 'Toyota', model: 'Camry', prcf: 1000, prct: 2000 }.to_json } }
      let(:dummy_country_code) { 123 }
      let(:make_double) { double('MasterMake', id_make: 1) }
      let(:model_double) { double('MasterModel', id_model: 1) }

      before do
        allow(MasterMake).to receive(:get_by_vc_name_e).and_return(make_double)
        allow(MasterModel).to receive(:get_by_vc_name_e).and_return(model_double)
      end

      it 'returns the correct query data' do
        params = ActionController::Parameters.new(dummy_params)
        search_condition_filter = described_class.new(params, dummy_country_code)

        expected_query_data = 'MakeID:1 AND ModelID:36 AND Price:[1000 TO 2000]'

        expect(search_condition_filter.query_data).to eq(expected_query_data)
      end
    end

    context 'when there are no search parameters' do
      let(:dummy_params) { ActionController::Parameters.new }

      it 'returns an empty string' do
        search_condition_filter = described_class.new(dummy_params)
        expect(search_condition_filter.query_data).to eq('')
      end
    end
  end

  describe 'private methods' do
    describe '#solr_response' do
      let(:dummy_params) { { search_params: { key1: [10, 20], key2: [30] }.to_json } }
      let(:dummy_country_code) { 123 }
      let(:search_condition_filter) { described_class.new(dummy_params, dummy_country_code) }

      it 'returns the response from Solr' do
        allow(search_condition_filter).to receive(:query_data).and_return('aid:1 AND oid:1 AND prcf:[1000 TO *] AND prct:[* TO 2000]')
        allow_any_instance_of(Solr::Base).to receive(:find).and_return({ 'response' => { 'docs' => %w[doc1 doc2], 'numFound' => 5 } })

        response = search_condition_filter.send(:solr_response)

        expect(response).to have_key('docs')
        expect(response).to have_key('numFound')
        expect(response['docs']).to be_an(Array)
        expect(response['numFound']).to be_a(Numeric)
      end
    end

    describe '#process_single_case' do
      let(:dummy_params) { { search_params: { key1: [10, 20], key2: [30] }.to_json } }
      let(:dummy_country_code) { 496 }
      let(:search_condition_filter) { described_class.new(dummy_params, dummy_country_code) }

      context 'when the type is from' do
        it 'returns a valid query string' do
          data = { key: 'prcf', type: 'from', value: 1000 }
          result = search_condition_filter.send(:process_single_case, data)
          expect(result).to eq('prcf:[1000 TO *]')
        end
      end

      context 'when the type is to' do
        it 'returns a valid query string' do
          data = { key: 'prct', type: 'to', value: 2000 }
          result = search_condition_filter.send(:process_single_case, data)
          expect(result).to eq('prct:[* TO 2000]')
        end
      end

      context 'when the type is for_country' do
        it 'returns a valid query string' do
          result = search_condition_filter.send(:process_single_case, { key: 'rfc', type: 'for_country', value: 123 })
          expect(result).to eq('ModelYear:[1999 TO *]') # Assuming dummy country code is 123
        end
      end

      context 'when the type is first_secondary_bsty' do
        it 'returns a valid query string' do
          allow(search_condition_filter).to receive(:process_primary_secondary_body_style).and_return('BodyStyle1:1 AND BodyStyle2:2')
          data = { key: 'bsty', type: 'first_secondary_bsty', value: '1.2' }
          result = search_condition_filter.send(:process_single_case, data)
          expect(result).to eq('BodyStyle1:1 AND BodyStyle2:2')
        end
      end

      context 'when the type is keyword' do
        it 'returns a valid query string' do
          allow(search_condition_filter).to receive(:process_for_keyword).and_return('BodyM:keyword OR Body:keyword')
          data = { key: 'fd', type: 'keyword', value: 'keyword' }
          result = search_condition_filter.send(:process_single_case, data)
          expect(result).to eq('BodyM:keyword OR Body:keyword')
        end
      end

      context 'when the type is uid' do
        it 'returns a valid query string' do
          data = { key: 'uid', type: 'uid', value: '123' }
          result = search_condition_filter.send(:process_single_case, data)
          expect(result).to eq('uid:123')
        end
      end

      context 'when the type is other' do
        it 'returns a valid query string' do
          data = { key: 'other', type: 'other', value: 'other_value' }
          result = search_condition_filter.send(:process_single_case, data)
          expect(result).to eq('other:other_value')
        end
      end
    end

    describe '#process_primary_secondary_body_style' do
      let(:dummy_params) { { search_params: { key1: [10, 20], key2: [30] }.to_json } }
      let(:dummy_country_code) { 123 }
      let(:search_condition_filter) { described_class.new(dummy_params, dummy_country_code) }

      context 'when the body style is primary and secondary' do
        it 'returns a valid query string' do
          data = { key: 'bsty', type: 'first_secondary_bsty', value: '1.2' }
          result = search_condition_filter.send(:process_primary_secondary_body_style, data)
          expect(result).to eq('((BodyStyle1:1 AND BodyStyle2:2))')
        end
      end

      context 'when the body style is primary only' do
        it 'returns a valid query string' do
          data = { key: 'bsty', type: 'first_secondary_bsty', value: '1' }
          result = search_condition_filter.send(:process_primary_secondary_body_style, data)
          expect(result).to eq('(BodyStyle1:1)')
        end
      end
    end

    describe '#get_value' do
      let(:dummy_params) { { search_params: { key1: [10, 20], key2: [30] }.to_json } }
      let(:dummy_country_code) { 123 }
      let(:search_condition_filter) { described_class.new(dummy_params, dummy_country_code) }

      it 'returns the value associated with the from type' do
        data = [
          { type: 'from', value: 100 },
          { type: 'to', value: 200 },
          { type: 'for_country', value: 'US' },
        ]
        result = search_condition_filter.send(:get_value, 'from', data)
        expect(result).to eq(100)
      end
    end

    describe '#modify_key_value_for_special' do
      let(:dummy_params) { { search_params: { key1: [10, 20], key2: [30] }.to_json } }
      let(:dummy_country_code) { 123 }
      let(:search_condition_filter) { described_class.new(dummy_params, dummy_country_code) }

      context 'when the key is make' do
        it 'returns the make id' do
          allow(MasterMake).to receive(:get_by_vc_name_e).with('Toyota').and_return(double(id_make: 1))
          result = search_condition_filter.send(:modify_key_value_for_special, 'make', 'Toyota')
          expect(result).to eq(['aid', 1])
        end
      end

      context 'when the key is model' do
        it 'returns the model id' do
          model_name = 'Camry'
          model_double = double(id_model: 1)

          allow(MasterModel).to receive(:joins).and_return(MasterModel)
          allow(MasterModel).to receive(:where).and_return([model_double])
          fake_cache = double('fake_cache')
          allow(fake_cache).to receive(:fetch) do |_key, &block|
            block.call
          end
          allow(Rails).to receive(:cache).and_return(fake_cache)

          result = search_condition_filter.send(:modify_key_value_for_special, 'model', model_name)
          expect(result).to eq(['oid', 1])
        end

        it 'returns the model id with make_name' do
          make_name = 'Toyota'
          model_name = 'Camry'
          model_double = double(id_model: 1)

          allow(MasterModel).to receive(:joins).and_return(MasterModel)
          allow(MasterModel).to receive(:where).and_return([model_double])

          fake_cache = double('fake_cache')
          allow(fake_cache).to receive(:fetch) do |_key, &block|
            block.call
          end
          allow(Rails).to receive(:cache).and_return(fake_cache)

          result = search_condition_filter.send(:modify_key_value_for_special, 'model', model_name, make_name)
          expect(result).to eq(['oid', 1])
        end
      end

      context 'when the key is nw, spp, or rfc' do
        it 'returns key and value unchanged if value matches NUMBER_REGEX' do
          valid_values = %w[123 456 789]

          valid_values.each do |value|
            result = search_condition_filter.send(:modify_key_value_for_special, 'nw', value)
            expect(result).to eq(['nw', value.to_i == 1 ? 1 : nil])

            result = search_condition_filter.send(:modify_key_value_for_special, 'spp', value)
            expect(result).to eq(['spp', value.to_i == 1 ? 1 : nil])

            result = search_condition_filter.send(:modify_key_value_for_special, 'rfc', value)
            expect(result).to eq(['rfc', value.to_i == 1 ? 1 : nil])
          end
        end
      end

      context 'when the key is other' do
        it 'returns the key and value unchanged' do
          result = search_condition_filter.send(:modify_key_value_for_special, 'prcf', 1000)
          expect(result).to eq(['prcf', 1000])
        end
      end
    end

    describe '#process_ref_by_country' do
      let(:dummy_params) { { search_params: { key1: [10, 20], key2: [30] }.to_json } }
      let(:dummy_country_code) { 123 }
      let(:search_condition_filter) { described_class.new(dummy_params, dummy_country_code) }

      context 'when the country code is 496 (Mongolia)' do
        it 'returns a query string for ModelYear from 1999 onwards' do
          allow(Time).to receive(:current).and_return(Time.new(2024, 2, 17))
          search_condition_filter.instance_variable_set(:@country_code, 496)

          result = search_condition_filter.send(:process_ref_by_country)

          expect(result).to eq('ModelYear:[1999 TO *]')
        end
      end

      context 'when the country code is 404 (Kenya)' do
        it 'returns a query string for ModelYearMonth eight years ago until now' do
          search_condition_filter.instance_variable_set(:@country_code, 404)
          eight_year_ago = Time.current - 8.year
          expected_start_date = eight_year_ago.beginning_of_year.strftime(described_class::YEAR_MONTH_FMT)
          expected_end_date = (eight_year_ago + 1.month).strftime(described_class::YEAR_MONTH_FMT)

          result = search_condition_filter.send(:process_ref_by_country)

          expect(result).to eq("ModelYearMonth:[#{expected_start_date} TO #{expected_end_date}]")
        end
      end
    end

    describe '#condition_by_key' do
      let(:search_condition_filter) { described_class.new({}) }
      let(:modify_key) { 'modified_key' }
      let(:value) { 1000 }
      let(:result) { search_condition_filter.send(:condition_by_key, key, modify_key, value) }

      context 'when the key is prcf' do
        let(:key) { 'prcf' }
        it 'returns a hash with the correct key, type, and value' do
          expect(result).to eq({ key: modify_key, type: 'from', value: value })
        end
      end

      context 'when the key is prct' do
        let(:key) { 'prct' }
        it 'returns a hash with the correct key, type, and value' do
          expect(result).to eq({ key: modify_key, type: 'to', value: value })
        end
      end

      context 'when the key is rfc' do
        let(:key) { 'rfc' }
        it 'returns a hash with the correct key, type, and value' do
          expect(result).to eq({ key: modify_key, type: 'for_country', value: value })
        end
      end

      context 'when the key is bsty' do
        let(:key) { 'bsty' }
        it 'returns a hash with the correct key, type, and value' do
          expect(result).to eq({ key: modify_key, type: 'first_secondary_bsty', value: value })
        end
      end

      context 'when the key is fd' do
        let(:key) { 'fd' }
        let(:value) { '' }
        it 'returns a hash with the correct key, type, and value' do
          expect(result).to eq({ key: modify_key, type: 'keyword', value: value })
        end
      end

      context 'when the key is uid' do
        let(:key) { 'uid' }
        let(:value) { '' }
        it 'returns a hash with the correct key, type, and value' do
          expect(result).to eq({ key: modify_key, type: 'uid', value: value })
        end
      end

      context 'when the key is smo' do
        let(:key) { 'smo' }
        it 'returns a hash with the correct key, type, and value' do
          expect(result).to eq({ key: modify_key, type: 'from', value: value.to_s })
        end
      end

      context 'when the key is emo' do
        let(:key) { 'emo' }
        it 'returns a hash with the correct key, type, and value' do
          expect(result).to eq({ key: modify_key, type: 'to', value: value.to_s })
        end
      end

      context 'when the key is tmns' do
        let(:key) { 'tmns' }
        let(:value) { '' }
        it 'returns a hash with the correct key, type, and value' do
          expect(result).to eq({ key: modify_key, type: 'other', value: [] })
        end
      end

      context 'when the key is other' do
        let(:key) { 'other' }
        it 'returns a hash with the correct key, type, and value' do
          expect(result).to eq({ key: modify_key, type: 'other', value: value })
        end
      end
    end

    describe '#keyword_escape' do
      let(:search_condition_filter) { described_class.new({}) }

      context 'when the value contains characters to be replaced by empty string' do
        it 'replaces those characters with empty string' do
          value = "key'word"

          result = search_condition_filter.send(:keyword_escape, value)

          expect(result).to eq('keyword')
        end
      end

      context 'when the value contains characters to be replaced by space' do
        it 'replaces those characters with space' do
          value = 'semi;colon'

          result = search_condition_filter.send(:keyword_escape, value)

          expect(result).to eq('semi colon')
        end
      end

      context 'when the value does not contain any characters to be replaced' do
        it 'returns the same value' do
          value = 'no_special_characters'

          result = search_condition_filter.send(:keyword_escape, value)

          expect(result).to eq('no_special_characters')
        end
      end
    end

    describe '#process_for_keyword' do
      let(:search_condition_filter) { described_class.new({}) }

      context 'when the value contains special characters' do
        it 'escapes those characters' do
          value = 'key+word'

          result = search_condition_filter.send(:process_for_keyword, { value: value })

          expect(result).to eq('(BodyM:key\\+word OR Body:key\\+word)')
        end
      end

      context 'when the value does not contain special characters' do
        it 'returns the value without modification' do
          value = 'no_special_characters'

          result = search_condition_filter.send(:process_for_keyword, { value: value })

          expect(result).to eq('(BodyM:no_special_characters OR Body:no_special_characters)')
        end
      end
    end

    describe '#validate_data' do
      let(:search_condition_filter) { described_class.new({}) }

      context 'when the key is in ARRAY_FIELDS' do
        it 'returns true if the value is a valid array' do
          key = 'tmns'
          value = [1, 2, 3]

          result = search_condition_filter.send(:validate_data, key, value)

          expect(result).to eq(true)
        end

        it 'returns false if the value is not a valid array' do
          key = 'tmns'
          value = 'not_an_array'

          result = search_condition_filter.send(:validate_data, key, value)

          expect(result).to eq(false)
        end
      end

      context 'when the key is in INT_VALUE_LIST' do
        it 'returns true if the value is a valid integer' do
          key = 'prcf'
          value = 100

          result = search_condition_filter.send(:validate_data, key, value)

          expect(result).to eq(true)
        end

        it 'returns false if the value is not a valid integer' do
          key = 'prcf'
          value = 'not_an_integer'

          result = search_condition_filter.send(:validate_data, key, value)

          expect(result).to eq(false)
        end
      end
    end
  end
end
