require 'rails_helper'

RSpec.describe Search::Cars do
  let(:cookies) { {} }
  let(:params) { ActionController::Parameters.new }
  let(:user_country_code) { 1 }
  let(:access_country_code) { 1 }
  let(:current_currency) { 'USD' }
  let(:core_number) { 0 }
  let(:expected_car) { { 'ItemID' => '123', 'Make' => 'Toyota', 'Model' => 'Camry', 'id' => 1 } }
  let(:sort_condition) { 1 }
  let(:service) do
    described_class.new(
      cookies: cookies,
      params: params,
      user_country_code: user_country_code,
      current_currency: current_currency,
      access_country_code: access_country_code,
      per_page: 10,
      sort_condition: sort_condition,
      core_number: core_number,
      new_arrivals: false,
      experiment: nil,
    )
  end

  describe '#exec' do
    context 'when cars are found' do
      let(:solr_response) { { 'response' => { 'docs' => [expected_car], 'numFound' => 1 } } }

      before do
        allow_any_instance_of(Solr::Base).to receive(:find).and_return(solr_response)
        allow_any_instance_of(Search::CarsPrService).to receive(:exec).and_return([{ 'id' => 1, 'name' => 'Toyota Camry' }])
        allow_any_instance_of(CarsPresenter).to receive(:cars).and_return([expected_car])
      end

      it 'returns cars' do
        cars, count_all, cars_pr_count = service.exec
        expect(cars).to eq([expected_car])
        expect(count_all).to eq(1)
        expect(cars_pr_count).to eq(1)
      end
    end

    context 'when no cars are found' do
      let(:solr_response) { { 'response' => { 'docs' => [], 'numFound' => 0 } } }

      before do
        allow_any_instance_of(Solr::Base).to receive(:find).and_return(solr_response)
      end

      it 'returns empty array' do
        cars, count_all, cars_pr_count = service.exec
        expect(cars).to eq([])
        expect(count_all).to eq(0)
        expect(cars_pr_count).to eq(0)
      end
    end
  end

  describe 'private method' do
    describe '#apply_sort' do
      context 'when jp_car_fag is true' do
        let(:params) { { co: '392' } }

        it do
          expect(service.send(:apply_sort)).to eq('Price asc, IsMoneyCollection desc, Sort desc, StartDate desc')
        end
      end

      context 'when jp_car_fag is false' do
        it do
          expect(service.send(:apply_sort)).to eq('Price asc, StartDate desc')
        end
      end
    end

    describe '#jp_flag_sort and #not_jp_flag_sort' do
      context 'sort_condition is 1' do
        it do
          expect(service.send(:jp_flag_sort)).to eq('Price asc, IsMoneyCollection desc, Sort desc, StartDate desc')
          expect(service.send(:not_jp_flag_sort)).to eq('Price asc, StartDate desc')
        end
      end

      context 'sort_condition is 2' do
        let(:sort_condition) { 2 }

        it do
          expect(service.send(:jp_flag_sort)).to eq('Price desc, IsMoneyCollection desc, Sort desc, StartDate desc')
          expect(service.send(:not_jp_flag_sort)).to eq('Price desc, StartDate desc')
        end
      end

      context 'sort_condition is 7' do
        let(:sort_condition) { 7 }

        it do
          expect(service.send(:jp_flag_sort)).to eq('ModelYear asc, IsMoneyCollection desc, Sort desc, StartDate desc')
          expect(service.send(:not_jp_flag_sort)).to eq('ModelYear asc, StartDate desc')
        end
      end

      context 'sort_condition is 8' do
        let(:sort_condition) { 8 }

        it do
          expect(service.send(:jp_flag_sort)).to eq('ModelYear desc, IsMoneyCollection desc, Sort desc, StartDate desc')
          expect(service.send(:not_jp_flag_sort)).to eq('ModelYear desc, StartDate desc')
        end
      end

      context 'sort_condition is 21' do
        let(:sort_condition) { 21 }

        it do
          expect(service.send(:jp_flag_sort)).to eq('IsMoneyCollection desc, StartDate_15min_Intervals asc, Sort desc, StartDate asc')
          expect(service.send(:not_jp_flag_sort)).to eq('StartDate asc')
        end
      end

      context 'sort_condition is 22' do
        let(:sort_condition) { 22 }

        it do
          expect(service.send(:jp_flag_sort)).to eq('IsMoneyCollection desc, StartDate_15min_Intervals desc, Sort desc, StartDate desc')
          expect(service.send(:not_jp_flag_sort)).to eq('StartDate desc')
        end
      end

      context 'sort_condition is 36' do
        let(:sort_condition) { 36 }

        it do
          expect(service.send(:jp_flag_sort)).to eq('OdometerOption asc, Odometer asc, IsMoneyCollection desc, Sort desc, StartDate desc')
          expect(service.send(:not_jp_flag_sort)).to eq('OdometerOption asc, Odometer asc, StartDate desc')
        end
      end

      context 'sort_condition is 37' do
        let(:sort_condition) { 37 }

        it do
          expect(service.send(:jp_flag_sort)).to eq('OdometerOption asc, Odometer desc, IsMoneyCollection desc, Sort desc, StartDate desc')
          expect(service.send(:not_jp_flag_sort)).to eq('OdometerOption asc, Odometer desc, StartDate desc')
        end
      end

      context 'sort_condition is 60' do
        let(:sort_condition) { 60 }
        let(:sort_total_price_jp) { service.send(:sort_total_price_jp)[0] }
        let(:sort_total_price_not_jp) { service.send(:sort_total_price_not_jp)[0] }

        it do
          expect(service.send(:jp_flag_sort)).to eq(sort_total_price_jp)
          expect(service.send(:not_jp_flag_sort)).to eq(sort_total_price_not_jp)
        end
      end

      context 'sort_condition is 61' do
        let(:sort_condition) { 61 }
        let(:sort_total_price_jp) { service.send(:sort_total_price_jp)[1] }
        let(:sort_total_price_not_jp) { service.send(:sort_total_price_not_jp)[1] }

        it do
          expect(service.send(:jp_flag_sort)).to eq(sort_total_price_jp)
          expect(service.send(:not_jp_flag_sort)).to eq(sort_total_price_not_jp)
        end
      end

      context 'sort_condition is 62' do
        let(:sort_condition) { 62 }
        let(:sort_by_featured) { service.send(:sort_by_featured) }

        it do
          expect(service.send(:jp_flag_sort)).to eq(sort_by_featured)
          expect(service.send(:not_jp_flag_sort)).to eq(sort_by_featured)
        end
      end
    end

    describe '#sort_by_featured and #new_sort_by_featured' do
      context 'when user_country_code is 894' do
        let(:user_country_code) { 894 }

        it do
          expect(service.send(:sort_by_featured)).to eq('Score1 desc, Price asc')
          expect(service.send(:new_sort_by_featured)).to eq('ScoreB1 desc, Price asc')
        end
      end

      context 'when user_country_code is 404' do
        let(:user_country_code) { 404 }

        it do
          expect(service.send(:sort_by_featured)).to eq('Score2 desc, Price asc')
          expect(service.send(:new_sort_by_featured)).to eq('ScoreB2 desc, Price asc')
        end
      end

      context 'when user_country_code is 840' do
        let(:user_country_code) { 840 }

        it do
          expect(service.send(:sort_by_featured)).to eq('Score3 desc, Price asc')
          expect(service.send(:new_sort_by_featured)).to eq('ScoreB3 desc, Price asc')
        end
      end

      context 'when user_country_code is 372' do
        let(:user_country_code) { 372 }

        it do
          expect(service.send(:sort_by_featured)).to eq('Score4 desc, Price asc')
          expect(service.send(:new_sort_by_featured)).to eq('ScoreB4 desc, Price asc')
        end
      end

      context 'when user_country_code is 124' do
        let(:user_country_code) { 124 }

        it do
          expect(service.send(:sort_by_featured)).to eq('Score5 desc, Price asc')
          expect(service.send(:new_sort_by_featured)).to eq('ScoreB5 desc, Price asc')
        end
      end

      context 'when user_country_code is 662' do
        let(:user_country_code) { 662 }

        it do
          expect(service.send(:sort_by_featured)).to eq('Score6 desc, Price asc')
          expect(service.send(:new_sort_by_featured)).to eq('ScoreB6 desc, Price asc')
        end
      end

      context 'when user_country_code is 776' do
        let(:user_country_code) { 776 }

        it do
          expect(service.send(:sort_by_featured)).to eq('Score7 desc, Price asc')
          expect(service.send(:new_sort_by_featured)).to eq('ScoreB7 desc, Price asc')
        end
      end

      context 'when user_country_code is 388' do
        let(:user_country_code) { 388 }

        it do
          expect(service.send(:sort_by_featured)).to eq('Score8 desc, Price asc')
          expect(service.send(:new_sort_by_featured)).to eq('ScoreB8 desc, Price asc')
        end
      end

      context 'when user_country_code is 826' do
        let(:user_country_code) { 826 }

        it do
          expect(service.send(:sort_by_featured)).to eq('Score desc, Price asc')
          expect(service.send(:new_sort_by_featured)).to eq('Score desc, Price asc')
        end
      end

      context 'when user_country_code is not specific' do
        it do
          expect(service.send(:sort_by_featured)).to eq('Score9 desc, Price asc')
          expect(service.send(:new_sort_by_featured)).to eq('ScoreB9 desc, Price asc')
        end
      end
    end

    describe '#sort_total_price_not_jp and #sort_total_price_jp' do
      context 'when shipping flag is true' do
        before do
          allow(service).to receive(:cal_tradetpopts).and_return([true, insurance, inspection])
        end

        context 'and insurance and inspection flags are false' do
          let(:insurance) { false }
          let(:inspection) { false }

          it 'returns the correct sort strings' do
            expected_sort_not_jp_strings = ['withFreightCost asc, StartDate desc', 'withFreightCost desc, StartDate desc']
            expected_sort_strings = [
              'withFreightCost asc, IsMoneyCollection desc, Sort desc, StartDate desc',
              'withFreightCost desc, IsMoneyCollection desc, Sort desc, StartDate desc',
            ]
            expect(service.send(:sort_total_price_not_jp)).to eq(expected_sort_not_jp_strings)
            expect(service.send(:sort_total_price_jp)).to eq(expected_sort_strings)
            expect(service.send(:sort_total_price_jp)).to eq(expected_sort_strings)
          end
        end

        context 'and insurance is true and inspection flag is false' do
          let(:insurance) { true }
          let(:inspection) { false }

          it 'returns the correct sort strings' do
            expected_sort_not_jp_strings = ['withInsurance asc, StartDate desc', 'withInsurance desc, StartDate desc']
            expected_sort_strings = [
              'withInsurance asc, IsMoneyCollection desc, Sort desc, StartDate desc',
              'withInsurance desc, IsMoneyCollection desc, Sort desc, StartDate desc',
            ]
            expect(service.send(:sort_total_price_not_jp)).to eq(expected_sort_not_jp_strings)
            expect(service.send(:sort_total_price_jp)).to eq(expected_sort_strings)
          end
        end

        context 'and inspection is true and insurance flag is false' do
          let(:insurance) { false }
          let(:inspection) { true }

          it 'returns the correct sort strings' do
            expected_sort_not_jp_strings = ['withInspection asc, StartDate desc', 'withInspection desc, StartDate desc']
            expected_sort_strings = [
              'withInspection asc, IsMoneyCollection desc, Sort desc, StartDate desc',
              'withInspection desc, IsMoneyCollection desc, Sort desc, StartDate desc',
            ]
            expect(service.send(:sort_total_price_not_jp)).to eq(expected_sort_not_jp_strings)
            expect(service.send(:sort_total_price_jp)).to eq(expected_sort_strings)
          end
        end

        context 'and insurance and inspection flags are true' do
          let(:insurance) { true }
          let(:inspection) { true }

          it 'returns the correct sort strings' do
            expected_sort_not_jp_strings = ['cifWithInspection asc, StartDate desc', 'cifWithInspection desc, StartDate desc']
            expected_sort_strings = [
              'cifWithInspection asc, IsMoneyCollection desc, Sort desc, StartDate desc',
              'cifWithInspection desc, IsMoneyCollection desc, Sort desc, StartDate desc',
            ]
            expect(service.send(:sort_total_price_not_jp)).to eq(expected_sort_not_jp_strings)
            expect(service.send(:sort_total_price_jp)).to eq(expected_sort_strings)
          end
        end
      end

      context 'when shipping flag is false' do
        before do
          allow(service).to receive(:cal_tradetpopts).and_return([false, true, true])
        end

        it 'returns the correct sort strings' do
          expected_sort_not_jp_strings = ['Price asc, StartDate desc', 'Price desc, StartDate desc']
          expected_sort_strings = [
            'Price asc, IsMoneyCollection desc, Sort desc, StartDate desc',
            'Price desc, IsMoneyCollection desc, Sort desc, StartDate desc',
          ]
          expect(service.send(:sort_total_price_not_jp)).to eq(expected_sort_not_jp_strings)
          expect(service.send(:sort_total_price_jp)).to eq(expected_sort_strings)
        end
      end
    end

    describe '#cal_tradetpopts' do
      let(:service) { described_class.new(cookies: cookies) }

      context 'when cookies include shipping, insurance, and inspection flags' do
        let(:cookies) { { 'tradetpopts' => 'shp=1&insu=1&insp=1' } }

        it 'returns shipping, insurance, and inspection flags as true' do
          expect(service.send(:cal_tradetpopts)).to eq([true, true, true])
        end
      end

      context 'when cookies include only shipping flag' do
        let(:cookies) { { 'tradetpopts' => 'shp=1' } }

        it 'returns shipping flag as true and insurance and inspection flags as false' do
          expect(service.send(:cal_tradetpopts)).to eq([true, false, false])
        end
      end

      context 'when cookies include only insurance flag' do
        let(:cookies) { { 'tradetpopts' => 'insu=1' } }

        it 'returns insurance flag as true and shipping and inspection flags as false' do
          expect(service.send(:cal_tradetpopts)).to eq([false, true, false])
        end
      end

      context 'when cookies include only inspection flag' do
        let(:cookies) { { 'tradetpopts' => 'insp=1' } }

        it 'returns inspection flag as true and shipping and insurance flags as false' do
          expect(service.send(:cal_tradetpopts)).to eq([false, false, true])
        end
      end

      context 'when cookies do not include any trade options' do
        let(:cookies) { {} }

        it 'returns shipping, insurance, and inspection flags as false' do
          expect(service.send(:cal_tradetpopts)).to eq([false, false, false])
        end
      end
    end
  end
end
