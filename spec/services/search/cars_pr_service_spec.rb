require 'rails_helper'

RSpec.describe Search::CarsPrService do
  let(:params) { ActionController::Parameters.new }
  let(:user_country_code) { 1 }
  let(:core_number) { 0 }
  let(:service) { described_class.new(params: params, user_country_code: user_country_code, core_number: core_number) }

  describe '#exec' do
    context 'when there are valid PR cars' do
      let(:pr_seller_ids) { %w[1 2 3] }
      let(:docs) do
        {
          '1' => { 'doclist' => { 'numFound' => 1, 'docs' => [{ 'MakeID' => '1', 'Price' => 1000, 'UserID' => '1' }] } },
          '2' => { 'doclist' => { 'numFound' => 0, 'docs' => [] } },
          '3' => { 'doclist' => { 'numFound' => 1, 'docs' => [{ 'MakeID' => '1', 'Price' => 2000, 'UserID' => '3' }] } }
        }
      end

      before do
        allow(service).to receive(:pr_seller_ids).and_return(pr_seller_ids)
        allow(Search::SearchConditionFilter).to receive(:new).and_return(double('Search::SearchConditionFilter', query_data: 'query'))
        allow(Solr::GroupService).to receive(:new).and_return(double('Solr::GroupService', group_query: { 'grouped' => docs }))
      end

      it 'executes successfully and returns valid PR cars' do
        expect_result = [{ 'MakeID' => '1', 'Price' => 1000, 'UserID' => '1', 'StockPR' => PR_CATEGORY_ID },
                         { 'MakeID' => '1', 'Price' => 2000, 'StockPR' => PR_CATEGORY_ID, 'UserID' => '3' }]
        expect(service.exec).to eq(expect_result)
      end
    end

    context 'when there are no valid PR cars' do
      let(:pr_seller_ids) { [] }

      before do
        allow(service).to receive(:pr_seller_ids).and_return(pr_seller_ids)
      end

      it 'executes successfully and returns an empty array' do
        expect(service.exec).to eq([])
      end
    end
  end

  describe 'private methods' do
    describe '#invalid_pr_stocks_request?' do
      let(:params) { { bsty: 'some_value' } }

      context 'when params is not ActionController::Parameters' do
        let(:params) { { bsty: 'some_value' } }

        it 'returns true' do
          expect(service.send(:invalid_pr_stocks_request?)).to be true
        end
      end

      context 'when params is ActionController::Parameters' do
        let(:params) { ActionController::Parameters.new }

        it 'returns false if bsty is not present' do
          expect(service.send(:invalid_pr_stocks_request?)).to be false
        end

        it 'returns false if bsty is not BSTY_MACHINEARY' do
          params[:bsty] = 'some_value'
          expect(service.send(:invalid_pr_stocks_request?)).to be false
        end

        it 'returns true if bsty is BSTY_MACHINEARY and contains only one body type' do
          params[:bsty] = '4'
          expect(service.send(:invalid_pr_stocks_request?)).to be true
        end
      end
    end

    describe '#pr_seller_ids' do
      context 'when there are valid PR seller IDs' do
        let(:expected_seller_ids) { %w[seller_id_1 seller_id_2] }

        before do
          allow(MAnyConfig).to receive(:find_by_sql).and_return(double(pluck: expected_seller_ids))
        end

        it 'returns the valid PR seller IDs' do
          expect(service.send(:pr_seller_ids)).to eq(expected_seller_ids)
        end
      end

      context 'when there are no valid PR seller IDs' do
        before do
          allow(MAnyConfig).to receive(:find_by_sql).and_return(double(pluck: []))
        end

        it 'returns an empty array' do
          expect(service.send(:pr_seller_ids)).to eq([])
        end
      end
    end

    describe '#pick_up_pr_stock' do
      context 'when there are valid PR cars' do
        let(:cars) do
          [
            { 'MakeID' => 'make_id_1', 'Price' => 1000 },
            { 'MakeID' => 'make_id_2', 'Price' => 1500 },
            { 'MakeID' => 'make_id_1', 'Price' => 1200 },
          ]
        end

        it 'picks up a valid PR car' do
          picked_car = service.send(:pick_up_pr_stock, cars)
          expect(cars.map { |car| car['MakeID'] }).to include(picked_car['MakeID'])
          expect(cars.map { |car| car['Price'] }).to include(picked_car['Price'])
          expect(picked_car['StockPR']).to eq(PR_CATEGORY_ID)
        end
      end

      context 'when there are no valid PR cars' do
        let(:cars) do
          [
            { 'MakeID' => 'make_id_1', 'Price' => 100_000_000 },
            { 'MakeID' => 'make_id_2', 'Price' => 100_000_000 },
            { 'MakeID' => 'make_id_1', 'Price' => 100_000_000 },
          ]
        end

        it 'returns nil' do
          expect(service.send(:pick_up_pr_stock, cars)).to be_nil
        end
      end
    end
  end
end
