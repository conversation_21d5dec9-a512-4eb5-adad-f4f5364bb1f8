require 'rails_helper'

RSpec.describe Search::RecommendMakeModel do
  describe '#call' do
    let(:make_id) { 1 }
    let(:country_code) { 0 }

    subject { described_class.new(make_id, country_code, check_smart_phone) }

    before do
      allow(Rails.cache).to receive(:fetch).and_call_original
      group_field_name = 'MakeID,ModelID'

      allow_any_instance_of(Solr::FacetService).to receive(:call_facet).and_return(
        {
          'facet_counts' => {
            'facet_pivot' => {
              group_field_name => [{ 'value' => 'some_value' }]
            }
          }
        },
      )
    end

    context 'when check_smart_phone is true' do
      let(:check_smart_phone) { true }

      it 'returns model name lists' do
        allow(Search::RecommendMakeModel).to receive_message_chain(:new, :call).and_return(['Model 1', 'Model 2'])

        result = subject.call

        expect(result).to eq(['Model 1', 'Model 2'])
      end
    end

    context 'when check_smart_phone is false' do
      let(:check_smart_phone) { false }

      it 'returns model name lists' do
        allow(TPopularRanking).to receive(:fetch_popular_ranking).with(country_code, anything,
                                                                       anything).and_return([{ model_id: 1 }, { model_id: 2 }, { model_id: 3 }])
        allow_any_instance_of(Array).to receive(:pluck).and_return(%w[model_id_1 model_id_2 model_id_3]) # Ensure pluck returns strings
        model_with_stock_ids = %w[model_id_1 model_id_2 model_id_3]
        allow(subject).to receive(:fetch_stock_by_group_field).and_return(model_with_stock_ids)

        expect(MasterModel).to receive(:fetch_vc_name_e).twice.with(model_with_stock_ids).and_return(['Model 3', 'Model 4'])

        result = subject.call

        expect(result).to eq(['Model 3', 'Model 4'])
      end
    end
  end

  describe '#list_body_type' do
    let(:make) { double('Make', id_make: 1) }
    let(:country_code) { 0 }
    let(:check_smart_phone) { false }
    let(:group_field_name) { 'MakeID,BodyStyle1' }

    subject { described_class.new(make, country_code, check_smart_phone) }

    before do
      allow(Rails.cache).to receive(:fetch).and_call_original

      allow_any_instance_of(Solr::FacetService).to receive(:call_facet).and_return(
        {
          'facet_counts' => {
            'facet_pivot' => {
              group_field_name => [{ 'value' => 'some_value' }]
            }
          }
        },
      )
    end

    it 'returns a list of body types' do
      make_body_style_1_ids = [1, 2, 3]

      allow(subject).to receive(:fetch_stock_by_group_field).and_return(make_body_style_1_ids)
      allow(MPrimaryBodyStyle).to receive(:where).and_return(MPrimaryBodyStyle.none)
      allow_any_instance_of(Array).to receive(:pluck).and_return([])

      result = subject.send(:list_body_type)

      expect(result).to eq([])
    end
  end

  describe '#fetch_stock_by_group_field' do
    let(:make) { double('Make', id_make: 1) }
    let(:country_code) { 0 }
    let(:check_smart_phone) { false }
    let(:subject) { described_class.new(make, country_code, check_smart_phone) }

    before do
      allow(Rails.cache).to receive(:fetch).and_call_original
      group_field_name = 'MakeID,BodyStyle1'

      allow_any_instance_of(Solr::FacetService).to receive(:call_facet).and_return(
        {
          'facet_counts' => {
            'facet_pivot' => {
              group_field_name => [{ 'pivot' => 'value_1' }, { 'pivot' => 'value_2' }]
            }
          }
        },
      )
    end

    it 'returns an array of stock values' do
      group_field_name = 'MakeID,BodyStyle1'
      params = {}

      result = subject.send(:fetch_stock_by_group_field, group_field_name, params)

      expect(result).to eq('value_1')
    end
  end
end
