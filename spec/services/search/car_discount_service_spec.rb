require 'rails_helper'

RSpec.describe Search::CarDiscountService do
  describe '#call' do
    let(:car_id) { 123 }
    let(:discount_rate) { 0.1 }

    subject { described_class.new(car_id).call }

    context 'when car has discount' do
      before do
        allow_any_instance_of(Solr::Base).to receive(:find).and_return({
                                                                         'response' => {
                                                                           'docs' => [{ 'DiscountRate1Month' => discount_rate }]
                                                                         }
                                                                       })
      end

      it 'returns the discount rate' do
        expect(subject).to eq({ 'DiscountRate1Month' => discount_rate })
      end
    end

    context 'when car has no discount' do
      before do
        allow_any_instance_of(Solr::Base).to receive(:find).and_return({
                                                                         'response' => {
                                                                           'docs' => []
                                                                         }
                                                                       })
      end

      it 'returns an empty hash' do
        expect(subject).to eq({})
      end
    end
  end
end
