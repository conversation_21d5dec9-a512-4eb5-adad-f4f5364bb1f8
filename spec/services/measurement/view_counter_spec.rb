require 'rails_helper'

RSpec.describe Measurement::ViewCounter do
  let(:car_stock) { { 'itemId' => 1, 'sellerId' => 2 } }
  let(:cookies) { { 'ml_cl_uid' => 'user_id', 'CVPD' => 'cvpd_value' } }
  let(:current_user) { { 'userId' => 3 } }
  let(:is_signed_in) { false }
  let(:view_counter) { Measurement::ViewCounter.new(car_stock, cookies, is_signed_in, current_user) }

  describe '#initialize' do
    it 'initializes the ViewCounter object with proper attributes' do
      expect(view_counter.instance_variable_get(:@item_id)).to eq(1)
      expect(view_counter.instance_variable_get(:@seller_id)).to eq(2)
      expect(view_counter.instance_variable_get(:@cookies)).to eq(cookies)
      expect(view_counter.instance_variable_get(:@current_user)).to eq(current_user)
      expect(view_counter.instance_variable_get(:@is_signed_in)).to eq(false)
    end
  end

  describe '#exec' do
    context "when the user is not signed in or current_user's userId is different from sellerId" do
      it 'executes the process in a separate thread' do
        allow(Thread).to receive(:new).and_yield

        expect(view_counter).to receive(:exec_process)

        view_counter.exec
      end
    end
    context "when the user is signed in and current_user's userId is the same as sellerId" do
      let(:current_user) { { 'userId' => 2 } }
      let(:is_signed_in) { true }
      it 'does not execute the process if user is signed in and current user is the seller' do
        expect(view_counter).not_to receive(:exec_process)

        view_counter.exec
      end
    end
  end

  describe '#exec_process' do
    it 'sends a POST request to the TcvCoreApi' do
      url = "#{Settings.tcv_core_api.domain}#{Settings.tcv_core_api.v1_paths.measurement.addpageview}"

      expect(TcvCoreApi::Request).to receive(:new).with(
        url: url,
        method: 'POST',
        params: {
          itemId: 1,
          sellerId: 2,
          cookieUserId: cookies['ml_cl_uid'].to_i
        }.to_json,
      )

      view_counter.send(:exec_process)
    end
  end
end
