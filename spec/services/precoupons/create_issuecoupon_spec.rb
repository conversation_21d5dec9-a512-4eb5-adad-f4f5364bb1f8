# spec/services/precoupons/create_issuecoupon_spec.rb
require 'rails_helper'

RSpec.describe Precoupons::CreateIssuecoupon do
  let(:cookies) { { 'CVPD' => 'cvpd_token', guid_key: 'guid_key' } }
  let(:guid_key) { 'guid_key' }

  describe '#call' do
    it 'sends a POST request to the TcvCoreApi with the correct headers and body' do
      expect(cookies['coupon_was_used']).to be_nil
      expect(cookies).to receive(:delete).with(:guid_key)

      allow_any_instance_of(TcvCoreApi::Request).to receive(:send)
        .and_return(instance_double(TcvCoreApi::Response, success?: true))

      described_class.new(cookies).call

      expect(cookies['coupon_was_used']).to eq({ value: true, expires_in: 1.day })
    end
  end
end
