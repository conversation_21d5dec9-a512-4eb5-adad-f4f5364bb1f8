require 'rails_helper'

RSpec.describe Precoupons::CreatePrecoupon do
  let(:cookies) { { CVPD: 'cvpd_token' } }
  let(:modal_type) { 'modal_type' }
  let(:ip_address) { '***********' }
  let(:guid_key) { SecureRandom.uuid }
  let(:payload) { { guidKey: guid_key, modalType: modal_type.to_i, ipAddress: ip_address }.to_json }

  before do
    allow(SecureRandom).to receive(:uuid).and_return(guid_key)
  end

  describe '#call' do
    it 'sends a POST request to the TcvCoreApi with the correct headers and body' do
      instance = described_class.new(cookies, modal_type, ip_address)
      expect(cookies).to receive(:[]=).with(:guid_key, hash_including(value: guid_key, expires: be_within(1.second).of(1.hour.from_now)))

      allow_any_instance_of(TcvCoreApi::Request).to receive(:send)
        .and_return(instance_double(TcvCoreApi::Response, success?: true))

      expect(instance.call).to eq({ success: true })
    end
  end

  describe '#build_key' do
    it 'generates a random UUID as the guid key' do
      instance = described_class.new(cookies, modal_type, ip_address)
      expect(SecureRandom).to receive(:uuid).and_return(guid_key)

      expect(instance.build_key).to eq(guid_key)
    end
  end

  describe '#payload' do
    it 'returns the correct JSON payload' do
      instance = described_class.new(cookies, modal_type, ip_address)
      expect(instance.payload).to eq(payload)
    end
  end
end
