require 'rails_helper'

RSpec.describe Inquiries::OfferCount, type: :service do
  describe '#exec' do
    let(:car_ids) { [561_992, 561_991] }
    let(:cvpd) do
      'i=854159&u=4ybcy7pQT1qijbtULhwAbA==&uh=abbasd89238&te=2024/01/08 17:07:02&ud=2024/01/08 11:59:53&h=c66a41ffebc5f537dd455c2efd1b1c67'
    end
    let(:service) { described_class.new(car_ids, cvpd) }
    context 'when the API call success' do
      it 'calls the API with correct parameters' do
        result = service.exec
        expect(result).to be_present
      end
    end

    context 'when the API call fails' do
      let(:failed_response) { double('Response', success?: false) }
      before do
        allow_any_instance_of(TcvCoreApi::Request).to receive(:send).and_return(failed_response)
      end

      it 'returns an empty hash' do
        result = service.exec
        expect(result).to be_empty
      end
    end
  end
end
