require 'rails_helper'

RSpec.describe Cars::AccountState do
  options = { :car => { 'itemId' => 561_610, 'name' => '', 'detail' => '', 'makeId' => 1, 'modelId' => 52_691, 'price' => 1000.0,
                        'priceDomestic' => 0.0, 'priceExchangeRateId' => 1, 'priceExchangeRateIdDomestic' => 0, 'isAsk' => false,
                        'firstRegistrationDate' => '2024-04-01T00:00:00', 'odmeter' => 10_000, 'odometerOption' => 0, 'displacement' => 2400,
                        'steeringId' => 13, 'transmissionId' => 7, 'fuelId' => 16, 'trimName' => '', 'chassisNo' => 'ACA36-5008045',
                        'modelCode' => 'ddddd', 'bodystyle1Id' => 1, 'bodystyle2Id' => 0, 'passengers' => 5, 'door' => 87,
                        'vehicleLength' => 2000.0, 'vehicleWidth' => 200.0, 'vehicleHeight' => 2000.0, 'vehicleOption' => [],
                        'exteriorColorId' => 49, 'interiorColorId' => 0, 'userNumber' => '************', 'modelYear' => 2024,
                        'isFavorite' => false, 'marketPriceStatus' => 0, 'stockSalesStatus' => 0, 'newStockUrl' => nil,
                        'isAccident' => false, 'terminateDate' => '2024-01-12T17:24:45.827', 'mechanicalProblem' => '',
                        'isAccountSuspended' => false, 'driveTypeId' => 10, 'manufactureYear' => 0, 'manufactureMonth' => 0,
                        'isNew' => false, 'isPegasusItem' => false, 'pegasusUserCountryNumber' => 0, 'imageCount' => 1,
                        'sellerId' => 850_014, 'sellerName' => 'sss', 'orderBuyerId' => 0, 'isPegasusChildInvalid' => false,
                        'isValid' => true, 'authentication' => { 'cvpd' => 'i=853664&u=dp44JLF%2fQKfiX99atE9LQg%3d%3d&uh=abb' } },
              :current_user => { 'userId' => 853_664, 'userAccountID' => 'abb120038', 'userHandleName' => 'abb120038',
                                 'email' => '<EMAIL>', 'userServices' => [], 'suppotersSignUpStatus' => 0, 'userCountryNumber' => 4,
                                 'authentication' => { 'cvpd' => 'i=853664&u=dp44JLF%2fQKfiX99atE9LQg%3d%3d&uh=abb120038&te=2024%2f01%2' } },
              :is_login => true, :is_new => nil, :is_preview => false, :is_smart_phone => false }

  context 'check initialize' do
    it 'check instance variable' do
      account_state = described_class.new(car: options[:car],
                                          current_user: options[:current_user],
                                          is_login: options[:is_login],
                                          is_new: options[:is_new],
                                          is_preview: options[:is_preview],
                                          is_smart_phone: options[:is_smart_phone])

      expect(account_state.instance_variables).to eq(%i[@car @current_user @is_login @is_new @is_preview @is_smart_phone])
    end
  end

  describe '#fetch' do
    context 'when car is nil' do
      let(:account_state) do
        described_class.new(car: options[:car], current_user: options[:current_user], is_login: options[:is_login],
                            is_new: options[:is_new], is_preview: options[:is_preview], is_smart_phone: options[:is_smart_phone])
      end

      it 'returns state nil' do
        expect(account_state.fetch).to eq({ state: nil })
      end
    end

    context 'when car is suspended' do
      let(:account_state) { described_class.new(car: { 'isAccountSuspended' => true }) }

      it 'returns suspended state with data' do
        expect(account_state.fetch).to eq({ state: 'suspended' })
      end
    end

    context 'when car is paid' do
      let(:account_state) do
        described_class.new(car: { 'orderBuyerId' => 24, 'stockSalesStatus' => 21 }, :current_user => { 'userId' => 24 }, :is_login => true)
      end

      it 'returns paid state with data' do
        expect(account_state.fetch).to eq({ state: 'paid' })
      end
    end

    context 'when car is reserved' do
      let(:account_state) do
        described_class.new(car: { 'orderBuyerId' => 24, 'stockSalesStatus' => 21 }, :current_user => { 'userId' => 25 }, :is_login => true)
      end

      it 'returns reserved state with data' do
        expect(account_state.fetch).to eq({ state: 'reserved' })
      end
    end

    context 'when car is awaiting_payment' do
      let(:account_state) do
        described_class.new(car: { 'orderBuyerId' => 24, 'stockSalesStatus' => 11 }, :current_user => { 'userId' => 24 }, :is_login => true)
      end

      it 'returns awaiting_payment state with data' do
        expect(account_state.fetch).to eq({ state: 'awaiting_payment' })
      end
    end

    context 'when car is reserved2' do
      let(:account_state) { described_class.new(car: { 'orderBuyerId' => 24, 'stockSalesStatus' => 11 }) }

      it 'returns reserved2 state with data' do
        expect(account_state.fetch).to eq({ state: 'reserved2' })
      end
    end

    context 'when car is expired' do
      let(:account_state) { described_class.new(car: { 'isValid' => false }) }

      it 'returns expired state with data' do
        expect(account_state.fetch).to eq({ state: 'expired', data: { new_item_url: nil } })
      end
    end

    context 'when car is has_new_item' do
      let(:account_state) { described_class.new(car: { 'isValid' => true, 'newStockUrl' => 'abc.com' }, is_new: true, is_smart_phone: false) }

      it 'returns has_new_item state with data' do
        expect(account_state.fetch).to eq({ state: 'has_new_item', data: { new_item_url: 'abc.com' } })
      end
    end

    context 'when car is my_stock' do
      let(:account_state) do
        described_class.new(car: { 'isValid' => true, 'newStockUrl' => 'abc.com', 'sellerId' => 24, 'stockSalesStatus' => 0 },
                            is_new: true, is_smart_phone: true, :current_user => { 'userId' => 24 }, :is_preview => false, :is_login => true)
      end

      it 'returns my_stock state with data' do
        expect(account_state.fetch).to be_present
      end
    end

    context 'test private method' do
      let(:account_state) do
        described_class.new(car: options[:car], current_user: options[:current_user], is_login: options[:is_login],
                            is_new: options[:is_new], is_preview: options[:is_preview], is_smart_phone: options[:is_smart_phone])
      end

      it '#suspended?' do
        expect(account_state.send(:suspended?, options[:car])).to eq(options[:car]['isAccountSuspended'])
      end

      it '#valid?' do
        expect(account_state.send(:valid?, options[:car])).to eq(options[:car]['isValid'])
      end

      it '#awaiting_payment?' do
        expect(account_state.send(:awaiting_payment?, options[:car])).to eq(false)
      end

      it '#paid?' do
        expect(account_state.send(:paid?, options[:car])).to eq(false)
      end

      it '#reserved2?' do
        expect(account_state.send(:reserved2?, options[:car])).to eq(false)
      end

      it '#reserved?' do
        expect(account_state.send(:reserved?, options[:car])).to eq(false)
      end

      it '#new_item?' do
        expect(account_state.send(:new_item?, options[:car])).to eq(false)
      end

      it '#my_stock?' do
        expect(account_state.send(:my_stock?, options[:car])).to eq(false)
      end

      it '#login_user_is_buyer?' do
        expect(account_state.send(:login_user_is_buyer?, options[:car])).to eq(false)
      end

      it '#login_user_is_seller?' do
        expect(account_state.send(:login_user_is_seller?, options[:car])).to eq(false)
      end

      it '#stock_active?' do
        expect(account_state.send(:stock_active?, options[:car])).to eq(true)
      end
    end
  end
end
