require 'rails_helper'

RSpec.describe Cars::PopularRanking do
  let(:solr_result) do
    { 'grouped' => { 'MakeID:1 & ModelID:54743' => { 'matches' => 306, 'doclist' => { 'numFound' => 0, 'start' => 0, 'docs' => [] } },
                     'MakeID:2 & ModelID:50034' => { 'matches' => 306, 'doclist' => { 'numFound' => 0, 'start' => 0, 'docs' => [] } },
                     'MakeID:5 & ModelID:61161' => { 'matches' => 306, 'doclist' => { 'numFound' => 0, 'start' => 0, 'docs' => [] } },
                     'MakeID:38 & ModelID:1257' => { 'matches' => 306, 'doclist' => { 'numFound' => 0, 'start' => 0, 'docs' => [] } },
                     'MakeID:5 & ModelID:4483' => { 'matches' => 306, 'doclist' => { 'numFound' => 0, 'start' => 0, 'docs' => [] } } } }
  end
  describe '#initialize' do
    it 'initializes with user_country_code' do
      service = described_class.new(user_country_code: 0)

      expect(service.instance_variable_get(:@user_country_code)).to eq(0)
    end
  end

  describe '#fetch' do
    let(:service) { described_class.new(user_country_code: 0) }

    context 'check result' do
      before do
        allow(Solr::GroupService).to receive_message_chain(:new, :group_query).and_return(solr_result)
      end
      let(:result) { service.fetch }

      it 'data length' do
        expect(result.length).to be > 0
      end

      it 'keys in data' do
        expect(result.map(&:keys).uniq.flatten).to eq(%i[maker_name model_name img_url total_count])
      end
    end

    context '#ranking_images' do
      let(:result) { service.send(:ranking_images, [54_743, 50_034, 61_161, 1257, 4483], JP_COUNTRY_ID) }

      it { expect(result.length).to be > 0 }
      it { expect(result.all? { |item| item[2] == JP_COUNTRY_ID }).to eq(true) }
    end

    it '#img_fields' do
      result = service.send(:img_fields)
      img_fields = <<-STRING
        model_images.image_id, model_images.extension, s.country_id, s.model_master_id,
        row_number() over (partition by s.country_id, s.model_master_id ORDER BY s.sales_id DESC, model_images.sort_order ASC) as rownumber
      STRING

      expect(result).to eq(img_fields)
    end

    it '#make_ranking_sql' do
      result = service.send(:make_ranking_sql, [54_743, 50_034, 61_161, 1257, 4483], 0, 1)
      model_ids_and_country = "s.model_master_id IN ('54743','50034','61161','1257','4483') AND s.country_id = '0'"

      expect(result).to include(model_ids_and_country)
    end

    it '#select_imgs_top_one_by' do
      result = service.send(:select_imgs_top_one_by, [54_743, 50_034, 61_161, 1257, 4483], 0, 1)
      model_ids_and_country = "s.model_master_id IN ('54743','50034','61161','1257','4483') AND s.country_id = '0'"

      expect(result).to include(model_ids_and_country)
    end
  end
end
