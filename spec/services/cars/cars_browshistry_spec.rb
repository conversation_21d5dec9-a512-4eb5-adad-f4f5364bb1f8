require 'rails_helper'

RSpec.describe Cars::CarsBrowshistry do
  let(:search_response) do
    [{ 'CountryID' => '392', 'IsBroken' => 0, 'dtUpdated' => '2024-01-11T19:41:58.827Z', 'ImagePath' => '', 'id' => '521311', 'IsValid' => 1,
       'Status' => 11, 'VehicleIdentificationNumber' => '231321111', 'ServicePlan' => 1, 'StockID_MP' => 0, 'Make' => 'BMW',
       'UserNumber' => '240104185729', 'Country' => 'JAPAN', 'IsPostedOption' => 0, 'VehicleWidth' => 1000, 'MakeID' => 23,
       'PriceExchangeRateID' => 1, 'PriceText' => '5000.00', 'DealerComment' => '3213123213', 'URL' => '', 'ColorID' => 26,
       'PriceOtherText' => '5000.00', 'TrimName' => '', 'VehicleHeight' => 1000, 'IsOffer' => true, 'SortNum' => '2024-01-04T18:57:29.043Z',
       'TerminateDate' => '2024-02-03T18:57:29.047Z', 'EndDate' => '2024-02-03T18:57:29.047Z', 'SteeringID' => 12,
       'dtCreated' => '2024-01-04T20:00:03.420Z', 'IsMoneyCollection' => true, 'Model' => 'MINI Park Lane', 'ModelYear' => 2022,
       'HangingCount' => 0, 'HangingUsers' => ':', 'MakeModelID' => 285_498, 'IsInternational' => 1, 'IsNoAccidentsHistory' => false,
       'DriveTypeID' => 9, 'IsBuyItNow' => false, 'OdometerOption' => 86, 'Name' => 'test_suzuki_invoice_replace_6', 'PurchaseCharge' => 2,
       'IsDomestic' => false, 'StartDate' => '2024-01-04T18:57:29.047Z', 'FinalCountries' => ',28,72,90,104,108,180,212,214,308,320,328,388',
       'CategoryID' => 1, 'ModelNumber' => 'testsuzuki', 'IsRecommendedItem' => true, 'BodyStyle1' => 1, 'BodyStyle2' => 0,
       'EditDate' => '2024-01-09T11:18:07.187Z', 'PortID' => 0, 'Odometer' => 100, 'ItemID' => 561_649, 'ServiceID' => 300,
       'SpecialPriceStatus' => 0, 'IsAccident' => 0, 'Price' => 5000.0, 'IsHanging' => false, 'CreateDateMI' => 57,
       'Detail' => '', 'ModelID' => 55_498, 'TransmissionID' => 5, 'IsNew' => 0, 'FuelTypeID' => 17, 'ModelYearMonth' => 202_203,
       'PriceRawText' => '5000.00', 'ImageCount' => 1, 'CreateDateY' => 2024, 'UserID' => 850_837, 'CreateDateM' => 1, 'CreateDateHH' => 18,
       'Body' => ' : BMW : MINI Park Lane : 2022 : testsuzuki : 2313: Bus : Diesel : -240104185729',
       'path' => '/used_car/bmw/mini%20park%20lane/561649/' }]
  end
  let(:current_currency) { 'USD' }
  let(:number_of_car_to_show) { 1 }
  let(:ids) { [561_649] }

  subject do
    described_class.new(ids.join(','), current_currency, number_of_car_to_show)
  end

  context '#initialize' do
    it 'check instance variable' do
      expect(subject.instance_variables).to eq(%i[@car_ids @current_currency @number_of_car_to_show])
    end
  end

  describe '#call' do
    context 'when car_ids is present' do
      before do
        allow(Search::Cars).to receive_message_chain(:new, :exec).and_return([search_response])
      end
      let(:result) { subject.call }

      it 'check present' do
        expect(result).to be_present
      end

      it 'returns itemIds include of id when searching in solr' do
        expect(result.map { |item| item['ItemID'] } - ids).to be_empty
      end
    end

    context 'when car_ids is empty' do
      let(:result) { described_class.new('', current_currency, number_of_car_to_show).call }

      it 'return nil' do
        expect(result).to be_nil
      end
    end

    context 'check private method' do
      let(:result) { subject.send(:search_browshistry_params) }

      it 'check equal' do
        expect(result).to eq({ queries: "ItemID:(#{subject.instance_variable_get(:@car_ids).join(' OR ')}) AND CategoryID:1" })
      end
    end
  end
end
