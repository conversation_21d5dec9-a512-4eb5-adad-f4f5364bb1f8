require 'rails_helper'

RSpec.describe Cars::RecommendCars do
  let(:params) { { make_id: '23', model_id: '426', body_id: '11', year: '2020', car_id: '561865' } }
  let(:current_currency) { 'USD' }
  let(:service) { described_class.new(params, current_currency) }
  let(:search_response) do
    [{ 'CountryID' => '392', 'IsBroken' => 0, 'dtUpdated' => '2024-01-28T07:12:19.023Z', 'CountTotal' => 18, 'CountHalfYear' => 0,
       'ImagePath' => '', 'id' => '521515', 'IsValid' => 1, 'Status' => 0, 'VehicleIdentificationNumber' => '123', 'ServicePlan' => 1,
       'StockID_MP' => 0, 'Make' => 'BMW', 'UserNumber' => '240123185117', 'Country' => 'JAPAN', 'IsPostedOption' => 0, 'VehicleWidth' => 1860,
       'MakeID' => 23, 'PriceExchangeRateID' => 1, 'PriceText' => '8000.00', 'DealerComment' => '成約課金でCarviewにお\r\n輸出業者です。',
       'URL' => '', 'OfferAverage' => 0, 'ColorID' => 27, 'PriceOtherText' => '8000.00', 'FeedbackTotal' => 4.8,
       'TrimName' => '', 'VehicleHeight' => 1470, 'FeedbackOneYear' => 0.0, 'IsOffer' => true, 'SortNum' => '2024-01-23T18:51:17.8',
       'TerminateDate' => '2024-02-27T04:01:23.427Z', 'EndDate' => '2024-02-27T04:01:23.427Z', 'SteeringID' => 12,
       'dtCreated' => '2024-01-23T18:52:02.153Z', 'IsMoneyCollection' => true, 'Model' => '5 Series', 'ModelYear' => 2020, 'HangingCount' => 0,
       'HangingUsers' => ':', 'YesCountTotal' => 14, 'MakeModelID' => 230_426, 'IsInternational' => 1, 'ManufactureDateM' => 5,
       'IsNoAccidentsHistory' => false, 'DriveTypeID' => 10, 'IsBuyItNow' => false, 'OdometerOption' => 0, 'Name' => 'Wondeful Vehicle7-7',
       'PurchaseCharge' => 2, 'IsDomestic' => false, 'StartDate' => '2024-01-23T18:51:17.877Z',
       'FinalCountries' => '659,702,716,780,800,826,834,840,894,', 'Sort' => 1, 'CategoryID' => 1, 'ModelNumber' => 'XG20',
       'IsRecommendedItem' => true, 'BodyStyle1' => 11, 'BodyStyle2' => 0, 'FeedbackHalfYear' => 0.0,
       'EditDate' => '2024-01-28T04:01:23.427Z', 'PortID' => 0, 'Odometer' => 10_000, 'ItemID' => 561_859, 'ServiceID' => 300,
       'SpecialPriceStatus' => 0, 'IsAccident' => 0, 'Price' => 8000.0, 'IsHanging' => false, 'CreateDateMI' => 51,
       'Detail' => 'Must see', 'ModelID' => 426, 'TransmissionID' => 5, 'IsNew' => 0, 'FuelTypeID' => 20, 'ModelYearMonth' => 202_005,
       'PriceRawText' => '8000.00', 'ImageCount' => 1, 'CreateDateY' => 2024, 'UserID' => 837_000, 'CreateDateM' => 1, 'CreateDateHH' => 18,
       'OfferCount' => 0, 'YesCountOneYear' => 0, 'PriceOther' => 8000.0, 'CreateDateD' => 23, 'Steering' => 'Right', 'AreaID' => 5,
       'Title' => 'Wondeful Vehicle7-7 - akm001 huga23', 'Displacement' => 3000, 'Door' => 4, 'IsNewZealand' => 0, 'VehicleLength' => 4920,
       'ManufaeY' => 2020, 'CountOneYear' => 0, 'IsDelete' => false, 'StartDate_15min_Intervals' => '2024-01-23T18:45:00Z' }]
  end

  describe '#initialize' do
    it 'initializes with params and current_currency' do
      expect(service.instance_variable_get(:@make_id)).to eq(params[:make_id])
      expect(service.instance_variable_get(:@model_id)).to eq(params[:model_id])
      expect(service.instance_variable_get(:@current_currency)).to eq(current_currency)
    end
  end

  describe '#call' do
    before do
      allow(Solr::Base).to receive_message_chain(:new, :find).and_return(search_response)
    end
    it 'calls Solr::Base with correct parameters and returns formatted results with keys' do
      allow(search_response).to receive(:dig).and_return(search_response)
      result = service.call

      expect(result.map(&:keys).flatten).to include('Country', 'MakeID', 'MakeModelID')
    end
  end

  describe 'private method' do
    let(:make_id) { service.instance_variable_get(:@make_id) }
    let(:model_id) { service.instance_variable_get(:@model_id) }
    let(:year) { service.instance_variable_get(:@year) }
    let(:body_id) { service.instance_variable_get(:@body_id) }

    context '#condition_1' do
      it 'check equal' do
        result = service.send(:condition_1)
        condition_1 = {
          aid: make_id,
          oid: model_id,
          bsty: body_id,
          fid: year,
          jid: year
        }

        expect(result).to eq(condition_1)
      end
    end

    context '#condition_2' do
      it 'check equal' do
        result = service.send(:condition_2)
        condition_2 = {
          aid: make_id,
          oid: model_id,
          fid: year,
          jid: year
        }

        expect(result).to eq(condition_2)
      end
    end

    context '#condition_3' do
      it 'check equal' do
        result = service.send(:condition_3)
        condition_3 = { bsty: body_id }

        expect(result).to eq(condition_3)
      end
    end

    context '#solr_queries' do
      it 'check includes' do
        result = service.send(:solr_queries, service.send(:condition_1))

        expect(result).to include(" AND -Price:#{ASK_PRICE} AND -ItemID:#{instance_variable_get(:@car_ids)}")
      end
    end
  end
end
