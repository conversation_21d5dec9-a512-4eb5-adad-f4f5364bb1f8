require 'rails_helper'

RSpec.describe Cars::ConditionChecking do
  let(:car) { { 'stockSalesStatus' => 11, 'isValid' => true, 'orderBuyerId' => 21 } }
  let(:user_signed_in) { true }
  let(:current_user) { { 'userId' => 11 } }
  let(:service) { described_class.new(car, user_signed_in, current_user) }

  context '#initialize' do
    it 'Success initialize' do
      expect(service).to be_present
    end

    it 'check instance variable' do
      expect(service.instance_variables).to eq(%i[@car @user_signed_in @current_user])
    end
  end

  describe '#allow_inquiry?' do
    subject { service.allow_inquiry? }

    context 'allow inquiry' do
      it { expect(subject).to eq(true) }
    end

    context 'deny inquiry' do
      let(:result) { described_class.new({ 'stockSalesStatus' => 21, 'isValid' => false, 'orderBuyerId' => 21 }, user_signed_in, current_user) }

      it { expect(result.allow_inquiry?).to eq(false) }
    end
  end

  describe 'check private method' do
    context '#ordered?' do
      let(:service) { described_class.new({ 'stockSalesStatus' => SALES_STATUS_RESERVED }, false, { 'userId' => 123 }) }

      it { expect(service.send(:ordered?)).to eq(true) }
    end
    context '#not_order_buyer?' do
      let(:service) { described_class.new({ 'orderBuyerId' => 1 }, false, { 'userId' => 123 }) }

      it { expect(service.send(:not_order_buyer?)).to eq(true) }
    end
    context '#sold_out?' do
      let(:service) { described_class.new({ 'stockSalesStatus' => SALES_STATUS_SOLD_OUT }, false, { 'userId' => 123 }) }

      it { expect(service.send(:sold_out?)).to eq(true) }
    end
  end
end
