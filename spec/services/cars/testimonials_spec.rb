require 'rails_helper'

RSpec.describe Cars::Testimonials do
  let(:country_number) { 0 }
  let(:cookies_cvpd) { 'some_cookie_value' }
  let(:service) { described_class.new(country_number, cookies_cvpd) }

  describe '#initialize' do
    it 'initializes with country_number and cookies_cvpd' do
      expect(service.instance_variable_get(:@country_number)).to eq(country_number)
      expect(service.instance_variable_get(:@cookies_cvpd)).to eq(cookies_cvpd)
    end
  end

  describe '#call' do
    context '#call is success' do
      let(:fake_response) do
        double('response', success?: true, body: { 'customervoices' => [{ 'userId' => 838_482, 'satisfaction' => 1,
                                                                          'comment' => '岡崎最高ーー', 'isDisplayPhoto' => true,
                                                                          'countryNumber' => 554, 'createdDate' => '2017-12-14T11:09:14.887',
                                                                          'updatedDate' => '2017-12-14T11:46:18.547' }] }, status: 200)
      end

      it 'keys in data' do
        expect(service.call.keys).to eq(%i[satisfaction created_date country_flag country_name comment image_user_post])
      end
    end

    context 'when the API call is failed' do
      let(:fake_response) { double('response', success?: false, body: 'response body', status: 400) }

      before do
        allow_any_instance_of(TcvCoreApi::Request).to receive(:send).and_return(fake_response)
      end

      it 'makes a failed API call' do
        expect(service.call).to be_nil
      end
    end
  end

  describe 'private method' do
    let(:customervoices) do
      {
        'userId' => 838_482, 'satisfaction' => 1,
        'comment' => '岡崎最高ーー', 'isDisplayPhoto' => true,
        'countryNumber' => 554, 'createdDate' => '2017-12-14T11:09:14.887',
        'updatedDate' => '2017-12-14T11:46:18.547'
      }
    end

    it '#testimonials_created_date' do
      result = service.send(:testimonials_created_date, customervoices['createdDate'])

      expect(result).to eq('Dec / 14 / 2017 (JST)')
    end

    it '#testimonials_country_name' do
      result = service.send(:testimonials_country_name, customervoices['countryNumber'])

      expect(result.strip).to eq('New Zealand')
    end

    it '#testimonials_comment' do
      comment = 'a' * Settings.testimonials.comment_character_limit.next
      result = service.send(:testimonials_comment, comment)

      expect(result).to eq("#{'a' * Settings.testimonials.comment_character_limit} ...")
    end

    it '#testimonials_comment the length of the string under the limit' do
      comment = 'a' * (Settings.testimonials.comment_character_limit - 1)
      result = service.send(:testimonials_comment, comment)

      expect(result).to eq('a' * (Settings.testimonials.comment_character_limit - 1))
    end

    it '#testimonials_image_user_post' do
      result = service.send(:testimonials_image_user_post, customervoices['isDisplayPhoto'], customervoices['userId'])

      expect(result).to eq('https://img.tcv-dev.com/cdn/trade/img01/uservoice/83/838482.jpg')
    end

    it '#testimonials_image_user_post none display photo' do
      result = service.send(:testimonials_image_user_post, false, customervoices['userId'])

      expect(result).to eq("#{Settings.tcv_domain}#{Settings.testimonials.image_user_post.no_image}")
    end

    it '#testimonials_data_return' do
      testimonials_data = {
        satisfaction: customervoices['satisfaction'],
        created_date: service.send(:testimonials_created_date, customervoices['createdDate']),
        country_flag: customervoices['countryNumber'],
        country_name: service.send(:testimonials_country_name, customervoices['countryNumber']),
        comment: service.send(:testimonials_comment, customervoices['comment']),
        image_user_post: service.send(:testimonials_image_user_post, customervoices['isDisplayPhoto'], customervoices['userId'])
      }
      result = service.send(:testimonials_data_return, customervoices)

      expect(result).to eq(testimonials_data)
    end
  end
end
