require 'rails_helper'

RSpec.describe Cars::MatchingByCountry do
  let(:max_items) { 1 }
  let(:current_currency) { 'USD' }
  let(:service) { described_class.new(max_items, current_currency) }
  let(:search_response) do
    [{ 'CountryID' => '392', 'IsBroken' => 0, 'dtUpdated' => '2024-01-11T19:41:58.827Z', 'ImagePath' => '', 'id' => '521311', 'IsValid' => 1,
       'Status' => 11, 'VehicleIdentificationNumber' => '231321111', 'ServicePlan' => 1, 'StockID_MP' => 0, 'Make' => 'BMW',
       'UserNumber' => '240104185729', 'Country' => 'JAPAN', 'IsPostedOption' => 0, 'VehicleWidth' => 1000, 'MakeID' => 23,
       'PriceExchangeRateID' => 1, 'PriceText' => '5000.00', 'DealerComment' => '3213123213', 'URL' => '', 'ColorID' => 26,
       'PriceOtherText' => '5000.00', 'TrimName' => '', 'VehicleHeight' => 1000, 'IsOffer' => true, 'SortNum' => '2024-01-04T18:57:29.043Z',
       'TerminateDate' => '2024-02-03T18:57:29.047Z', 'EndDate' => '2024-02-03T18:57:29.047Z', 'SteeringID' => 12,
       'dtCreated' => '2024-01-04T20:00:03.420Z', 'IsMoneyCollection' => true, 'Model' => 'MINI Park Lane', 'ModelYear' => 2022,
       'HangingCount' => 0, 'HangingUsers' => ':', 'MakeModelID' => 285_498, 'IsInternational' => 1, 'IsNoAccidentsHistory' => false,
       'DriveTypeID' => 9, 'IsBuyItNow' => false, 'OdometerOption' => 86, 'Name' => 'test_suzuki_invoice_replace_6', 'PurchaseCharge' => 2,
       'IsDomestic' => false, 'StartDate' => '2024-01-04T18:57:29.047Z', 'FinalCountries' => ',28,72,90,104,108,180,212,214,308,320,328,388',
       'CategoryID' => 1, 'ModelNumber' => 'testsuzuki', 'IsRecommendedItem' => true, 'BodyStyle1' => 1, 'BodyStyle2' => 0,
       'EditDate' => '2024-01-09T11:18:07.187Z', 'PortID' => 0, 'Odometer' => 100, 'ItemID' => 561_649, 'ServiceID' => 300,
       'SpecialPriceStatus' => 0, 'IsAccident' => 0, 'Price' => 5000.0, 'IsHanging' => false, 'CreateDateMI' => 57, 'Detail' => '',
       'ModelID' => 55_498, 'TransmissionID' => 5, 'IsNew' => 0, 'FuelTypeID' => 17, 'ModelYearMonth' => 202_203, 'PriceRawText' => '5000.00',
       'ImageCount' => 1, 'CreateDateY' => 2024, 'UserID' => 850_837, 'CreateDateM' => 1, 'CreateDateHH' => 18,
       'Body' => ' : BMW : MINI Park Lane : 2022 : testsuzuki : 2313: Bus : Diesel : -240104185729',
       'path' => '/used_car/bmw/mini%20park%20lane/561649/' }]
  end

  describe '#initialize' do
    it 'initializes with max_items and current_currency' do
      expect(service.instance_variable_get(:@max_items)).to eq(max_items)
      expect(service.instance_variable_get(:@current_currency)).to eq(current_currency)
      expect(service.instance_variables).to eq(%i[@model_year_start @model_year_end @max_items @current_currency @per_page])
    end
  end

  describe '#call' do
    before do
      allow(Search::Cars).to receive_message_chain(:new, :exec).and_return([search_response])
    end

    it 'calls Search::Cars with correct parameters and returns a sample of cars' do
      result = service.call

      expect(result.length).to eq(1)
    end
  end

  describe '#search_car_params' do
    it 'returns correct search parameters' do
      model_year_start = service.instance_variable_get(:@model_year_start)
      model_year_end = service.instance_variable_get(:@model_year_end)
      expected_params = {
        queries: "CategoryID:1 AND ModelYear:[#{model_year_start} TO #{model_year_end}]",
        sort: 'dtUpdated desc'
      }

      expect(service.send(:search_car_params)).to include(expected_params)
    end
  end
end
