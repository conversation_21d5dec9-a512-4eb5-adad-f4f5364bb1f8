require 'rails_helper'

RSpec.describe Cars::CatalogBuildUrl do
  let(:model_id) { 1 }
  let(:model_year) { 2022 }
  let(:trim_name) { 'Test Trim' }

  subject { described_class.new(model_id, model_year, trim_name) }
  describe '#initialize' do
    it 'initializes the object with the correct attributes' do
      expect(subject.instance_variable_get(:@model_id)).to eq(model_id)
      expect(subject.instance_variable_get(:@model_year)).to eq(model_year)
      expect(subject.instance_variable_get(:@trim_name)).to eq(trim_name)
    end
  end

  describe '#call' do
    let(:model_id) { 1 }
    let(:model_year) { 2022 }
    let(:trim_name) { 'Test Trim' }

    subject { described_class.new(model_id, model_year, trim_name).call }

    context 'when trim_name is present' do
      let(:result) { double('Result', make_ascii_name: 'Make', model_ascii_name: 'Model', trim_ascii_name: 'Trim', trim_id: 1) }

      before do
        allow_any_instance_of(described_class).to receive(:query_pattern_1).and_return(result)
      end

      it 'calls query_pattern_1 and returns the correct output' do
        expect(subject).to eq(url: '/specifications/make/model/trim/1/', result: result.as_json(include_root: false), model_year: model_year)
      end
    end

    context 'when trim_name is not present' do
      let(:result) { double('Result', make_ascii_name: 'Make', model_ascii_name: 'Model') }

      before do
        allow_any_instance_of(described_class).to receive(:query_pattern_2).and_return(result)
      end

      it 'calls query_pattern_2 and returns the correct output' do
        expect(subject).to eq(url: '/specifications/make/model/', result: result.as_json(include_root: false), model_year: model_year)
      end
    end

    context 'when result is not found' do
      before do
        allow_any_instance_of(described_class).to receive(:query_pattern_1).and_return(nil)
        allow_any_instance_of(described_class).to receive(:query_pattern_2).and_return(nil)
      end

      it 'returns nil' do
        expect(subject).to be_nil
      end
    end
  end

  describe 'test private method' do
    context '#url_pattern_1' do
      it 'returns the correct URL pattern' do
        result = double('Result', make_ascii_name: 'Make', model_ascii_name: 'Model', trim_ascii_name: 'Trim', trim_id: 1)
        url = subject.send(:url_pattern_1, result)

        expect(url).to eq('/specifications/make/model/trim/1/')
      end
    end

    context '#url_pattern_2' do
      it 'returns the correct URL pattern' do
        result = double('Result', make_ascii_name: 'Make', model_ascii_name: 'Model')
        url = subject.send(:url_pattern_2, result)

        expect(url).to eq('/specifications/make/model/')
      end
    end

    context '#query_pattern_1' do
      it 'performs the correct database query' do
        trim_name = 'TestTrim'
        fake_result = double('FakeResult', make_ascii_name: 'FakeMake', model_ascii_name: 'FakeModel')
        model_id = 1

        allow(ModelMaster).to receive(:where).and_return(ModelMaster)
        allow(ModelMaster).to receive(:joins).and_return(ModelMaster)
        allow(ModelMaster).to receive(:select).and_return(ModelMaster)
        allow(ModelMaster).to receive(:first).and_return(fake_result)

        result = subject.send(:query_pattern_1, trim_name, model_id)

        expect(result).to eq(fake_result)
      end
    end

    context '#query_pattern_2' do
      it 'performs the correct database query' do
        model_id = 1
        fake_result = double('FakeResult', make_ascii_name: 'FakeMake', model_ascii_name: 'FakeModel')

        allow(ModelMaster).to receive(:where).and_return(ModelMaster)
        allow(ModelMaster).to receive(:joins).and_return(ModelMaster)
        allow(ModelMaster).to receive(:select).and_return(ModelMaster)
        allow(ModelMaster).to receive(:first).and_return(fake_result)

        result = subject.send(:query_pattern_2, model_id)

        expect(result).to eq(fake_result)
      end
    end

    context '#output_return' do
      it 'returns the correct output structure' do
        url = '/fake_url/'
        result = double('Result', as_json: { key: 'value' })
        model_year = 2022

        output = subject.send(:output_return, url, result, model_year)

        expect(output).to eq(url: url, result: { key: 'value' }, model_year: model_year)
      end
    end
  end
end
