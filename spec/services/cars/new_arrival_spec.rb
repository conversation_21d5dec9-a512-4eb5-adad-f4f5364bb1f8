require 'rails_helper'

RSpec.describe Cars::NewArrival do
  describe '#initialize' do
    it 'initializes with current_currency and per_page' do
      service = Cars::NewArrival.new('USD', 10)

      expect(service.instance_variable_get(:@current_currency)).to eq('USD')
      expect(service.instance_variable_get(:@per_page)).to eq(10)
    end
  end

  describe '#call' do
    let(:service) { Cars::NewArrival.new('USD', 10) }
    let(:mocked_result) { double('SearchResult') }

    before do
      allow(Search::Cars).to receive(:new).and_return(double(exec: [mocked_result]))
    end

    it 'calls Search::Cars with correct parameters and returns first result' do
      expect(service.call).to eq(mocked_result)
      expect(Search::Cars).to have_received(:new)
        .with(hash_including(params: anything, current_currency: 'USD', per_page: 10, new_arrivals: true))
    end
  end

  describe '#search_car_params' do
    it 'returns correct search parameters' do
      service = Cars::NewArrival.new('USD', 10)
      query_param = "StartDate:[#{(Time.now.utc - 1.day).strftime(SOLR_TIME_FORMAT)} TO *] AND ServiceID:300 AND IsMoneyCollection:True
          AND IsOffer:True AND -UploadDealerID:[* TO *] AND CategoryID:1 AND -PriceOther:(99999999.99 OR 0.00) AND PriceOther:[* TO *]"
      actual_params = service.send(:search_car_params)

      expect(actual_params[:queries]).to eq(query_param)
      expect(actual_params[:field_list]).to eq('CountryID, UserID, ItemID, id, PriceExchangeRateID, PriceOther, ModelYear, Make, Model')
      expect(actual_params[:sort]).to eq('StartDate desc')
    end
  end
end
