require 'rails_helper'

RSpec.describe DataLoader::CarCounterService do
  let(:service) { described_class.new(0) }
  let(:count_price) do
    { 'response' => { 'numFound' => 306, 'start' => 0, 'docs' => [] },
      'facet_counts' => { 'facet_queries' => { 'Price:[0 TO 500]' => 6, 'Price:[500 TO 1000]' => 0, 'Price:[1000 TO 1500]' => 0,
                                               'Price:[1500 TO 2000]' => 69, 'Price:[2000 TO 2500]' => 4, 'Price:[2500 TO 5000]' => 48,
                                               'Price:[5000 TO 10000]' => 198, 'Price:[10000 TO 20000]' => 139, 'Price:[20000 TO *]' => 7 } } }
  end

  let(:make_statistic_count) do
    { 'response' => { 'numFound' => 312, 'start' => 0, 'docs' => [] }, 'facet_counts' =>
      { 'facet_queries' => {}, 'facet_fields' => {}, 'facet_ranges' => {}, 'facet_intervals' => {}, 'facet_heatmaps' => {},
        'facet_pivot' => { 'MakeID,ModelID' => [{ 'field' => 'MakeID', 'value' => 23, 'count' => 138,
                                                  'pivot' => [{ 'field' => 'ModelID', 'value' => 55_498, 'count' => 108 },
                                                              { 'field' => 'ModelID', 'value' => 426, 'count' => 24 }] },
                                                { 'field' => 'MakeID', 'value' => 1, 'count' => 88,
                                                  'pivot' => [{ 'field' => 'ModelID', 'value' => 50_021, 'count' => 67 },
                                                              { 'field' => 'ModelID', 'value' => 11, 'count' => 7 }] }] } } }
  end

  let(:body_style_data) do
    { 'response' => { 'numFound' => 318, 'start' => 0, 'docs' => [] },
      'facet_counts' => { 'facet_queries' => {}, 'facet_fields' => { 'BodyStyle1' => ['1', 188, '11', 42, '6', 21, '7', 20, '9', 12,
                                                                                      '2', 7, '5', 7, '10', 7, '3', 6, '12', 6, '8', 2] },
                          'facet_ranges' => {}, 'facet_intervals' => {}, 'facet_heatmaps' => {} } }
  end

  let(:category_solr_facet) do
    { 'response' => { 'numFound' => 318, 'start' => 0, 'docs' => [] }, 'facet_counts' =>
      { 'facet_queries' => { 'SteeringID:13' => 41, 'TransmissionID:6' => 94,
                             'FuelTypeID:17' => 125, 'DriveTypeID:10' => 69,
                             'IsAccident:0 AND IsNoAccidentsHistory:True' => 0,
                             'IsAccident:1' => 0 }, 'facet_fields' => {}, 'facet_ranges' => {},
        'facet_intervals' => {}, 'facet_heatmaps' => {} } }
  end
  let(:make_for_sp) do
    { 'response' => { 'numFound' => 319, 'start' => 0, 'docs' => [] },
      'facet_counts' => { 'facet_queries' => {}, 'facet_fields' =>
        { 'MakeID' => ['23', 137, '1', 92, '3', 17, '31', 13, '8', 5, '29', 5, '62', 5, '122',
                       5, '2', 4, '7', 4, '9', 4, '148', 4, '335', 4, '4', 3, '26', 2, '39', 2,
                       '40', 2, '70', 2, '126', 2, '5', 1, '6', 1, '37', 1, '127', 1, '136', 1,
                       '149', 1, '210', 1] }, 'facet_ranges' => {}, 'facet_intervals' => {}, 'facet_heatmaps' => {} } }
  end

  let(:make_all_facet) do
    { 'response' => { 'numFound' => 319, 'start' => 0, 'docs' => [] }, 'facet_counts' =>
      { 'facet_queries' => {},
        'facet_fields' => { 'MakeID' => ['23', 137, '1', 92, '3', 17, '31', 13, '8', 5, '29', 5, '62', 5, '122', 5, '2',
                                         4, '7', 4, '9', 4, '148', 4, '335', 4, '4', 3, '26', 2, '39', 2, '40', 2, '70', 2,
                                         '126', 2, '5', 1, '6', 1, '37', 1, '127', 1, '136', 1, '149', 1, '210', 1] },
        'facet_ranges' => {}, 'facet_intervals' => {}, 'facet_heatmaps' => {} } }
  end

  describe '#initialize' do
    let(:user_country_code) { 0 }
    subject(:service) { DataLoader::CarCounterService.new(user_country_code) }

    it 'assigns user_country_code' do
      expect(service.user_country_code).to eq(user_country_code)
    end
  end

  describe 'Test function' do
    let(:key_attribute) { %i[svg_name photo name stock url url_with_float_id sp_url] }

    context '#process_category_data' do
      subject(:category_data) { service.process_category_data }
      let(:stock_count_list) { MasterInfo::Category.all.pluck(:key_get_stock_count) }

      it 'returns an array with category data and stock' do
        allow(Solr::FacetService).to receive_message_chain(:new, :call_facet).and_return(category_solr_facet)
        expect(category_data.map { |item| item[:key_get_stock_count] }).to eq(stock_count_list)
      end
    end

    context '#process_body_style_data when is_sp is false' do
      before do
        allow(Solr::FacetService).to receive_message_chain(:new, :call_facet).and_return(body_style_data)
      end

      let(:result) { service.process_body_style_data }

      it 'length in body style data' do
        expect(result.length).to be > 0
      end

      it 'keys in body style data' do
        expect(result.map(&:keys).uniq.flatten).to eq(key_attribute)
      end
    end

    context '#process_body_style_data when is_sp is true' do
      before do
        allow(Solr::FacetService).to receive_message_chain(:new, :call_facet).and_return(body_style_data)
      end

      let(:result) { service.process_body_style_data(is_sp: true) }

      it 'length body style data for present in sp' do
        expect(result.length).to be > 0
      end

      it 'keys body style data for present in sp' do
        expect(result.map(&:keys).uniq.flatten).to eq(key_attribute)
      end
    end

    context '#process_maker_data' do
      before do
        allow(Solr::FacetService).to receive_message_chain(:new, :call_facet).and_return(make_statistic_count)
      end

      let(:key_maker_data_limit_model) { %i[name svg_name stock url sp_url top_models] }
      let(:key_maker_data) { %i[name stock top_models] }

      it 'popular_make with limit' do
        allow(ActiveRecord::Base.connection).to receive(:execute).and_return('SET SESSION group_concat_max_len = 1000000').once
        result = service.process_maker_data(:popular_make)
        expect(result.length).to be > 0
        expect(result.map(&:keys).uniq.flatten).to eq(key_maker_data_limit_model)
      end

      it 'popular_make without limit' do
        result = service.process_maker_data(:popular_make, limit_model_number: false)
        expect(result.length).to be > 0
        expect(result.map(&:keys).uniq.flatten).to eq(key_maker_data)
      end

      it 'alphabetical_make without limit' do
        result = service.process_maker_data(:alphabetical_make, limit_model_number: false)
        expect(result.length).to be > 0
        expect(result.map(&:keys).uniq.flatten).to eq(key_maker_data)
      end
    end

    context '#process_maker_data_for_sp' do
      before do
        allow(Solr::FacetService).to receive_message_chain(:new, :call_facet).and_return(make_for_sp)
      end

      let(:result) { service.process_maker_data_for_sp }

      it 'data length' do
        expect(result.length).to be > 0
      end

      it 'keys in data' do
        expect(result.map(&:keys).uniq.flatten).to eq(%i[name svg_name stock])
      end
    end

    context '#all_makes' do
      before do
        allow(Solr::FacetService).to receive_message_chain(:new, :call_facet).and_return(make_all_facet)
      end

      let(:result) { service.all_makes }

      it 'data length' do
        expect(result.length).to be > 0
      end

      it 'keys in data' do
        expect(result.map(&:keys).uniq.flatten).to eq(%i[name stock])
      end
    end

    context '#models_by_maker_id' do
      before do
        allow(Solr::FacetService).to receive_message_chain(:new, :call_facet).and_return(make_statistic_count)
      end

      let(:result) { service.models_by_maker_id(23) }

      it 'data length' do
        expect(result.length).to be > 0
      end

      it 'keys in data' do
        expect(result.map(&:keys).uniq.flatten).to eq(%i[name id stock])
      end
    end

    context '#get_models_by_make' do
      let(:make_model_data_filtered) do
        [{ name: '86', stock: 1 }, { name: 'AQUA', stock: 3 }, { name: 'RAV4', stock: 1 },
         { name: 'Noah', stock: 1 }, { name: 'Alphard G', stock: 1 }, { name: 'Prius', stock: 1 }]
      end
      let(:total_stock) { 23 }
      let(:url) { ->(type) { "used_car/toyota/#{type}/" } }
      let(:make_model_url) { 'used_car/toyota/' }
      let(:result) { service.get_models_by_make(make_model_data_filtered, total_stock, url, make_model_url) }

      it 'data length' do
        expect(result.length).to be > 0
      end

      it 'keys in data' do
        expect(result.map(&:keys).uniq.flatten).to eq(%i[name stock url])
      end
    end

    it 'returns an empty array when make_model_data is empty' do
      model_stock = []
      make_model_data = []
      popular_ranking_by_make = []

      result = service.filter_models_by_make(model_stock, make_model_data, popular_ranking_by_make)
      expect(result).to eq([])
    end

    it 'returns the filtered models based on the logic' do
      model_stock = [
        { 'field' => 'ModelID', 'value' => 19, 'count' => 1 },
        { 'field' => 'ModelID', 'value' => 4183, 'count' => 1 },
        { 'field' => 'ModelID', 'value' => 4485, 'count' => 1 },
      ]
      make_model_data = [{ country_number: 0, make_id: 1 }]

      popular_ranking_by_make = [{ id: 3496, country_number: 0, make_id: 1, model_id: 19, ranking_point: 1, media_template_path: 'NULL',
                                   media_template_prefix: 'NULL', media_template_suffix: 'NULL', id_merismus: nil, id_make: 0, id_model: 0,
                                   make_brand_id: 0, model_master_id: 0, model_year: 2009 },
                                 { id: 3505, country_number: 0, make_id: 1, model_id: 4623, ranking_point: 1, media_template_path: 'NULL',
                                   media_template_prefix: 'NULL', media_template_suffix: 'NULL', id_merismus: nil, id_make: 0, id_model: 0,
                                   make_brand_id: 0, model_master_id: 0, model_year: 2010 }]

      fake_model_data = [{ country_number: 5, stocks_with_count: 10 }, { country_number: 10, stocks_with_count: 5 }]
      allow(service).to receive(:filter_models_by_make).and_return(fake_model_data)
      result = service.filter_models_by_make(model_stock, make_model_data, popular_ranking_by_make)
      expect(result).to eq(fake_model_data)
    end

    it '#map_model_with_stock_count' do
      model_stock = [{ 'field' => 'ModelID', 'value' => 19, 'count' => 1 }, { 'field' => 'ModelID', 'value' => 4183, 'count' => 1 },
                     { 'field' => 'ModelID', 'value' => 4485, 'count' => 1 }, { 'field' => 'ModelID', 'value' => 6105, 'count' => 1 },
                     { 'field' => 'ModelID', 'value' => 50_000, 'count' => 1 }, { 'field' => 'ModelID', 'value' => 50_021, 'count' => 13 },
                     { 'field' => 'ModelID', 'value' => 54_460, 'count' => 3 }, { 'field' => 'ModelID', 'value' => 60_547, 'count' => 1 },
                     { 'field' => 'ModelID', 'value' => 61_917, 'count' => 2 }, { 'field' => 'ModelID', 'value' => 61_926, 'count' => 1 }]

      ranking_ids = [3513, 3512, 3500, 3507, 3508, 3498, 3502, 3503, 3511, 3506, 3499, 3501, 3504, 3505, 3509, 3510, 3514, 3515, 3496, 3497]
      fake_model_data = [{ country_number: 5, stocks_with_count: 10 }, { country_number: 10, stocks_with_count: 5 }]
      allow(service).to receive(:map_model_with_stock_count).and_return(fake_model_data)
      result = service.map_model_with_stock_count(model_stock, ranking_ids, TPopularRanking.limit(10))
      expect(result).to eq(fake_model_data)
    end

    context '#fetch_exterior_colors' do
      let(:result) { service.fetch_exterior_colors }

      it 'data length' do
        expect(result.length).to be > 0
      end

      it 'keys in data' do
        expect(result.flatten).to include('Red', 'Yellow', 'Black')
      end
    end

    context '#fetch_search_form_body_style' do
      let(:result) { service.fetch_search_form_body_style }

      it 'data length' do
        expect(result.length).to be > 0
      end

      it 'all model is MPrimaryBodyStyle' do
        expect(result.all? { |data| data.is_a?(MPrimaryBodyStyle) }).to be(true)
      end
    end

    context '#price_statistic_data' do
      before do
        allow(Solr::FacetService).to receive_message_chain(:new, :call_facet).and_return(count_price)
      end

      let(:result) { service.price_statistic_data }

      it 'data length' do
        expect(result.length).to be > 0
      end

      it 'check data' do
        expect(result.keys).to eq(DataLoader::CarCounterService::PRICE_RANGES)
      end
    end

    context '#get_all_models_by_make' do
      before do
        master_model = MasterModel.pluck(:vc_name_e, :id_model, :id_make)
        service.instance_variable_set(:@master_model, master_model)
      end
      let(:model_stock) do
        { 'field' => 'MakeID', 'value' => 148, 'count' => 3, 'pivot' => [{ 'field' => 'ModelID', 'value' => 50_765, 'count' => 3 }] }
      end
      let(:result) { service.get_all_models_by_make(model_stock) }

      it 'data length' do
        expect(result.length).to be > 0
      end

      it 'check keys data' do
        expect(result.map(&:keys).uniq).to eq([%i[name stock]])
      end
    end

    it '#process_price_data' do
      process_price_data = [{ range: 'Under $500', url: 'used_car/all/all/?prct=500' },
                            { range: '$500 - $1,000', url: 'used_car/all/all/?prcf=500&prct=1000' },
                            { range: '$1,000 - $1,500', url: 'used_car/all/all/?prcf=1000&prct=1500' },
                            { range: '$1,500 - $2,000', url: 'used_car/all/all/?prcf=1500&prct=2000' },
                            { range: '$2,000 - $2,500', url: 'used_car/all/all/?prcf=2000&prct=2500' },
                            { range: '$2,500 - $5,000', url: 'used_car/all/all/?prcf=2500&prct=5000' },
                            { range: '$5,000 - $10,000', url: 'used_car/all/all/?prcf=5000&prct=10000' },
                            { range: '$10,000 - $20,000', url: 'used_car/all/all/?prcf=10000&prct=20000' },
                            { range: 'Over $20,000', url: 'used_car/all/all/?prcf=20000' }]
      allow(Solr::FacetService).to receive_message_chain(:new, :call_facet).and_return(count_price)
      ranges_and_urls = service.process_price_data.map { |item| { range: item[:range], url: item[:url] } }
      expect(ranges_and_urls).to eq(process_price_data)
    end

    it '#category_statistic_data' do
      allow(Solr::FacetService).to receive_message_chain(:new, :call_facet).and_return(category_solr_facet)
      category_keys = service.category_statistic_data.keys
      category_conditions = ['SteeringID:13', 'TransmissionID:6', 'FuelTypeID:17', 'DriveTypeID:10',
                             'IsAccident:0 AND IsNoAccidentsHistory:True', 'IsAccident:1']
      expect(category_keys).to eq(category_conditions)
    end

    context '#make_statistic_data' do
      before do
        allow(Solr::FacetService).to receive_message_chain(:new, :call_facet).and_return(make_statistic_count)
      end

      let(:result) { service.make_statistic_data }

      it 'data length' do
        expect(result.length).to be > 0
      end

      it 'check keys data' do
        expect(result.map(&:keys).uniq).to eq([%w[field value count pivot]])
      end
    end

    context '#get_stock_by_make_id' do
      before do
        allow(Solr::FacetService).to receive_message_chain(:new, :call_facet).and_return(make_statistic_count)
      end

      let(:result) { service.get_stock_by_make_id(23) }

      it 'data length' do
        expect(result.length).to be > 0
      end

      it 'check keys data' do
        expect(result.keys).to eq(%w[field value count pivot])
      end
    end

    context '#body_style_statistic_data' do
      before do
        allow(Solr::FacetService).to receive_message_chain(:new, :call_facet).and_return(body_style_data)
      end

      let(:result) { service.body_style_statistic_data }

      it 'data length' do
        expect(result.length).to be > 0
      end

      it 'check sum count body style' do
        expect(result.values.sum).to be > 0
      end
    end

    it '#get_stock_by_body_style_id' do
      allow(Solr::FacetService).to receive_message_chain(:new, :call_facet).and_return(body_style_data)
      body_style_by_id = service.get_stock_by_body_style_id('1')
      expect(body_style_by_id).to be > 0
    end

    it '#get_style_photo all photo' do
      all_photo = [6, 7, 9, 10, 8, 3].map { |int| service.get_style_photo(int) }
      photo_data = ['media/images/bodystyle/bodystyle_sedan.png', 'media/images/bodystyle/bodystyle_suv.png',
                    'media/images/bodystyle/bodystyle_van.png', 'media/images/bodystyle/bodystyle_wagon.png',
                    'media/images/bodystyle/bodystyle_truck.png', 'media/images/bodystyle/bodystyle_hatchback.png']
      expect(all_photo).to eq(photo_data)
    end

    context '#fetch_popular_makes' do
      let(:result) { service.fetch_popular_makes }

      it 'check data length' do
        expect(result.length).to be > 0
      end

      it 'all model is MasterMake' do
        expect(result.all? { |data| data.is_a?(MasterMake) }).to be(true)
      end
    end

    context '#fetch_makes_alphabetical' do
      let(:result) { service.fetch_makes_alphabetical }

      it 'check data length' do
        expect(result.length).to be > 0
      end

      it 'all model is MasterMake' do
        expect(result.all? { |data| data.is_a?(MasterMake) }).to be(true)
      end
    end

    context '#popular_make' do
      let(:result) { service.send(:popular_make) }
      let(:master_make_data) { service.fetch_popular_makes }

      it 'keys in data' do
        expect(result.keys).to eq(%i[master_make_ids master_make_data id name])
      end

      it 'check result master_make_ids' do
        expect(result[:master_make_ids]).to eq([1, 2, 3, 4, 31, 23, 5, 6, 29, 7, 62, 9, 26, 16, 8, 122, 56, 47])
      end

      it 'check result master_make_data' do
        expect(result[:master_make_data].length).to be > 0
      end
    end

    context '#alphabetical_make' do
      let(:result) { service.send(:alphabetical_make) }

      it 'keys in data' do
        expect(result.keys).to eq(%i[master_make_ids master_make_data id name])
      end

      it 'check result master_make_ids' do
        expect(result[:master_make_ids].length).to be > 0
      end

      it 'check result master_make_data' do
        expect(result[:master_make_data].length).to be > 0
      end
    end
  end
end
